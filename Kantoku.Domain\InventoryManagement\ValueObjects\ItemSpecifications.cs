using Kantoku.SharedKernel;

namespace Kantoku.Domain.InventoryManagement.ValueObjects;

/// <summary>
/// Value object representing item specifications and technical details
/// </summary>
public class ItemSpecifications : ValueObject
{
    public string? Model { get; private set; }
    public string? Brand { get; private set; }
    public string? Color { get; private set; }
    public string? Size { get; private set; }
    public string? Material { get; private set; }
    public decimal? Weight { get; private set; }
    public string? WeightUnit { get; private set; }
    public string? Dimensions { get; private set; }
    public Dictionary<string, string> CustomProperties { get; private set; } = new();

    private ItemSpecifications() { } // For EF Core

    public ItemSpecifications(
        string? model = null,
        string? brand = null,
        string? color = null,
        string? size = null,
        string? material = null,
        decimal? weight = null,
        string? weightUnit = null,
        string? dimensions = null,
        Dictionary<string, string>? customProperties = null)
    {
        Model = model?.Trim();
        Brand = brand?.Trim();
        Color = color?.Trim();
        Size = size?.Trim();
        Material = material?.Trim();
        SetWeight(weight, weightUnit);
        Dimensions = dimensions?.Trim();
        CustomProperties = customProperties ?? new Dictionary<string, string>();

        Validate();
    }

    /// <summary>
    /// Creates basic specifications with model and brand
    /// </summary>
    public static ItemSpecifications CreateBasic(string? model = null, string? brand = null)
    {
        return new ItemSpecifications(model, brand);
    }

    /// <summary>
    /// Creates specifications with physical properties
    /// </summary>
    public static ItemSpecifications CreatePhysical(
        string? color = null,
        string? size = null,
        string? material = null,
        decimal? weight = null,
        string? weightUnit = null,
        string? dimensions = null)
    {
        return new ItemSpecifications(null, null, color, size, material, weight, weightUnit, dimensions);
    }

    /// <summary>
    /// Adds a custom property
    /// </summary>
    public ItemSpecifications AddCustomProperty(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Property key cannot be null or empty", nameof(key));

        var newProperties = new Dictionary<string, string>(CustomProperties)
        {
            [key.Trim()] = value?.Trim() ?? string.Empty
        };

        return new ItemSpecifications(Model, Brand, Color, Size, Material, Weight, WeightUnit, Dimensions, newProperties);
    }

    /// <summary>
    /// Removes a custom property
    /// </summary>
    public ItemSpecifications RemoveCustomProperty(string key)
    {
        var newProperties = new Dictionary<string, string>(CustomProperties);
        newProperties.Remove(key);

        return new ItemSpecifications(Model, Brand, Color, Size, Material, Weight, WeightUnit, Dimensions, newProperties);
    }

    /// <summary>
    /// Gets a custom property value
    /// </summary>
    public string? GetCustomProperty(string key)
    {
        return CustomProperties.TryGetValue(key, out var value) ? value : null;
    }

    /// <summary>
    /// Checks if a custom property exists
    /// </summary>
    public bool HasCustomProperty(string key)
    {
        return CustomProperties.ContainsKey(key);
    }

    /// <summary>
    /// Gets all custom property keys
    /// </summary>
    public IEnumerable<string> GetCustomPropertyKeys()
    {
        return CustomProperties.Keys;
    }

    /// <summary>
    /// Updates the model
    /// </summary>
    public ItemSpecifications WithModel(string? model)
    {
        return new ItemSpecifications(model, Brand, Color, Size, Material, Weight, WeightUnit, Dimensions, CustomProperties);
    }

    /// <summary>
    /// Updates the brand
    /// </summary>
    public ItemSpecifications WithBrand(string? brand)
    {
        return new ItemSpecifications(Model, brand, Color, Size, Material, Weight, WeightUnit, Dimensions, CustomProperties);
    }

    /// <summary>
    /// Updates the color
    /// </summary>
    public ItemSpecifications WithColor(string? color)
    {
        return new ItemSpecifications(Model, Brand, color, Size, Material, Weight, WeightUnit, Dimensions, CustomProperties);
    }

    /// <summary>
    /// Updates the size
    /// </summary>
    public ItemSpecifications WithSize(string? size)
    {
        return new ItemSpecifications(Model, Brand, Color, size, Material, Weight, WeightUnit, Dimensions, CustomProperties);
    }

    /// <summary>
    /// Updates the material
    /// </summary>
    public ItemSpecifications WithMaterial(string? material)
    {
        return new ItemSpecifications(Model, Brand, Color, Size, material, Weight, WeightUnit, Dimensions, CustomProperties);
    }

    /// <summary>
    /// Updates the weight
    /// </summary>
    public ItemSpecifications WithWeight(decimal? weight, string? weightUnit = null)
    {
        return new ItemSpecifications(Model, Brand, Color, Size, Material, weight, weightUnit ?? WeightUnit, Dimensions, CustomProperties);
    }

    /// <summary>
    /// Updates the dimensions
    /// </summary>
    public ItemSpecifications WithDimensions(string? dimensions)
    {
        return new ItemSpecifications(Model, Brand, Color, Size, Material, Weight, WeightUnit, dimensions, CustomProperties);
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Model;
        yield return Brand;
        yield return Color;
        yield return Size;
        yield return Material;
        yield return Weight;
        yield return WeightUnit;
        yield return Dimensions;
        
        foreach (var kvp in CustomProperties.OrderBy(p => p.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }

    private void SetWeight(decimal? weight, string? weightUnit)
    {
        if (weight.HasValue)
        {
            if (weight.Value < 0)
                throw new ArgumentException("Weight cannot be negative");

            Weight = weight.Value;
            WeightUnit = weightUnit?.Trim() ?? "kg";
        }
        else
        {
            Weight = null;
            WeightUnit = null;
        }
    }

    private void Validate()
    {
        if (!string.IsNullOrEmpty(Model) && Model.Length > 100)
            throw new ArgumentException("Model cannot exceed 100 characters");

        if (!string.IsNullOrEmpty(Brand) && Brand.Length > 100)
            throw new ArgumentException("Brand cannot exceed 100 characters");

        if (!string.IsNullOrEmpty(Color) && Color.Length > 50)
            throw new ArgumentException("Color cannot exceed 50 characters");

        if (!string.IsNullOrEmpty(Size) && Size.Length > 50)
            throw new ArgumentException("Size cannot exceed 50 characters");

        if (!string.IsNullOrEmpty(Material) && Material.Length > 100)
            throw new ArgumentException("Material cannot exceed 100 characters");

        if (!string.IsNullOrEmpty(WeightUnit) && WeightUnit.Length > 10)
            throw new ArgumentException("Weight unit cannot exceed 10 characters");

        if (!string.IsNullOrEmpty(Dimensions) && Dimensions.Length > 200)
            throw new ArgumentException("Dimensions cannot exceed 200 characters");

        if (CustomProperties.Count > 20)
            throw new ArgumentException("Cannot have more than 20 custom properties");

        foreach (var kvp in CustomProperties)
        {
            if (string.IsNullOrWhiteSpace(kvp.Key))
                throw new ArgumentException("Custom property key cannot be null or empty");

            if (kvp.Key.Length > 50)
                throw new ArgumentException("Custom property key cannot exceed 50 characters");

            if (kvp.Value?.Length > 200)
                throw new ArgumentException("Custom property value cannot exceed 200 characters");
        }
    }

    public override string ToString()
    {
        var parts = new List<string>();

        if (!string.IsNullOrEmpty(Brand))
            parts.Add($"Brand: {Brand}");

        if (!string.IsNullOrEmpty(Model))
            parts.Add($"Model: {Model}");

        if (!string.IsNullOrEmpty(Color))
            parts.Add($"Color: {Color}");

        if (!string.IsNullOrEmpty(Size))
            parts.Add($"Size: {Size}");

        if (!string.IsNullOrEmpty(Material))
            parts.Add($"Material: {Material}");

        if (Weight.HasValue)
            parts.Add($"Weight: {Weight} {WeightUnit}");

        if (!string.IsNullOrEmpty(Dimensions))
            parts.Add($"Dimensions: {Dimensions}");

        return string.Join(", ", parts);
    }
}
