using Kantoku.SharedKernel;

namespace Kantoku.Domain.ProjectManagement.ValueObjects;

/// <summary>
/// Value object representing workload summary information
/// </summary>
public class WorkloadSummary : ValueObject
{
    public int WorkerCount { get; private set; }
    public decimal TotalHours { get; private set; }
    public decimal RegularHours { get; private set; }
    public decimal OvertimeHours { get; private set; }
    public string? Notes { get; private set; }

    private WorkloadSummary() { } // For EF Core

    public WorkloadSummary(
        int workerCount,
        decimal totalHours,
        decimal regularHours = 0,
        decimal overtimeHours = 0,
        string? notes = null)
    {
        SetWorkerCount(workerCount);
        SetHours(totalHours, regularHours, overtimeHours);
        Notes = notes;
    }

    /// <summary>
    /// Creates a workload summary with total hours only
    /// </summary>
    public static WorkloadSummary Create(int workerCount, decimal totalHours, string? notes = null)
    {
        return new WorkloadSummary(workerCount, totalHours, totalHours, 0, notes);
    }

    /// <summary>
    /// Creates a workload summary with regular and overtime hours
    /// </summary>
    public static WorkloadSummary CreateDetailed(
        int workerCount,
        decimal regularHours,
        decimal overtimeHours,
        string? notes = null)
    {
        var totalHours = regularHours + overtimeHours;
        return new WorkloadSummary(workerCount, totalHours, regularHours, overtimeHours, notes);
    }

    /// <summary>
    /// Gets the average hours per worker
    /// </summary>
    public decimal AverageHoursPerWorker
    {
        get
        {
            if (WorkerCount == 0)
                return 0;
            
            return TotalHours / WorkerCount;
        }
    }

    /// <summary>
    /// Gets the overtime percentage
    /// </summary>
    public decimal OvertimePercentage
    {
        get
        {
            if (TotalHours == 0)
                return 0;
            
            return (OvertimeHours / TotalHours) * 100;
        }
    }

    /// <summary>
    /// Checks if there is overtime work
    /// </summary>
    public bool HasOvertime => OvertimeHours > 0;

    /// <summary>
    /// Checks if the workload is high (more than 10 hours per worker on average)
    /// </summary>
    public bool IsHighWorkload => AverageHoursPerWorker > 10;

    /// <summary>
    /// Adds another workload summary to this one
    /// </summary>
    public WorkloadSummary Add(WorkloadSummary other)
    {
        if (other == null)
            return this;

        return new WorkloadSummary(
            WorkerCount + other.WorkerCount,
            TotalHours + other.TotalHours,
            RegularHours + other.RegularHours,
            OvertimeHours + other.OvertimeHours,
            CombineNotes(Notes, other.Notes));
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return WorkerCount;
        yield return TotalHours;
        yield return RegularHours;
        yield return OvertimeHours;
        yield return Notes;
    }

    private void SetWorkerCount(int workerCount)
    {
        if (workerCount < 0)
            throw new ArgumentException("Worker count cannot be negative", nameof(workerCount));

        if (workerCount > 1000)
            throw new ArgumentException("Worker count cannot exceed 1000", nameof(workerCount));

        WorkerCount = workerCount;
    }

    private void SetHours(decimal totalHours, decimal regularHours, decimal overtimeHours)
    {
        if (totalHours < 0)
            throw new ArgumentException("Total hours cannot be negative", nameof(totalHours));

        if (regularHours < 0)
            throw new ArgumentException("Regular hours cannot be negative", nameof(regularHours));

        if (overtimeHours < 0)
            throw new ArgumentException("Overtime hours cannot be negative", nameof(overtimeHours));

        // If regular and overtime are specified, they should add up to total
        if (regularHours > 0 || overtimeHours > 0)
        {
            var calculatedTotal = regularHours + overtimeHours;
            if (Math.Abs(calculatedTotal - totalHours) > 0.01m)
                throw new ArgumentException("Regular hours plus overtime hours must equal total hours");
        }

        TotalHours = totalHours;
        RegularHours = regularHours > 0 ? regularHours : totalHours;
        OvertimeHours = overtimeHours;
    }

    private static string? CombineNotes(string? notes1, string? notes2)
    {
        if (string.IsNullOrWhiteSpace(notes1) && string.IsNullOrWhiteSpace(notes2))
            return null;

        if (string.IsNullOrWhiteSpace(notes1))
            return notes2;

        if (string.IsNullOrWhiteSpace(notes2))
            return notes1;

        return $"{notes1}; {notes2}";
    }

    public override string ToString()
    {
        var result = $"{WorkerCount} workers, {TotalHours:F1} hours";
        
        if (HasOvertime)
            result += $" ({RegularHours:F1} regular + {OvertimeHours:F1} overtime)";
        
        if (!string.IsNullOrWhiteSpace(Notes))
            result += $" - {Notes}";
        
        return result;
    }
}
