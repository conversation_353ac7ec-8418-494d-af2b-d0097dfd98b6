using Kantoku.SharedKernel;

namespace Kantoku.Domain.RequestManagement.ValueObjects;

/// <summary>
/// Value object representing request dates
/// </summary>
public class RequestDates : ValueObject
{
    public DateTime? StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public DateTime? DueDate { get; private set; }
    public DateTime? PreferredDate { get; private set; }

    private RequestDates() { } // For EF Core

    public RequestDates(
        DateTime? startDate = null,
        DateTime? endDate = null,
        DateTime? dueDate = null,
        DateTime? preferredDate = null)
    {
        ValidateDates(startDate, endDate, dueDate, preferredDate);
        
        StartDate = startDate;
        EndDate = endDate;
        DueDate = dueDate;
        PreferredDate = preferredDate;
    }

    /// <summary>
    /// Creates request dates for a single date request
    /// </summary>
    public static RequestDates CreateSingleDate(DateTime date, DateTime? dueDate = null)
    {
        return new RequestDates(date, date, dueDate, date);
    }

    /// <summary>
    /// Creates request dates for a date range request
    /// </summary>
    public static RequestDates CreateDateRange(DateTime startDate, DateTime endDate, DateTime? dueDate = null)
    {
        return new RequestDates(startDate, endDate, dueDate);
    }

    /// <summary>
    /// Creates request dates with only a due date
    /// </summary>
    public static RequestDates CreateWithDueDate(DateTime dueDate)
    {
        return new RequestDates(null, null, dueDate);
    }

    /// <summary>
    /// Creates request dates with a preferred date
    /// </summary>
    public static RequestDates CreateWithPreferredDate(DateTime preferredDate, DateTime? dueDate = null)
    {
        return new RequestDates(null, null, dueDate, preferredDate);
    }

    /// <summary>
    /// Gets the duration in days (if start and end dates are specified)
    /// </summary>
    public int? DurationDays
    {
        get
        {
            if (StartDate.HasValue && EndDate.HasValue)
                return (EndDate.Value.Date - StartDate.Value.Date).Days + 1;
            return null;
        }
    }

    /// <summary>
    /// Checks if this is a single day request
    /// </summary>
    public bool IsSingleDay => StartDate.HasValue && EndDate.HasValue && StartDate.Value.Date == EndDate.Value.Date;

    /// <summary>
    /// Checks if this is a date range request
    /// </summary>
    public bool IsDateRange => StartDate.HasValue && EndDate.HasValue && StartDate.Value.Date != EndDate.Value.Date;

    /// <summary>
    /// Checks if the request is overdue
    /// </summary>
    public bool IsOverdue => DueDate.HasValue && DueDate.Value.Date < DateTime.Today;

    /// <summary>
    /// Checks if the request is due soon (within 3 days)
    /// </summary>
    public bool IsDueSoon => DueDate.HasValue && DueDate.Value.Date <= DateTime.Today.AddDays(3) && !IsOverdue;

    /// <summary>
    /// Gets the number of days until due date
    /// </summary>
    public int? DaysUntilDue
    {
        get
        {
            if (!DueDate.HasValue)
                return null;
            
            return (DueDate.Value.Date - DateTime.Today).Days;
        }
    }

    /// <summary>
    /// Checks if the request dates overlap with another date range
    /// </summary>
    public bool OverlapsWith(DateTime otherStart, DateTime otherEnd)
    {
        if (!StartDate.HasValue || !EndDate.HasValue)
            return false;

        return StartDate.Value.Date <= otherEnd.Date && EndDate.Value.Date >= otherStart.Date;
    }

    /// <summary>
    /// Checks if the request dates overlap with another RequestDates
    /// </summary>
    public bool OverlapsWith(RequestDates other)
    {
        if (other == null || !other.StartDate.HasValue || !other.EndDate.HasValue)
            return false;

        return OverlapsWith(other.StartDate.Value, other.EndDate.Value);
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return StartDate;
        yield return EndDate;
        yield return DueDate;
        yield return PreferredDate;
    }

    private static void ValidateDates(
        DateTime? startDate,
        DateTime? endDate,
        DateTime? dueDate,
        DateTime? preferredDate)
    {
        // Validate start and end dates
        if (startDate.HasValue && endDate.HasValue)
        {
            if (startDate.Value > endDate.Value)
                throw new ArgumentException("Start date cannot be after end date");
        }

        // Validate due date is not in the past (allow today)
        if (dueDate.HasValue && dueDate.Value.Date < DateTime.Today)
            throw new ArgumentException("Due date cannot be in the past");

        // Validate preferred date
        if (preferredDate.HasValue)
        {
            if (startDate.HasValue && preferredDate.Value.Date < startDate.Value.Date)
                throw new ArgumentException("Preferred date cannot be before start date");
            
            if (endDate.HasValue && preferredDate.Value.Date > endDate.Value.Date)
                throw new ArgumentException("Preferred date cannot be after end date");
        }

        // Validate due date against start date
        if (dueDate.HasValue && startDate.HasValue && dueDate.Value.Date < startDate.Value.Date)
            throw new ArgumentException("Due date cannot be before start date");
    }

    public override string ToString()
    {
        var parts = new List<string>();

        if (StartDate.HasValue && EndDate.HasValue)
        {
            if (IsSingleDay)
                parts.Add($"Date: {StartDate.Value:yyyy-MM-dd}");
            else
                parts.Add($"Period: {StartDate.Value:yyyy-MM-dd} to {EndDate.Value:yyyy-MM-dd}");
        }
        else if (PreferredDate.HasValue)
        {
            parts.Add($"Preferred: {PreferredDate.Value:yyyy-MM-dd}");
        }

        if (DueDate.HasValue)
            parts.Add($"Due: {DueDate.Value:yyyy-MM-dd}");

        return string.Join(", ", parts);
    }
}
