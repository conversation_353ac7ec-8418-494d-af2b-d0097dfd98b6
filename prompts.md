# Prompts for AI Agent: Monolith to Clean Architecture Transformation

## 0. Goals & Motivations

*   **Prompt:** "Based on the existing KantokuService codebase and the stated goals (improved maintainability, testability, scalability, business agility, separation of concerns), analyze how adopting Clean Architecture, DDD, and an Event-Driven Approach will specifically address these goals. Provide concrete examples from a hypothetical refactoring of a feature (e.g., 'Project Creation') to illustrate the benefits."

## 1. Core Architectural Principles

*   **Prompt (Clean Architecture):** "Explain the Dependency Rule in Clean Architecture. For the proposed layers (Domain, Application, Infrastructure, Presentation/API), detail the responsibilities of each layer in an ASP.NET Core context. Provide C# examples of how dependencies should flow (e.g., Application using Domain interfaces, Infrastructure implementing those interfaces)."
*   **Prompt (DDD):** "Define Ubiquitous Language, Bounded Contexts, Aggregates, Entities, Value Objects, Repositories, Domain Services, and Domain Events. For each concept, provide a C# code snippet example within a hypothetical 'ProjectManagement' bounded context in KantokuService."
*   **Prompt (EDA):** "Describe how an Event-Driven Architecture (EDA) facilitates decoupling and asynchronous processing. Provide a conceptual example of an event (e.g., `ProjectCreatedEvent`) being published from one part of KantokuService and consumed by another, potentially in `Kantoku.Processor`."

## 2. Proposed Target Project Structure

*   **Prompt:** "Generate the C# project file structures (`.csproj`) for `Kantoku.Domain`, `Kantoku.Application`, `Kantoku.Infrastructure`, `Kantoku.Api` (refactored), and an optional `Kantoku.SharedKernel`. Include placeholder folders for key components within each project (e.g., Entities, Aggregates in Domain; Commands, Queries in Application; Repositories, DbContext in Infrastructure). Specify target frameworks (net8.0) and essential NuGet package references for each project (e.g., MediatR for Application, EF Core for Infrastructure)."

## 3. Phased Refactoring Approach

### Phase 0: Preparation & Foundation

*   **Prompt (P0.1 Deep Dive & Analysis):** "Given the current file structure of `Kantoku.Api` and `Kantoku.Processor`, analyze the existing codebase to identify potential core business domains (e.g., UserManagement, ProjectManagement, Costing, Scheduling) and subdomains. Outline a strategy to map data flows and critical business logic. Suggest tools or techniques (e.g., static analysis, code visualization, team discussions) for this analysis."
*   **Prompt (P0.2 Team Upskilling & Alignment):** "Develop a concise training outline for a development team on Clean Architecture, DDD, and EDA. Recommend key resources (articles, books, courses). Propose a checklist for establishing coding standards and naming conventions, referencing the provided `asp-dotnet` rules."
*   **Prompt (P0.3 Define Initial Bounded Contexts):** "Based on a hypothetical analysis of `Kantoku.Api` and `Kantoku.Processor`, propose 2-3 initial Bounded Contexts (e.g., `ProjectManagementContext`, `UserAccountContext`, `NotificationContext`). For each, list its primary responsibilities and potential key aggregates. Explain how `Kantoku.Processor` might fit into this model (e.g., as a dedicated background processing context or a host for workers from multiple contexts)."
*   **Prompt (P0.4 Set Up New Project Structure):** "Generate the necessary `dotnet new` CLI commands to create the solution (`.sln`) and project (`.csproj`) files for `Kantoku.Domain`, `Kantoku.Application`, `Kantoku.Infrastructure`, `Kantoku.Api_New` (representing the refactored API), and `Kantoku.SharedKernel`. Ensure correct project references are set up according to Clean Architecture (e.g., Application references Domain, Infrastructure references Application)."
*   **Prompt (P0.5 Introduce Core Abstractions):** "Generate C# code for base classes/interfaces: `Entity<TId>`, `AggregateRoot<TId>`, `ValueObject`, `IDomainEvent`, `IRepository<T>` (generic repository interface). Place these in the appropriate project (`Kantoku.Domain` or `Kantoku.SharedKernel`). Also, generate C# interfaces for `ICommand`, `IQuery`, `ICommandHandler<TCommand>`, `IQueryHandler<TQuery, TResult>`, `IUnitOfWork`, `IEventBusPublisher`, `IEventBusSubscriber`. Place these in `Kantoku.Application`."
*   **Prompt (P0.6 Basic Event Bus Setup):** "Provide a C# implementation example of an in-process event bus for domain events using MediatR. Show how an `IDomainEvent` would be published after an aggregate saves and how a handler would subscribe to it. Discuss the considerations for choosing a robust out-of-process event bus (RabbitMQ, Kafka, Azure Service Bus) for later integration events."
*   **Prompt (P0.7 Logging & Error Handling):** "Provide a C# example of setting up Serilog for structured logging in an ASP.NET Core application (`Kantoku.Api_New`). Demonstrate how to implement a global exception handling middleware that catches unhandled exceptions, logs them, and returns a standardized JSON error response (e.g., including a trace ID, error code, and message)."

### Phase 1: First Vertical Slice

*   **Prompt (P1.1 Select a Candidate Slice):** "Assume we need to select the first vertical slice for refactoring. From the `Kantoku.Api` DTOs and Controllers, identify a suitable candidate feature (e.g., managing 'Categories' or 'PaymentTypes'). Justify why this feature is a good starting point (well-understood, low-risk, sufficiently complex)."
*   **Prompt (P1.2 Domain Modeling for the Slice - e.g., Category Management):** "For the selected 'Category Management' slice: define the `Category` aggregate root (with properties like Id, Name, Description, IsActive), any relevant value objects, and the `ICategoryRepository` interface in `Kantoku.Domain`. Implement basic domain logic (e.g., a method to deactivate a category) and define a `CategoryCreatedEvent` and `CategoryDeactivatedEvent`."
*   **Prompt (P1.3 Application Layer for the Slice - Category Management):** "For 'Category Management': create C# Commands (`CreateCategoryCommand`, `UpdateCategoryCommand`, `DeactivateCategoryCommand`) and Queries (`GetCategoryByIdQuery`, `GetAllCategoriesQuery`) in `Kantoku.Application`. Implement their corresponding handlers, using the `ICategoryRepository` and dispatching domain events via MediatR. Define request/response DTOs (e.g., `CategoryDto`, `CreateCategoryRequest`)."
*   **Prompt (P1.4 Infrastructure Implementation for the Slice - Category Management):** "Implement `CategoryRepository` in `Kantoku.Infrastructure` using Entity Framework Core, including its configuration in a `KantokuDbContext`. Show how the `IUnitOfWork` would be implemented (typically wrapping `DbContext.SaveChangesAsync()`)."
*   **Prompt (P1.5 Presentation/API Layer Integration - Category Management):** "Create an ASP.NET Core `CategoriesController` in `Kantoku.Api_New` that uses the new application services (Commands/Queries via MediatR) for 'Category Management'. Ensure it uses the new DTOs and follows RESTful principles."
*   **Prompt (P1.6 Event Handling (In-Process) - Category Management):** "Create C# examples of event handlers in `Kantoku.Application` for `CategoryCreatedEvent` and `CategoryDeactivatedEvent`. These handlers could perform actions like logging, sending a notification (conceptually), or updating a cache/read model (conceptually)."
*   **Prompt (P1.7 Testing - Category Management):** "Outline a test plan for the 'Category Management' slice. Provide C# xUnit examples for: a domain logic unit test (e.g., `Category.Deactivate()`), an application service handler unit test (e.g., `DeactivateCategoryCommandHandler` mocking the repository), and an integration test for the `CategoriesController` endpoint using `WebApplicationFactory`."
*   **Prompt (P1.8 "Strangling" the Old Code):** "Describe the steps to safely 'strangle' the old 'Category Management' implementation in the existing `Kantoku.Api`. This includes routing API calls to the new `CategoriesController`, monitoring, and eventual cleanup of old code. Suggest strategies for feature flagging or A/B testing the new implementation if desired."

### Phase 2: Iterative Refactoring & Expansion

*   **Prompt (P2.1 Repeat Phase 1):** "Select another feature from `Kantoku.Api` (e.g., 'Construction Management' based on `ConstructionController` and related DTOs). Briefly outline the DDD entities/aggregates, Application services (commands/queries), and Infrastructure components that would need to be created, mirroring the process from Phase 1."
*   **Prompt (P2.2 Refine Bounded Contexts):** "After refactoring 'Category Management' and 'Construction Management', discuss how these might influence the definition of Bounded Contexts. Would they belong to the same or different contexts? Justify the reasoning based on business cohesion and potential for independent evolution. How would `Kantoku.Processor` be involved if, for example, 'Construction' updates trigger long-running background tasks?"
*   **Prompt (P2.3 Introduce Integration Events):** "Imagine the 'UserAccountContext' needs to notify the 'ProjectManagementContext' when a user's role changes, as this might affect project permissions. Define a `UserRoleChangedIntegrationEvent` (C# class). Show how it would be published from `UserAccountContext` (e.g., via `IEventBusPublisher` after a local domain event is handled) and how `ProjectManagementContext` would subscribe and handle it, potentially updating its local representation of project collaborators. Choose RabbitMQ as the message broker and briefly describe its setup conceptually."
*   **Prompt (P2.4 Database Decoupling):** "Discuss the pros and cons of `ProjectManagementContext` having its own dedicated database versus sharing the main monolith database. Outline a high-level strategy for migrating `Project` related tables to a new schema or database if decoupling is chosen, including data synchronization considerations during the transition."
*   **Prompt (P2.5 Address Cross-Cutting Concerns):** "Review the existing `Kantoku.Api` for how authentication/authorization (likely JWT based on typical ASP.NET setups) and validation are handled. Propose how these would be integrated into the new Clean Architecture structure, specifically within the `Kantoku.Api_New` (Presentation) and `Kantoku.Application` layers (e.g., using ASP.NET Core policies, FluentValidation for DTOs)."
*   **Prompt (P2.6 Continuous Testing and CI/CD):** "Outline a CI/CD pipeline structure (e.g., using GitHub Actions or Jenkins as per `Jenkinsfile`) for the evolving multi-project solution. Include stages for build, unit tests, integration tests, and deployment. How would the pipeline handle the deployment of both the new architecture components and the remaining monolith parts during the transition?"

### Phase 3: Processor Refactoring & Potential Service Extraction

*   **Prompt (P3.1 Deep Dive into `Kantoku.Processor`):** "Analyze the purpose of `Kantoku.Processor` based on its project structure (Jobs, Services). If it handles diverse background tasks (e.g., report generation, email sending, data synchronization), propose how its internal logic could be refactored using Clean Architecture and DDD. Could its jobs be triggered by consuming integration events from other contexts? Provide a C# example of a refactored job within this new structure."
*   **Prompt (P3.2 Service Extraction):** "If the 'ProjectManagementContext' becomes highly complex and requires independent scaling, outline the technical steps and considerations for extracting it into a separate microservice. This should include API gateway changes, inter-service communication (via the established event bus), data ownership, and deployment strategies."

### Phase 4: Optimization & Refinement

*   **Prompt (P4.1 Performance Tuning):** "Identify 2-3 potential performance bottlenecks in an event-driven, DDD-based system (e.g., N+1 query problems in repositories, slow event handlers, message bus latency). For each, suggest C# code examples or EF Core configurations for optimization (e.g., using `Include`/`ThenInclude`, batch processing, optimizing event handler logic)."
*   **Prompt (P4.2 Architectural Consistency):** "Propose a checklist or a set of code review guidelines to maintain architectural consistency across the development team as they work on different bounded contexts and layers. How can static analysis tools or custom Roslyn analyzers help enforce these rules?"
*   **Prompt (P4.3 Documentation):** "List key architectural documents that should be created and maintained throughout this transformation (e.g., C4 model diagrams, Bounded Context Map, API documentation using Swagger, Event Catalog). For one of these (e.g., Bounded Context Map), describe its purpose and what information it should contain."
*   **Prompt (P4.4 Monitoring & Observability):** "Suggest a monitoring and observability stack for the refactored system (e.g., using Prometheus, Grafana, OpenTelemetry for distributed tracing). Explain how you would monitor the health of event flows, detect failures in event handlers, and trace requests across potential microservices."

## 4. Key DDD Concepts in Practice

*   **Prompt (Ubiquitous Language):** "Describe the process of establishing a Ubiquitous Language for a new Bounded Context (e.g., `InventoryManagement`). How would developers and domain experts collaborate? Provide examples of terms that might be part of this language."
*   **Prompt (Identifying Aggregates):** "Given a list of entities for an e-commerce `OrderManagement` context (Order, OrderItem, Product, Customer, Shipment), guide the process of identifying aggregates. What questions should be asked to determine aggregate roots and boundaries? Propose 1-2 aggregates from this list and justify your choice."
*   **Prompt (Repositories):** "Provide a C# implementation for `IRepository<Order>` and its EF Core backed `OrderRepository` for the `Order` aggregate. Show how the repository method (e.g., `GetByIdAsync`) would ensure it returns the full `Order` aggregate, including its `OrderItems`, respecting aggregate boundaries."
*   **Prompt (Domain Events):** "For an `Order` aggregate, define an `OrderShippedEvent` (C# class). Show how this event would be added to the aggregate's domain events collection when its `Ship()` method is called. Then, show a MediatR handler in the Application layer that reacts to this event, perhaps by notifying the customer."
*   **Prompt (Entities vs. Value Objects):** "Explain the difference between an Entity and a Value Object using C# examples. Create an `Address` value object (Street, City, ZipCode, Country) and show how it could be used within a `Customer` entity. Emphasize immutability and structural equality for the value object."

## 5. Event-Driven Strategy Details

*   **Prompt (Domain vs. Integration Events):** "Clarify the distinction between Domain Events and Integration Events in terms of purpose, scope, and typical processing (synchronous/asynchronous). Provide a C# example of each for a 'User Registration' scenario (e.g., `UserRegisteredDomainEvent` leading to `WelcomeEmailSentIntegrationEvent`)."
*   **Prompt (Idempotency):** "Explain why idempotency is crucial for event consumers in an event-driven architecture. Provide a C# example of an event handler that processes an `OrderPaymentReceivedEvent` idempotently, perhaps by checking if the payment has already been recorded for that order ID."
*   **Prompt (Outbox Pattern):** "Describe the Outbox Pattern for reliable event publishing. Provide a conceptual C# and SQL (or EF Core) example of how an event would be saved to an 'outbox' table within the same transaction as domain changes, and how a separate process (e.g., a background worker in `Kantoku.Processor`) would poll this table and publish events to a message broker."

## 6. Tooling & Technology

*   **Prompt:** "For each tool/technology listed (EF Core, MediatR, a chosen Message Broker like RabbitMQ, FluentValidation, xUnit, Serilog, Swagger), provide a brief C# code snippet showing its basic setup or a common usage pattern within the context of the KantokuService refactoring project. For instance, show EF Core DbContext registration, MediatR registration in `Startup.cs`, a basic FluentValidation rule, etc."

## 7. Testing Strategy

*   **Prompt (Layered Testing):** "For each layer (Domain, Application, Infrastructure, API/Presentation), describe the primary goal of testing in that layer and the common types of tests performed (unit, integration). Provide a one-sentence C# xUnit test example for each layer, highlighting what is being tested and what is typically mocked."
*   **Prompt (End-to-End & Contract Testing):** "Explain the purpose of End-to-End tests and Contract tests in this architecture. Provide a conceptual scenario for an E2E test (e.g., user creates a project, and it appears in their dashboard, possibly involving event processing). For contract testing, describe how you would ensure an `IntegrationEvent` published by one service matches the schema expected by a consuming service."

## 8. Potential Challenges & Mitigation

*   **Prompt:** "For three key challenges (e.g., 'Identifying Bounded Contexts', 'Database Refactoring & Data Migration', 'Managing Eventual Consistency'), elaborate on the challenge and propose 2-3 concrete mitigation strategies for each. For 'Eventual Consistency', explain how to design a UI to handle it gracefully."

## 9. Next Steps (Immediate Actions)

*   **Prompt:** "Outline a detailed agenda for an initial project kickoff workshop focused on DDD and Event Storming for the KantokuService refactoring. List the participants who should be involved and the desired outcomes of the workshop." 