using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.CustomerManagement.ValueObjects;
using Kantoku.Domain.CustomerManagement.Events;
using Kantoku.Domain.CustomerManagement.Enums;

namespace Kantoku.Domain.CustomerManagement;

/// <summary>
/// Customer aggregate root representing a customer in the system
/// </summary>
public class Customer : FullAuditedEntity<Guid>
{
    private readonly List<CustomerContact> _contacts = new();

    public Guid OrgId { get; private set; }
    public string CustomerCode { get; private set; } = null!;
    public string CustomerName { get; private set; } = null!;
    public string? CustomerSubName { get; private set; }
    public string? Description { get; private set; }
    
    public Guid? CustomerTypeId { get; private set; }
    public CustomerStatus Status { get; private set; } = CustomerStatus.Active;
    
    public CustomerAddress? Address { get; private set; }
    public CustomerContactInfo? ContactInfo { get; private set; }
    public CustomerBusinessInfo? BusinessInfo { get; private set; }
    
    public string? LogoUrl { get; private set; }
    public string? Website { get; private set; }
    public decimal CreditLimit { get; private set; } = 0;
    public string Currency { get; private set; } = "JPY";
    public string PaymentTerms { get; private set; } = "NET30";
    
    public bool IsVip { get; private set; } = false;
    public bool IsBlacklisted { get; private set; } = false;

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<CustomerContact> Contacts => _contacts.AsReadOnly();

    // Private constructor for EF Core
    private Customer() : base() { }

    /// <summary>
    /// Creates a new customer
    /// </summary>
    public Customer(
        Guid id,
        Guid orgId,
        string customerCode,
        string customerName,
        string? customerSubName = null,
        string? description = null,
        Guid? customerTypeId = null) : base(id)
    {
        OrgId = orgId;
        SetCustomerCode(customerCode);
        SetCustomerName(customerName);
        CustomerSubName = customerSubName;
        Description = description;
        CustomerTypeId = customerTypeId;

        AddDomainEvent(new CustomerCreatedEvent(this));
    }

    /// <summary>
    /// Updates basic customer information
    /// </summary>
    public void UpdateBasicInfo(
        string customerName,
        string? customerSubName = null,
        string? description = null,
        Guid? customerTypeId = null)
    {
        SetCustomerName(customerName);
        CustomerSubName = customerSubName;
        Description = description;
        CustomerTypeId = customerTypeId;

        AddDomainEvent(new CustomerUpdatedEvent(this));
    }

    /// <summary>
    /// Updates customer address
    /// </summary>
    public void UpdateAddress(CustomerAddress address)
    {
        Address = address;
        AddDomainEvent(new CustomerAddressUpdatedEvent(this));
    }

    /// <summary>
    /// Updates customer contact information
    /// </summary>
    public void UpdateContactInfo(CustomerContactInfo contactInfo)
    {
        ContactInfo = contactInfo;
        AddDomainEvent(new CustomerContactInfoUpdatedEvent(this));
    }

    /// <summary>
    /// Updates customer business information
    /// </summary>
    public void UpdateBusinessInfo(CustomerBusinessInfo businessInfo)
    {
        BusinessInfo = businessInfo;
        AddDomainEvent(new CustomerBusinessInfoUpdatedEvent(this));
    }

    /// <summary>
    /// Updates customer status
    /// </summary>
    public void UpdateStatus(CustomerStatus status)
    {
        var oldStatus = Status;
        Status = status;

        if (oldStatus != status)
        {
            AddDomainEvent(new CustomerStatusChangedEvent(this, oldStatus, status));
        }
    }

    /// <summary>
    /// Updates financial information
    /// </summary>
    public void UpdateFinancialInfo(
        decimal creditLimit,
        string currency = "JPY",
        string paymentTerms = "NET30")
    {
        SetCreditLimit(creditLimit);
        SetCurrency(currency);
        SetPaymentTerms(paymentTerms);

        AddDomainEvent(new CustomerFinancialInfoUpdatedEvent(this));
    }

    /// <summary>
    /// Updates web presence information
    /// </summary>
    public void UpdateWebPresence(string? logoUrl = null, string? website = null)
    {
        LogoUrl = logoUrl;
        Website = website;
    }

    /// <summary>
    /// Sets customer as VIP
    /// </summary>
    public void SetAsVip()
    {
        if (!IsVip)
        {
            IsVip = true;
            AddDomainEvent(new CustomerVipStatusChangedEvent(this, true));
        }
    }

    /// <summary>
    /// Removes VIP status
    /// </summary>
    public void RemoveVipStatus()
    {
        if (IsVip)
        {
            IsVip = false;
            AddDomainEvent(new CustomerVipStatusChangedEvent(this, false));
        }
    }

    /// <summary>
    /// Blacklists the customer
    /// </summary>
    public void Blacklist(string reason)
    {
        if (!IsBlacklisted)
        {
            IsBlacklisted = true;
            Status = CustomerStatus.Blacklisted;
            AddDomainEvent(new CustomerBlacklistedEvent(this, reason));
        }
    }

    /// <summary>
    /// Removes customer from blacklist
    /// </summary>
    public void RemoveFromBlacklist()
    {
        if (IsBlacklisted)
        {
            IsBlacklisted = false;
            Status = CustomerStatus.Active;
            AddDomainEvent(new CustomerRemovedFromBlacklistEvent(this));
        }
    }

    /// <summary>
    /// Adds a contact to the customer
    /// </summary>
    public void AddContact(CustomerContact contact)
    {
        if (contact == null)
            throw new ArgumentNullException(nameof(contact));

        if (_contacts.Any(c => c.Email == contact.Email))
            throw new InvalidOperationException("Contact with this email already exists");

        _contacts.Add(contact);
        AddDomainEvent(new CustomerContactAddedEvent(this, contact));
    }

    /// <summary>
    /// Removes a contact from the customer
    /// </summary>
    public void RemoveContact(Guid contactId)
    {
        var contact = _contacts.FirstOrDefault(c => c.Id == contactId);
        if (contact != null)
        {
            _contacts.Remove(contact);
            AddDomainEvent(new CustomerContactRemovedEvent(this, contact));
        }
    }

    /// <summary>
    /// Gets the primary contact
    /// </summary>
    public CustomerContact? GetPrimaryContact()
    {
        return _contacts.FirstOrDefault(c => c.IsPrimary) ?? _contacts.FirstOrDefault();
    }

    /// <summary>
    /// Activates the customer
    /// </summary>
    public void Activate()
    {
        if (Status != CustomerStatus.Active)
        {
            Status = CustomerStatus.Active;
            AddDomainEvent(new CustomerActivatedEvent(this));
        }
    }

    /// <summary>
    /// Deactivates the customer
    /// </summary>
    public void Deactivate()
    {
        if (Status != CustomerStatus.Inactive)
        {
            Status = CustomerStatus.Inactive;
            AddDomainEvent(new CustomerDeactivatedEvent(this));
        }
    }

    /// <summary>
    /// Checks if the customer can place orders
    /// </summary>
    public bool CanPlaceOrders => Status == CustomerStatus.Active && !IsBlacklisted;

    /// <summary>
    /// Checks if the customer has exceeded credit limit
    /// </summary>
    public bool HasExceededCreditLimit(decimal currentBalance)
    {
        return CreditLimit > 0 && currentBalance > CreditLimit;
    }

    // Private helper methods
    private void SetCustomerCode(string customerCode)
    {
        if (string.IsNullOrWhiteSpace(customerCode))
            throw new ArgumentException("Customer code cannot be null or empty", nameof(customerCode));

        if (customerCode.Length > 50)
            throw new ArgumentException("Customer code cannot exceed 50 characters", nameof(customerCode));

        CustomerCode = customerCode.Trim();
    }

    private void SetCustomerName(string customerName)
    {
        if (string.IsNullOrWhiteSpace(customerName))
            throw new ArgumentException("Customer name cannot be null or empty", nameof(customerName));

        if (customerName.Length > 200)
            throw new ArgumentException("Customer name cannot exceed 200 characters", nameof(customerName));

        CustomerName = customerName.Trim();
    }

    private void SetCreditLimit(decimal creditLimit)
    {
        if (creditLimit < 0)
            throw new ArgumentException("Credit limit cannot be negative", nameof(creditLimit));

        CreditLimit = creditLimit;
    }

    private void SetCurrency(string currency)
    {
        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("Currency cannot be null or empty", nameof(currency));

        if (currency.Length != 3)
            throw new ArgumentException("Currency must be a 3-character ISO code", nameof(currency));

        Currency = currency.ToUpperInvariant();
    }

    private void SetPaymentTerms(string paymentTerms)
    {
        if (string.IsNullOrWhiteSpace(paymentTerms))
            throw new ArgumentException("Payment terms cannot be null or empty", nameof(paymentTerms));

        if (paymentTerms.Length > 50)
            throw new ArgumentException("Payment terms cannot exceed 50 characters", nameof(paymentTerms));

        PaymentTerms = paymentTerms.Trim();
    }
}
