using Kantoku.SharedKernel;

namespace Kantoku.Domain.InventoryManagement;

/// <summary>
/// Entity representing an item price
/// </summary>
public class ItemPrice : Entity<Guid>
{
    public Guid ItemId { get; private set; }
    public string PriceType { get; private set; } = "STANDARD"; // STANDARD, WHOLESALE, RETAIL, SPECIAL
    public decimal Price { get; private set; }
    public string Currency { get; private set; } = "JPY";
    public DateOnly EffectiveDate { get; private set; }
    public DateOnly? ExpiryDate { get; private set; }
    public bool IsActive { get; private set; } = true;
    public string? Description { get; private set; }
    public Guid? VendorId { get; private set; }

    // Private constructor for EF Core
    private ItemPrice() : base() { }

    /// <summary>
    /// Creates a new item price
    /// </summary>
    public ItemPrice(
        Guid id,
        Guid itemId,
        decimal price,
        string priceType = "STANDARD",
        string currency = "JPY",
        DateOnly? effectiveDate = null,
        DateOnly? expiryDate = null,
        string? description = null,
        Guid? vendorId = null) : base(id)
    {
        ItemId = itemId;
        SetPrice(price);
        SetPriceType(priceType);
        SetCurrency(currency);
        EffectiveDate = effectiveDate ?? DateOnly.FromDateTime(DateTime.Today);
        SetExpiryDate(expiryDate);
        Description = description;
        VendorId = vendorId;
    }

    /// <summary>
    /// Updates the price
    /// </summary>
    public void UpdatePrice(decimal price)
    {
        SetPrice(price);
    }

    /// <summary>
    /// Updates the price type
    /// </summary>
    public void UpdatePriceType(string priceType)
    {
        SetPriceType(priceType);
    }

    /// <summary>
    /// Updates the currency
    /// </summary>
    public void UpdateCurrency(string currency)
    {
        SetCurrency(currency);
    }

    /// <summary>
    /// Updates the effective date
    /// </summary>
    public void UpdateEffectiveDate(DateOnly effectiveDate)
    {
        EffectiveDate = effectiveDate;
        ValidateDates();
    }

    /// <summary>
    /// Updates the expiry date
    /// </summary>
    public void UpdateExpiryDate(DateOnly? expiryDate)
    {
        SetExpiryDate(expiryDate);
    }

    /// <summary>
    /// Updates the description
    /// </summary>
    public void UpdateDescription(string? description)
    {
        Description = description;
    }

    /// <summary>
    /// Updates the vendor
    /// </summary>
    public void UpdateVendor(Guid? vendorId)
    {
        VendorId = vendorId;
    }

    /// <summary>
    /// Activates the price
    /// </summary>
    public void Activate()
    {
        IsActive = true;
    }

    /// <summary>
    /// Deactivates the price
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
    }

    /// <summary>
    /// Checks if the price is currently valid
    /// </summary>
    public bool IsCurrentlyValid
    {
        get
        {
            var today = DateOnly.FromDateTime(DateTime.Today);
            return IsActive && 
                   EffectiveDate <= today && 
                   (!ExpiryDate.HasValue || ExpiryDate.Value >= today);
        }
    }

    /// <summary>
    /// Checks if the price is expired
    /// </summary>
    public bool IsExpired
    {
        get
        {
            var today = DateOnly.FromDateTime(DateTime.Today);
            return ExpiryDate.HasValue && ExpiryDate.Value < today;
        }
    }

    /// <summary>
    /// Checks if the price is future-dated
    /// </summary>
    public bool IsFuture
    {
        get
        {
            var today = DateOnly.FromDateTime(DateTime.Today);
            return EffectiveDate > today;
        }
    }

    /// <summary>
    /// Gets the number of days until the price becomes effective
    /// </summary>
    public int? DaysUntilEffective
    {
        get
        {
            var today = DateOnly.FromDateTime(DateTime.Today);
            if (EffectiveDate <= today)
                return null;
            
            return EffectiveDate.DayNumber - today.DayNumber;
        }
    }

    /// <summary>
    /// Gets the number of days until the price expires
    /// </summary>
    public int? DaysUntilExpiry
    {
        get
        {
            if (!ExpiryDate.HasValue)
                return null;
            
            var today = DateOnly.FromDateTime(DateTime.Today);
            if (ExpiryDate.Value < today)
                return null;
            
            return ExpiryDate.Value.DayNumber - today.DayNumber;
        }
    }

    // Private helper methods
    private void SetPrice(decimal price)
    {
        if (price < 0)
            throw new ArgumentException("Price cannot be negative", nameof(price));

        Price = price;
    }

    private void SetPriceType(string priceType)
    {
        if (string.IsNullOrWhiteSpace(priceType))
            throw new ArgumentException("Price type cannot be null or empty", nameof(priceType));

        var validTypes = new[] { "STANDARD", "WHOLESALE", "RETAIL", "SPECIAL", "COST", "MSRP" };
        if (!validTypes.Contains(priceType.ToUpperInvariant()))
            throw new ArgumentException($"Invalid price type: {priceType}. Valid types are: {string.Join(", ", validTypes)}");

        PriceType = priceType.ToUpperInvariant();
    }

    private void SetCurrency(string currency)
    {
        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("Currency cannot be null or empty", nameof(currency));

        if (currency.Length != 3)
            throw new ArgumentException("Currency must be a 3-character ISO code", nameof(currency));

        Currency = currency.ToUpperInvariant();
    }

    private void SetExpiryDate(DateOnly? expiryDate)
    {
        ExpiryDate = expiryDate;
        ValidateDates();
    }

    private void ValidateDates()
    {
        if (ExpiryDate.HasValue && ExpiryDate.Value <= EffectiveDate)
            throw new ArgumentException("Expiry date must be after effective date");
    }

    public override string ToString()
    {
        var result = $"{Price:N2} {Currency} ({PriceType})";
        
        if (IsFuture)
            result += $" - Effective from {EffectiveDate}";
        else if (IsExpired)
            result += $" - Expired on {ExpiryDate}";
        else if (ExpiryDate.HasValue)
            result += $" - Valid until {ExpiryDate}";
        
        if (!IsActive)
            result += " (Inactive)";
        
        return result;
    }
}
