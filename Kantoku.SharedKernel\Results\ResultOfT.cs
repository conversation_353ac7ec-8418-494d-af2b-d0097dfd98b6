namespace Kantoku.SharedKernel.Results;

/// <summary>
/// Represents the result of an operation that can either succeed with a value or fail.
/// This is a generic version for operations that return a value.
/// </summary>
/// <typeparam name="T">The type of the value returned by the operation</typeparam>
public class Result<T> : Result
{
    /// <summary>
    /// Gets the value if the operation was successful
    /// </summary>
    public T? Value { get; protected set; }

    /// <summary>
    /// Initializes a new instance of the Result class
    /// </summary>
    /// <param name="isSuccess">Whether the operation was successful</param>
    /// <param name="value">The value if the operation was successful</param>
    /// <param name="error">The error message if the operation failed</param>
    /// <param name="errors">The collection of error messages if the operation failed</param>
    protected Result(bool isSuccess, T? value = default, string? error = null, IEnumerable<string>? errors = null)
        : base(isSuccess, error, errors)
    {
        Value = value;

        if (isSuccess && value is null && !typeof(T).IsValueType)
        {
            throw new InvalidOperationException("A successful result must have a non-null value for reference types.");
        }
    }

    /// <summary>
    /// Creates a successful result with the specified value
    /// </summary>
    /// <param name="value">The value</param>
    /// <returns>A successful result</returns>
    public static Result<T> Success(T value) => new(true, value);

    /// <summary>
    /// Creates a failed result with the specified error message
    /// </summary>
    /// <param name="error">The error message</param>
    /// <returns>A failed result</returns>
    public static new Result<T> Failure(string error) => new(false, error: error);

    /// <summary>
    /// Creates a failed result with the specified error messages
    /// </summary>
    /// <param name="errors">The error messages</param>
    /// <returns>A failed result</returns>
    public static new Result<T> Failure(IEnumerable<string> errors) => new(false, errors: errors);

    /// <summary>
    /// Creates a failed result with the specified error messages
    /// </summary>
    /// <param name="errors">The error messages</param>
    /// <returns>A failed result</returns>
    public static new Result<T> Failure(params string[] errors) => new(false, errors: errors);

    /// <summary>
    /// Implicitly converts a value to a successful result
    /// </summary>
    /// <param name="value">The value</param>
    public static implicit operator Result<T>(T value) => Success(value);

    /// <summary>
    /// Implicitly converts a string to a failed result
    /// </summary>
    /// <param name="error">The error message</param>
    public static implicit operator Result<T>(string error) => Failure(error);



    /// <summary>
    /// Maps the value of a successful result to a new type
    /// </summary>
    /// <typeparam name="TNew">The new type</typeparam>
    /// <param name="mapper">The mapping function</param>
    /// <returns>A new result with the mapped value</returns>
    public Result<TNew> Map<TNew>(Func<T, TNew> mapper)
    {
        if (IsFailure)
            return Errors.Any() ? Result<TNew>.Failure(Errors) : Result<TNew>.Failure(Error!);

        try
        {
            var mappedValue = mapper(Value!);
            return Result<TNew>.Success(mappedValue);
        }
        catch (Exception ex)
        {
            return Result<TNew>.Failure($"Mapping failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Binds the value of a successful result to a new result-returning operation
    /// </summary>
    /// <typeparam name="TNew">The new type</typeparam>
    /// <param name="binder">The binding function</param>
    /// <returns>A new result</returns>
    public Result<TNew> Bind<TNew>(Func<T, Result<TNew>> binder)
    {
        if (IsFailure)
            return Errors.Any() ? Result<TNew>.Failure(Errors) : Result<TNew>.Failure(Error!);

        try
        {
            return binder(Value!);
        }
        catch (Exception ex)
        {
            return Result<TNew>.Failure($"Binding failed: {ex.Message}");
        }
    }
}
