using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class MonthlyReportFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search for name or code of employee
    /// </summary>
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    /// <summary>
    /// Report from date
    /// </summary>
    [FromQuery(Name = "dateFrom")]
    [Required]
    public required string ReportFrom { get; set; }

    /// <summary>
    /// Report to date
    /// </summary>
    [FromQuery(Name = "dateTo")]
    [Required]
    public required string ReportTo { get; set; }
}