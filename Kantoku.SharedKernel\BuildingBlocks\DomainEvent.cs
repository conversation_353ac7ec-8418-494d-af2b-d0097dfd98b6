using MediatR;

namespace Kantoku.SharedKernel.BuildingBlocks;

/// <summary>
/// Base class for domain events in Domain-Driven Design.
/// Domain events represent something that happened in the domain that domain experts care about.
/// They are used to decouple different parts of the domain and enable eventual consistency.
/// </summary>
public abstract class DomainEvent : INotification
{
    /// <summary>
    /// Gets the date and time when the domain event occurred
    /// </summary>
    public DateTimeOffset DateOccurred { get; protected set; } = DateTimeOffset.UtcNow;

    /// <summary>
    /// Gets a unique identifier for this domain event
    /// </summary>
    public Guid EventId { get; protected set; } = Guid.NewGuid();

    /// <summary>
    /// Gets the version of the event schema (useful for event versioning)
    /// </summary>
    public virtual int Version { get; protected set; } = 1;
}
