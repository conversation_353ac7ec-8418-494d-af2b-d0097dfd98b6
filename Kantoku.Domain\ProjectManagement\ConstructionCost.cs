using Kantoku.SharedKernel;

namespace Kantoku.Domain.ProjectManagement;

/// <summary>
/// Entity representing a cost item for a construction
/// </summary>
public class ConstructionCost : Entity<Guid>
{
    public Guid ConstructionId { get; private set; }
    public Guid OrgId { get; private set; }
    public Guid? CategoryId { get; private set; }
    public string CostName { get; private set; } = null!;
    public string? Description { get; private set; }
    public decimal Amount { get; private set; }
    public string Currency { get; private set; } = "JPY";
    public DateOnly CostDate { get; private set; }
    public string CostType { get; private set; } = "MATERIAL"; // MATERIAL, LABOR, EQUIPMENT, OTHER
    public string? Vendor { get; private set; }
    public string? InvoiceNumber { get; private set; }
    public bool IsApproved { get; private set; } = false;
    public Guid? ApprovedBy { get; private set; }
    public DateTime? ApprovedDate { get; private set; }
    public bool IsDeleted { get; private set; } = false;

    // Private constructor for EF Core
    private ConstructionCost() : base() { }

    /// <summary>
    /// Creates a new construction cost
    /// </summary>
    public ConstructionCost(
        Guid id,
        Guid constructionId,
        Guid orgId,
        string costName,
        decimal amount,
        DateOnly costDate,
        string costType = "MATERIAL",
        string currency = "JPY",
        string? description = null,
        Guid? categoryId = null,
        string? vendor = null,
        string? invoiceNumber = null) : base(id)
    {
        ConstructionId = constructionId;
        OrgId = orgId;
        SetCostName(costName);
        SetAmount(amount);
        SetCurrency(currency);
        CostDate = costDate;
        SetCostType(costType);
        Description = description;
        CategoryId = categoryId;
        Vendor = vendor;
        InvoiceNumber = invoiceNumber;
    }

    /// <summary>
    /// Updates cost information
    /// </summary>
    public void UpdateCost(
        string costName,
        decimal amount,
        DateOnly costDate,
        string? description = null,
        string? vendor = null,
        string? invoiceNumber = null)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update approved cost");

        SetCostName(costName);
        SetAmount(amount);
        CostDate = costDate;
        Description = description;
        Vendor = vendor;
        InvoiceNumber = invoiceNumber;
    }

    /// <summary>
    /// Updates the cost category
    /// </summary>
    public void UpdateCategory(Guid? categoryId)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update category for approved cost");

        CategoryId = categoryId;
    }

    /// <summary>
    /// Updates the cost type
    /// </summary>
    public void UpdateCostType(string costType)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update cost type for approved cost");

        SetCostType(costType);
    }

    /// <summary>
    /// Approves the cost
    /// </summary>
    public void Approve(Guid approvedBy)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cost is already approved");

        IsApproved = true;
        ApprovedBy = approvedBy;
        ApprovedDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Revokes approval
    /// </summary>
    public void RevokeApproval()
    {
        if (!IsApproved)
            throw new InvalidOperationException("Cost is not approved");

        IsApproved = false;
        ApprovedBy = null;
        ApprovedDate = null;
    }

    /// <summary>
    /// Soft deletes the cost
    /// </summary>
    public void Delete()
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot delete approved cost");

        IsDeleted = true;
    }

    /// <summary>
    /// Restores the cost
    /// </summary>
    public void Restore()
    {
        IsDeleted = false;
    }

    // Private helper methods
    private void SetCostName(string costName)
    {
        if (string.IsNullOrWhiteSpace(costName))
            throw new ArgumentException("Cost name cannot be null or empty", nameof(costName));

        if (costName.Length > 200)
            throw new ArgumentException("Cost name cannot exceed 200 characters", nameof(costName));

        CostName = costName.Trim();
    }

    private void SetAmount(decimal amount)
    {
        if (amount < 0)
            throw new ArgumentException("Amount cannot be negative", nameof(amount));

        Amount = amount;
    }

    private void SetCurrency(string currency)
    {
        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("Currency cannot be null or empty", nameof(currency));

        if (currency.Length != 3)
            throw new ArgumentException("Currency must be a 3-character ISO code", nameof(currency));

        Currency = currency.ToUpperInvariant();
    }

    private void SetCostType(string costType)
    {
        var validTypes = new[] { "MATERIAL", "LABOR", "EQUIPMENT", "OTHER" };
        if (!validTypes.Contains(costType))
            throw new ArgumentException($"Invalid cost type: {costType}. Valid types are: {string.Join(", ", validTypes)}");

        CostType = costType;
    }
}
