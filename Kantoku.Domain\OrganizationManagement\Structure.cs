using Kantoku.SharedKernel;
using Kantoku.SharedKernel.ValueObjects;

namespace Kantoku.Domain.OrganizationManagement;

/// <summary>
/// Entity representing an organizational structure/department
/// </summary>
public class Structure : Entity<StructureId>
{
    private readonly List<Structure> _children = new();

    public OrganizationId OrgId { get; private set; }
    public StructureId? ParentId { get; private set; }
    public string StructureCode { get; private set; } = null!;
    public string StructureName { get; private set; } = null!;
    public string? Description { get; private set; }
    public bool IsHidden { get; private set; } = false;
    public bool IsDefault { get; private set; } = false;
    public bool IsDeleted { get; private set; } = false;

    // Navigation properties
    public Structure? Parent { get; private set; }
    public IReadOnlyCollection<Structure> Children => _children.AsReadOnly();

    // Private constructor for EF Core
    private Structure() : base() { }

    /// <summary>
    /// Creates a new structure
    /// </summary>
    public Structure(
        StructureId id,
        OrganizationId orgId,
        string structureCode,
        string structureName,
        string? description = null,
        StructureId? parentId = null) : base(id)
    {
        OrgId = orgId;
        SetStructureCode(structureCode);
        SetStructureName(structureName);
        Description = description;
        ParentId = parentId;
    }

    /// <summary>
    /// Updates structure information
    /// </summary>
    public void Update(
        string structureName,
        string? description = null)
    {
        SetStructureName(structureName);
        Description = description;
    }

    /// <summary>
    /// Sets the parent structure
    /// </summary>
    public void SetParent(Structure? parent)
    {
        if (parent != null)
        {
            if (parent.OrgId != OrgId)
                throw new InvalidOperationException("Parent structure must belong to the same organization");

            if (parent.Id.Equals(Id))
                throw new InvalidOperationException("Structure cannot be its own parent");

            if (IsDescendantOf(parent))
                throw new InvalidOperationException("Cannot set a descendant as parent (circular reference)");
        }

        Parent = parent;
        ParentId = parent?.Id;
    }

    /// <summary>
    /// Adds a child structure
    /// </summary>
    public void AddChild(Structure child)
    {
        if (child == null)
            throw new ArgumentNullException(nameof(child));

        if (child.OrgId != OrgId)
            throw new InvalidOperationException("Child structure must belong to the same organization");

        if (child.Id.Equals(Id))
            throw new InvalidOperationException("Structure cannot be its own child");

        if (_children.Any(c => c.Id.Equals(child.Id)))
            throw new InvalidOperationException("Child structure already exists");

        child.SetParent(this);
        _children.Add(child);
    }

    /// <summary>
    /// Removes a child structure
    /// </summary>
    public void RemoveChild(Structure child)
    {
        if (child == null)
            throw new ArgumentNullException(nameof(child));

        if (_children.Remove(child))
        {
            child.SetParent(null);
        }
    }

    /// <summary>
    /// Sets the structure as default
    /// </summary>
    public void SetAsDefault()
    {
        IsDefault = true;
    }

    /// <summary>
    /// Unsets the structure as default
    /// </summary>
    public void UnsetAsDefault()
    {
        IsDefault = false;
    }

    /// <summary>
    /// Hides the structure
    /// </summary>
    public void Hide()
    {
        IsHidden = true;
    }

    /// <summary>
    /// Shows the structure
    /// </summary>
    public void Show()
    {
        IsHidden = false;
    }

    /// <summary>
    /// Soft deletes the structure
    /// </summary>
    public void Delete()
    {
        if (_children.Any(c => !c.IsDeleted))
            throw new InvalidOperationException("Cannot delete structure with active children");

        IsDeleted = true;
    }

    /// <summary>
    /// Restores the structure
    /// </summary>
    public void Restore()
    {
        IsDeleted = false;
    }

    /// <summary>
    /// Checks if this structure is a descendant of the specified structure
    /// </summary>
    private bool IsDescendantOf(Structure structure)
    {
        var current = Parent;
        while (current != null)
        {
            if (current.Id.Equals(structure.Id))
                return true;
            current = current.Parent;
        }
        return false;
    }

    // Private helper methods
    private void SetStructureCode(string structureCode)
    {
        if (string.IsNullOrWhiteSpace(structureCode))
            throw new ArgumentException("Structure code cannot be null or empty", nameof(structureCode));

        if (structureCode.Length > 50)
            throw new ArgumentException("Structure code cannot exceed 50 characters", nameof(structureCode));

        StructureCode = structureCode.Trim();
    }

    private void SetStructureName(string structureName)
    {
        if (string.IsNullOrWhiteSpace(structureName))
            throw new ArgumentException("Structure name cannot be null or empty", nameof(structureName));

        if (structureName.Length > 200)
            throw new ArgumentException("Structure name cannot exceed 200 characters", nameof(structureName));

        StructureName = structureName.Trim();
    }
}
