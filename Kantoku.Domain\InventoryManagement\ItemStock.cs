using Kantoku.SharedKernel;
using Kantoku.Domain.InventoryManagement.Enums;

namespace Kantoku.Domain.InventoryManagement;

/// <summary>
/// Entity representing a stock movement for an item
/// </summary>
public class ItemStock : Entity<Guid>
{
    public Guid ItemId { get; private set; }
    public StockMovementType MovementType { get; private set; } = null!;
    public decimal Quantity { get; private set; }
    public decimal? UnitCost { get; private set; }
    public string Reason { get; private set; } = null!;
    public string? Reference { get; private set; }
    public DateTime MovementDate { get; private set; }
    public Guid? EmployeeId { get; private set; }
    public Guid? ProjectId { get; private set; }
    public string? Notes { get; private set; }
    public bool IsReversed { get; private set; } = false;
    public Guid? ReversalMovementId { get; private set; }

    // Private constructor for EF Core
    private ItemStock() : base() { }

    /// <summary>
    /// Creates a new stock movement
    /// </summary>
    public ItemStock(
        Guid id,
        Guid itemId,
        StockMovementType movementType,
        decimal quantity,
        string reason,
        decimal? unitCost = null,
        string? reference = null,
        DateTime? movementDate = null,
        Guid? employeeId = null,
        Guid? projectId = null,
        string? notes = null) : base(id)
    {
        ItemId = itemId;
        MovementType = movementType ?? throw new ArgumentNullException(nameof(movementType));
        SetQuantity(quantity);
        SetReason(reason);
        UnitCost = unitCost;
        Reference = reference;
        MovementDate = movementDate ?? DateTime.UtcNow;
        EmployeeId = employeeId;
        ProjectId = projectId;
        Notes = notes;
    }

    /// <summary>
    /// Updates the unit cost
    /// </summary>
    public void UpdateUnitCost(decimal? unitCost)
    {
        if (IsReversed)
            throw new InvalidOperationException("Cannot update reversed stock movement");

        if (unitCost.HasValue && unitCost.Value < 0)
            throw new ArgumentException("Unit cost cannot be negative", nameof(unitCost));

        UnitCost = unitCost;
    }

    /// <summary>
    /// Updates the reference
    /// </summary>
    public void UpdateReference(string? reference)
    {
        if (IsReversed)
            throw new InvalidOperationException("Cannot update reversed stock movement");

        Reference = reference;
    }

    /// <summary>
    /// Updates the employee who performed the movement
    /// </summary>
    public void UpdateEmployee(Guid? employeeId)
    {
        if (IsReversed)
            throw new InvalidOperationException("Cannot update reversed stock movement");

        EmployeeId = employeeId;
    }

    /// <summary>
    /// Updates the project associated with the movement
    /// </summary>
    public void UpdateProject(Guid? projectId)
    {
        if (IsReversed)
            throw new InvalidOperationException("Cannot update reversed stock movement");

        ProjectId = projectId;
    }

    /// <summary>
    /// Updates the notes
    /// </summary>
    public void UpdateNotes(string? notes)
    {
        if (IsReversed)
            throw new InvalidOperationException("Cannot update reversed stock movement");

        Notes = notes;
    }

    /// <summary>
    /// Reverses this stock movement
    /// </summary>
    public ItemStock Reverse(string reversalReason, Guid? reversalEmployeeId = null)
    {
        if (IsReversed)
            throw new InvalidOperationException("Stock movement is already reversed");

        // Create the reversal movement
        var reversalType = GetReversalMovementType();
        var reversalMovement = new ItemStock(
            Guid.NewGuid(),
            ItemId,
            reversalType,
            Quantity,
            $"Reversal: {reversalReason}",
            UnitCost,
            $"REV-{Reference}",
            DateTime.UtcNow,
            reversalEmployeeId ?? EmployeeId,
            ProjectId,
            $"Reversal of movement {Id}: {reversalReason}");

        // Mark this movement as reversed
        IsReversed = true;
        ReversalMovementId = reversalMovement.Id;

        return reversalMovement;
    }

    /// <summary>
    /// Gets the total cost of this movement
    /// </summary>
    public decimal? TotalCost => UnitCost.HasValue ? UnitCost.Value * Quantity : null;

    /// <summary>
    /// Gets the effective quantity (considering direction)
    /// </summary>
    public decimal EffectiveQuantity => Quantity * MovementType.Direction;

    /// <summary>
    /// Checks if this is an incoming movement
    /// </summary>
    public bool IsIncoming => MovementType.IsIncoming;

    /// <summary>
    /// Checks if this is an outgoing movement
    /// </summary>
    public bool IsOutgoing => MovementType.IsOutgoing;

    /// <summary>
    /// Checks if this movement can be reversed
    /// </summary>
    public bool CanBeReversed => !IsReversed && MovementDate > DateTime.UtcNow.AddDays(-30); // Can reverse within 30 days

    /// <summary>
    /// Gets the age of this movement in days
    /// </summary>
    public int AgeInDays => (DateTime.UtcNow.Date - MovementDate.Date).Days;

    // Private helper methods
    private void SetQuantity(decimal quantity)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be greater than 0", nameof(quantity));

        Quantity = quantity;
    }

    private void SetReason(string reason)
    {
        if (string.IsNullOrWhiteSpace(reason))
            throw new ArgumentException("Reason cannot be null or empty", nameof(reason));

        if (reason.Length > 200)
            throw new ArgumentException("Reason cannot exceed 200 characters", nameof(reason));

        Reason = reason.Trim();
    }

    private StockMovementType GetReversalMovementType()
    {
        return MovementType.Name switch
        {
            nameof(StockMovementType.In) => StockMovementType.Out,
            nameof(StockMovementType.Out) => StockMovementType.In,
            nameof(StockMovementType.AdjustmentIn) => StockMovementType.AdjustmentOut,
            nameof(StockMovementType.AdjustmentOut) => StockMovementType.AdjustmentIn,
            nameof(StockMovementType.Return) => StockMovementType.Out,
            nameof(StockMovementType.Damage) => StockMovementType.In,
            nameof(StockMovementType.Loss) => StockMovementType.In,
            _ => throw new InvalidOperationException($"Cannot reverse movement type: {MovementType.Name}")
        };
    }

    public override string ToString()
    {
        var direction = MovementType.IsIncoming ? "+" : "-";
        var result = $"{direction}{Quantity} - {MovementType.DisplayName}";
        
        if (!string.IsNullOrEmpty(Reference))
            result += $" (Ref: {Reference})";
        
        if (TotalCost.HasValue)
            result += $" - Cost: {TotalCost:N2}";
        
        if (IsReversed)
            result += " [REVERSED]";
        
        return result;
    }
}
