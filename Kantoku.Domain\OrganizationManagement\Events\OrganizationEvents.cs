using Kantoku.SharedKernel;

namespace Kantoku.Domain.OrganizationManagement.Events;

/// <summary>
/// Domain event raised when an organization is created
/// </summary>
public class OrganizationCreatedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public DateTime OccurredOn { get; }

    public OrganizationCreatedEvent(Organization organization)
    {
        Organization = organization;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an organization's name is changed
/// </summary>
public class OrganizationNameChangedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public string OldName { get; }
    public string NewName { get; }
    public DateTime OccurredOn { get; }

    public OrganizationNameChangedEvent(Organization organization, string oldName, string newName)
    {
        Organization = organization;
        OldName = oldName;
        NewName = newName;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an organization's address is updated
/// </summary>
public class OrganizationAddressUpdatedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public DateTime OccurredOn { get; }

    public OrganizationAddressUpdatedEvent(Organization organization)
    {
        Organization = organization;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an organization's contact information is updated
/// </summary>
public class OrganizationContactInfoUpdatedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public DateTime OccurredOn { get; }

    public OrganizationContactInfoUpdatedEvent(Organization organization)
    {
        Organization = organization;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an organization is deactivated
/// </summary>
public class OrganizationDeactivatedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public DateTime OccurredOn { get; }

    public OrganizationDeactivatedEvent(Organization organization)
    {
        Organization = organization;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an organization is reactivated
/// </summary>
public class OrganizationReactivatedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public DateTime OccurredOn { get; }

    public OrganizationReactivatedEvent(Organization organization)
    {
        Organization = organization;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a structure is added to an organization
/// </summary>
public class StructureAddedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public Structure Structure { get; }
    public DateTime OccurredOn { get; }

    public StructureAddedEvent(Organization organization, Structure structure)
    {
        Organization = organization;
        Structure = structure;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a position is added to an organization
/// </summary>
public class PositionAddedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public Position Position { get; }
    public DateTime OccurredOn { get; }

    public PositionAddedEvent(Organization organization, Position position)
    {
        Organization = organization;
        Position = position;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a ranking is added to an organization
/// </summary>
public class RankingAddedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public Ranking Ranking { get; }
    public DateTime OccurredOn { get; }

    public RankingAddedEvent(Organization organization, Ranking ranking)
    {
        Organization = organization;
        Ranking = ranking;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a work shift is added to an organization
/// </summary>
public class WorkShiftAddedEvent : IDomainEvent
{
    public Organization Organization { get; }
    public WorkShift WorkShift { get; }
    public DateTime OccurredOn { get; }

    public WorkShiftAddedEvent(Organization organization, WorkShift workShift)
    {
        Organization = organization;
        WorkShift = workShift;
        OccurredOn = DateTime.UtcNow;
    }
}
