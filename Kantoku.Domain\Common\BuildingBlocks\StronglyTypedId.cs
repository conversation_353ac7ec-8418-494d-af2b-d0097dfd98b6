using System.ComponentModel;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Kantoku.Domain.Common.BuildingBlocks;

/// <summary>
/// Base class for strongly typed identifiers
/// </summary>
/// <typeparam name="TId">The type of the strongly typed ID</typeparam>
/// <typeparam name="TValue">The underlying value type (typically Guid)</typeparam>
[TypeConverter(typeof(StronglyTypedIdTypeConverter))]
[JsonConverter(typeof(StronglyTypedIdJsonConverterFactory))]
public abstract record StronglyTypedId<TId, TValue> : IEquatable<TId>
    where TId : StronglyTypedId<TId, TValue>
    where TValue : IEquatable<TValue>
{
    public TValue Value { get; }

    protected StronglyTypedId(TValue value)
    {
        if (object.Equals(value, default(TValue)))
        {
            throw new ArgumentException($"The ID value cannot be the default value for {typeof(TValue).Name}.", nameof(value));
        }
        Value = value;
    }

    public bool Equals(TId? other)
    {
        return other is not null && Value.Equals(other.Value);
    }

    public override int GetHashCode()
    {
        return Value.GetHashCode();
    }

    public override string ToString()
    {
        return Value.ToString() ?? string.Empty;
    }

    public static implicit operator TValue(StronglyTypedId<TId, TValue> id)
    {
        return id.Value;
    }
}

/// <summary>
/// Base class for Guid-based strongly typed identifiers
/// </summary>
/// <typeparam name="TId">The type of the strongly typed ID</typeparam>
public abstract record StronglyTypedId<TId> : StronglyTypedId<TId, Guid>
    where TId : StronglyTypedId<TId>
{
    protected StronglyTypedId(Guid value) : base(value) { }
}

/// <summary>
/// Type converter for strongly typed IDs
/// </summary>
public class StronglyTypedIdTypeConverter : TypeConverter
{
    public override bool CanConvertFrom(ITypeDescriptorContext? context, Type sourceType)
    {
        return sourceType == typeof(string) || sourceType == typeof(Guid) || base.CanConvertFrom(context, sourceType);
    }

    public override object? ConvertFrom(ITypeDescriptorContext? context, System.Globalization.CultureInfo? culture, object value)
    {
        if (context?.PropertyDescriptor?.PropertyType is Type targetType)
        {
            if (value is string stringValue && Guid.TryParse(stringValue, out var guidFromString))
            {
                return Activator.CreateInstance(targetType, guidFromString);
            }
            
            if (value is Guid guidValue)
            {
                return Activator.CreateInstance(targetType, guidValue);
            }
        }

        return base.ConvertFrom(context, culture, value);
    }

    public override bool CanConvertTo(ITypeDescriptorContext? context, Type? destinationType)
    {
        return destinationType == typeof(string) || destinationType == typeof(Guid) || base.CanConvertTo(context, destinationType);
    }

    public override object? ConvertTo(ITypeDescriptorContext? context, System.Globalization.CultureInfo? culture, object? value, Type destinationType)
    {
        if (value is not null && IsStronglyTypedId(value.GetType()))
        {
            var valueProperty = value.GetType().GetProperty("Value");
            var underlyingValue = valueProperty?.GetValue(value);

            if (destinationType == typeof(string))
            {
                return underlyingValue?.ToString();
            }

            if (destinationType == typeof(Guid) && underlyingValue is Guid guidValue)
            {
                return guidValue;
            }
        }

        return base.ConvertTo(context, culture, value, destinationType);
    }

    private static bool IsStronglyTypedId(Type type)
    {
        while (type is not null)
        {
            if (type.IsGenericType)
            {
                var genericTypeDefinition = type.GetGenericTypeDefinition();
                if (genericTypeDefinition == typeof(StronglyTypedId<,>) || genericTypeDefinition == typeof(StronglyTypedId<>))
                {
                    return true;
                }
            }
            type = type.BaseType!;
        }
        return false;
    }
}

/// <summary>
/// JSON converter factory for strongly typed IDs
/// </summary>
public class StronglyTypedIdJsonConverterFactory : JsonConverterFactory
{
    public override bool CanConvert(Type typeToConvert)
    {
        return IsStronglyTypedId(typeToConvert);
    }

    public override JsonConverter CreateConverter(Type typeToConvert, JsonSerializerOptions options)
    {
        var converterType = typeof(StronglyTypedIdJsonConverter<>).MakeGenericType(typeToConvert);
        return (JsonConverter)Activator.CreateInstance(converterType)!;
    }

    private static bool IsStronglyTypedId(Type type)
    {
        while (type is not null)
        {
            if (type.IsGenericType)
            {
                var genericTypeDefinition = type.GetGenericTypeDefinition();
                if (genericTypeDefinition == typeof(StronglyTypedId<,>) || genericTypeDefinition == typeof(StronglyTypedId<>))
                {
                    return true;
                }
            }
            type = type.BaseType!;
        }
        return false;
    }
}

/// <summary>
/// JSON converter for strongly typed IDs
/// </summary>
public class StronglyTypedIdJsonConverter<TId> : JsonConverter<TId>
    where TId : class
{
    public override TId? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Null)
        {
            return null;
        }

        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            if (string.IsNullOrEmpty(stringValue))
            {
                return null;
            }

            if (Guid.TryParse(stringValue, out var guid))
            {
                return (TId?)Activator.CreateInstance(typeToConvert, guid);
            }
        }

        throw new JsonException($"Unable to convert JSON to {typeof(TId).Name}");
    }

    public override void Write(Utf8JsonWriter writer, TId value, JsonSerializerOptions options)
    {
        if (value is null)
        {
            writer.WriteNullValue();
        }
        else
        {
            var valueProperty = value.GetType().GetProperty("Value");
            var underlyingValue = valueProperty?.GetValue(value);
            writer.WriteStringValue(underlyingValue?.ToString());
        }
    }
}
