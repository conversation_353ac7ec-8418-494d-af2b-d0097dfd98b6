using Kantoku.Domain.RequestManagement.Enums;

namespace Kantoku.Domain.RequestManagement;

/// <summary>
/// Repository interface for Request aggregate
/// </summary>
public interface IRequestRepository
{
    /// <summary>
    /// Gets a request by its ID
    /// </summary>
    Task<Request?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a request by its code within an organization
    /// </summary>
    Task<Request?> GetByCodeAsync(Guid orgId, string requestCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all requests for an organization
    /// </summary>
    Task<IEnumerable<Request>> GetByOrganizationAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets requests by requester
    /// </summary>
    Task<IEnumerable<Request>> GetByRequesterAsync(Guid requesterId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets requests by status within an organization
    /// </summary>
    Task<IEnumerable<Request>> GetByStatusAsync(Guid orgId, RequestStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets requests by type within an organization
    /// </summary>
    Task<IEnumerable<Request>> GetByTypeAsync(Guid orgId, RequestType requestType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets requests by priority within an organization
    /// </summary>
    Task<IEnumerable<Request>> GetByPriorityAsync(Guid orgId, RequestPriority priority, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets pending requests for an approver
    /// </summary>
    Task<IEnumerable<Request>> GetPendingForApproverAsync(Guid approverId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets overdue requests within an organization
    /// </summary>
    Task<IEnumerable<Request>> GetOverdueRequestsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets requests within a date range
    /// </summary>
    Task<IEnumerable<Request>> GetByDateRangeAsync(
        Guid orgId, 
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets requests submitted within a date range
    /// </summary>
    Task<IEnumerable<Request>> GetBySubmissionDateRangeAsync(
        Guid orgId, 
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a request with the given code exists within an organization
    /// </summary>
    Task<bool> ExistsByCodeAsync(Guid orgId, string requestCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a request with the given code exists within an organization (excluding the specified ID)
    /// </summary>
    Task<bool> ExistsByCodeAsync(Guid orgId, string requestCode, Guid excludeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new request
    /// </summary>
    Task AddAsync(Request request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing request
    /// </summary>
    void Update(Request request);

    /// <summary>
    /// Removes a request
    /// </summary>
    void Remove(Request request);

    /// <summary>
    /// Gets request approvals for a specific request
    /// </summary>
    Task<IEnumerable<RequestApproval>> GetApprovalsAsync(Guid requestId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets pending approvals for a specific approver
    /// </summary>
    Task<IEnumerable<RequestApproval>> GetPendingApprovalsAsync(Guid approverId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets overdue approvals within an organization
    /// </summary>
    Task<IEnumerable<RequestApproval>> GetOverdueApprovalsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets approval statistics for an approver
    /// </summary>
    Task<(int Pending, int Approved, int Rejected, double AverageResponseTimeHours)> GetApprovalStatsAsync(
        Guid approverId, 
        DateTime? fromDate = null, 
        CancellationToken cancellationToken = default);
}
