version: '3.4'

services:
  kantoku-api:
    image: kantoku-api-dev
    container_name: kantoku-api-dev
    build:
      context: ./Kantoku.Api
      dockerfile: Dockerfile
      args:
        - configuration=Debug
    ports:
      - 4869:4869
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - TZ=Asia/Tokyo
    volumes:
      - ./Logs:/app/Logs:rw

  kantoku-processor:
    image: kantoku-processor-dev
    container_name: kantoku-processor-dev
    build:
      context: ./Kantoku.Processor
      dockerfile: Dockerfile
      args:
        - configuration=Debug
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - TZ=Asia/Tokyo
    volumes:
      - ./Logs:/app/Logs:rw

