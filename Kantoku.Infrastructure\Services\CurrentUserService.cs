using System;
using System.Security.Claims;
using Kantoku.Application.Interfaces; // Points to the interface in Kantoku.Application
using Microsoft.AspNetCore.Http; // Added for IHttpContextAccessor

namespace Kantoku.Infrastructure.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        public Guid? UserId
        {
            get
            {
                var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (Guid.TryParse(userIdClaim, out var userId))
                {
                    return userId;
                }

                // Fallback: Check "sub" claim if NameIdentifier is not found or not a Guid
                var subClaim = _httpContextAccessor.HttpContext?.User?.FindFirst("sub")?.Value;
                if (Guid.TryParse(subClaim, out userId))
                {
                    return userId;
                }

                // Add more fallbacks if your user ID is stored in other standard or custom claims

                return null; // Or throw an exception if UserId is expected but not found, depending on application requirements
            }
        }
    }
}