using Kantoku.SharedKernel;

namespace Kantoku.Domain.EmployeeManagement.Enums;

/// <summary>
/// Enumeration representing employee types
/// </summary>
public class EmployeeType : Enumeration
{
    public static readonly EmployeeType Officer = new(1, nameof(Officer), "Officer");
    public static readonly EmployeeType Worker = new(2, nameof(Worker), "Worker");
    public static readonly EmployeeType Contractor = new(3, nameof(Contractor), "Contractor");
    public static readonly EmployeeType Intern = new(4, nameof(Intern), "Intern");
    public static readonly EmployeeType PartTime = new(5, nameof(PartTime), "Part-time");

    public string DisplayName { get; private set; }

    private EmployeeType(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<EmployeeType> GetAll()
    {
        return new[] { Officer, Worker, Contractor, Intern, PartTime };
    }

    public static EmployeeType FromName(string name)
    {
        var type = GetAll().FirstOrDefault(t => string.Equals(t.Name, name, StringComparison.OrdinalIgnoreCase));
        if (type == null)
            throw new ArgumentException($"Unknown employee type: {name}");
        return type;
    }

    public static EmployeeType FromId(int id)
    {
        var type = GetAll().FirstOrDefault(t => t.Id == id);
        if (type == null)
            throw new ArgumentException($"Unknown employee type ID: {id}");
        return type;
    }

    public bool IsFullTime => this == Officer || this == Worker;
    public bool IsTemporary => this == Contractor || this == Intern || this == PartTime;
}
