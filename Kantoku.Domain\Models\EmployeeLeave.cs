namespace Kantoku.Domain.Models;

public class EmployeeLeave : AuditableEntity
{
    public Guid EmployeeLeaveUid { get; set; }
    public Guid EmployeeUid { get; set; }


    public float BaseLeave { get; set; }

    public DateOnly BaseLeaveExpire { get; set; }


    public float? LastRemainLeave { get; set; }


    public DateOnly? LastRemainLeaveExpire { get; set; }


    public float SelfTakenLeave { get; set; } = 0.0f;


    public float OrgTakenLeave { get; set; } = 0.0f;

    public bool IsDeleted { get; set; } = false;

    public virtual Employee Employee { get; set; } = null!;
}
