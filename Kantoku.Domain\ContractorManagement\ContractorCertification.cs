using Kantoku.Domain.Common.BuildingBlocks;

namespace Kantoku.Domain.ContractorManagement;

/// <summary>
/// Entity representing a certification held by a contractor
/// </summary>
public class ContractorCertification : Entity<Guid>
{
    public Guid ContractorId { get; private set; }
    public string CertificationName { get; private set; } = null!;
    public string? CertificationNumber { get; private set; }
    public string? IssuingAuthority { get; private set; }
    public DateOnly IssueDate { get; private set; }
    public DateOnly? ExpiryDate { get; private set; }
    public string? Description { get; private set; }
    public string? DocumentUrl { get; private set; }
    public bool IsActive { get; private set; } = true;

    // Private constructor for EF Core
    private ContractorCertification() : base() { }

    /// <summary>
    /// Creates a new contractor certification
    /// </summary>
    public ContractorCertification(
        Guid id,
        Guid contractorId,
        string certificationName,
        DateOnly issueDate,
        string? certificationNumber = null,
        string? issuingAuthority = null,
        DateOnly? expiryDate = null,
        string? description = null,
        string? documentUrl = null) : base(id)
    {
        ContractorId = contractorId;
        SetCertificationName(certificationName);
        IssueDate = issueDate;
        CertificationNumber = certificationNumber?.Trim();
        IssuingAuthority = issuingAuthority?.Trim();
        ExpiryDate = expiryDate;
        Description = description?.Trim();
        DocumentUrl = documentUrl?.Trim();

        ValidateDates();
    }

    /// <summary>
    /// Updates certification information
    /// </summary>
    public void UpdateCertificationInfo(
        string certificationName,
        DateOnly issueDate,
        string? certificationNumber = null,
        string? issuingAuthority = null,
        DateOnly? expiryDate = null,
        string? description = null,
        string? documentUrl = null)
    {
        SetCertificationName(certificationName);
        IssueDate = issueDate;
        CertificationNumber = certificationNumber?.Trim();
        IssuingAuthority = issuingAuthority?.Trim();
        ExpiryDate = expiryDate;
        Description = description?.Trim();
        DocumentUrl = documentUrl?.Trim();

        ValidateDates();
    }

    /// <summary>
    /// Renews the certification with new expiry date
    /// </summary>
    public void Renew(DateOnly newExpiryDate, string? newCertificationNumber = null)
    {
        if (newExpiryDate <= DateOnly.FromDateTime(DateTime.Today))
            throw new ArgumentException("New expiry date must be in the future", nameof(newExpiryDate));

        ExpiryDate = newExpiryDate;
        if (!string.IsNullOrWhiteSpace(newCertificationNumber))
            CertificationNumber = newCertificationNumber.Trim();

        IsActive = true;
    }

    /// <summary>
    /// Deactivates the certification
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
    }

    /// <summary>
    /// Reactivates the certification
    /// </summary>
    public void Reactivate()
    {
        if (IsExpired)
            throw new InvalidOperationException("Cannot reactivate an expired certification");

        IsActive = true;
    }

    /// <summary>
    /// Checks if the certification is expired
    /// </summary>
    public bool IsExpired
    {
        get
        {
            if (!ExpiryDate.HasValue)
                return false;

            return ExpiryDate.Value <= DateOnly.FromDateTime(DateTime.Today);
        }
    }

    /// <summary>
    /// Checks if the certification is expiring soon (within 30 days)
    /// </summary>
    public bool IsExpiringSoon
    {
        get
        {
            if (!ExpiryDate.HasValue)
                return false;

            return ExpiryDate.Value <= DateOnly.FromDateTime(DateTime.Today.AddDays(30));
        }
    }

    /// <summary>
    /// Checks if the certification is valid (active and not expired)
    /// </summary>
    public bool IsValid => IsActive && !IsExpired;

    /// <summary>
    /// Gets the number of days until expiry
    /// </summary>
    public int? DaysUntilExpiry
    {
        get
        {
            if (!ExpiryDate.HasValue)
                return null;

            var today = DateOnly.FromDateTime(DateTime.Today);
            return ExpiryDate.Value.DayNumber - today.DayNumber;
        }
    }

    // Private helper methods
    private void SetCertificationName(string certificationName)
    {
        if (string.IsNullOrWhiteSpace(certificationName))
            throw new ArgumentException("Certification name cannot be null or empty", nameof(certificationName));

        if (certificationName.Length > 200)
            throw new ArgumentException("Certification name cannot exceed 200 characters", nameof(certificationName));

        CertificationName = certificationName.Trim();
    }

    private void ValidateDates()
    {
        if (ExpiryDate.HasValue && ExpiryDate.Value <= IssueDate)
            throw new ArgumentException("Expiry date must be after issue date");
    }
}
