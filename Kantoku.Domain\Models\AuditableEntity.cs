namespace Kantoku.Domain.Models;

public interface IAuditableEntity
{
    string? CreatedBy { get; set; }
    DateTime? CreatedTime { get; set; }
    string? LastModifiedBy { get; set; }
    DateTime? LastModifiedTime { get; set; }
}

public abstract class AuditableEntity : IAuditableEntity
{
    public string? CreatedBy { get; set; }
    public DateTime? CreatedTime { get; set; }
    public string? LastModifiedBy { get; set; }
    public DateTime? LastModifiedTime { get; set; }
}