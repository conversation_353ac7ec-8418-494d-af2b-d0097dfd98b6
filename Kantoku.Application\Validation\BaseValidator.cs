using FluentValidation;

namespace Kantoku.Application.Validation
{
    /// <summary>
    /// Base validator for all command validators. Provides common validation rules.
    /// </summary>
    /// <typeparam name="T">The type of object to validate</typeparam>
    public abstract class BaseValidator<T> : AbstractValidator<T>
    {
        // Base validation rules can be defined here that would apply to all validators
        protected BaseValidator()
        {
            // Example: add transformations that apply to all validators
            // RuleFor(x => x.SomeProperty).NotEmpty().MaximumLength(100).Trim();
        }

        /// <summary>
        /// Validates that a string has a valid date format (yyyy-MM-dd)
        /// </summary>
        /// <typeparam name="T">The type being validated</typeparam>
        /// <param name="rule">The rule builder</param>
        /// <returns>The rule builder</returns>
        protected static IRuleBuilderOptions<T, string> ValidDate<T>(IRuleBuilder<T, string> rule)
        {
            return rule.Must(date =>
            {
                if (string.IsNullOrEmpty(date))
                    return true; // Skip validation if the date is null (use .NotEmpty() if required)

                return DateTime.TryParseExact(date, "yyyy-MM-dd", System.Globalization.CultureInfo.InvariantCulture, 
                    System.Globalization.DateTimeStyles.None, out _);
            })
            .WithMessage("'{PropertyName}' must be a valid date in the format 'yyyy-MM-dd'.");
        }

        /// <summary>
        /// Validates that a string is a valid email address
        /// </summary>
        /// <typeparam name="T">The type being validated</typeparam>
        /// <param name="rule">The rule builder</param>
        /// <returns>The rule builder</returns>
        protected static IRuleBuilderOptions<T, string> ValidEmail<T>(IRuleBuilder<T, string> rule)
        {
            return rule.EmailAddress()
                .WithMessage("'{PropertyName}' must be a valid email address.");
        }
    }
} 