namespace Kantoku.SharedKernel.BuildingBlocks;

/// <summary>
/// Base class for value objects in Domain-Driven Design.
/// Value objects are immutable objects that are defined by their attributes rather than their identity.
/// Two value objects are considered equal if all their attributes are equal.
/// </summary>
public abstract class ValueObject
{
    /// <summary>
    /// Determines whether two value objects are equal
    /// </summary>
    /// <param name="a">The first value object</param>
    /// <param name="b">The second value object</param>
    /// <returns>True if the value objects are equal; otherwise, false</returns>
    public static bool operator ==(ValueObject? a, ValueObject? b)
    {
        if (a is null && b is null)
        {
            return true;
        }

        if (a is null || b is null)
        {
            return false;
        }

        return a.Equals(b);
    }

    /// <summary>
    /// Determines whether two value objects are not equal
    /// </summary>
    /// <param name="a">The first value object</param>
    /// <param name="b">The second value object</param>
    /// <returns>True if the value objects are not equal; otherwise, false</returns>
    public static bool operator !=(ValueObject? a, ValueObject? b) =>
        !(a == b);

    /// <summary>
    /// Determines whether this value object is equal to another value object
    /// </summary>
    /// <param name="other">The other value object</param>
    /// <returns>True if the value objects are equal; otherwise, false</returns>
    public virtual bool Equals(ValueObject? other) =>
        other is not null && ValuesAreEqual(other);

    /// <summary>
    /// Determines whether this value object is equal to the specified object
    /// </summary>
    /// <param name="obj">The object to compare</param>
    /// <returns>True if the objects are equal; otherwise, false</returns>
    public override bool Equals(object? obj) =>
        obj is ValueObject valueObject && ValuesAreEqual(valueObject);

    /// <summary>
    /// Returns the hash code for this value object
    /// </summary>
    /// <returns>A hash code for this value object</returns>
    public override int GetHashCode() =>
        GetEqualityComponents().Aggregate(
            default(int),
            (hashcode, value) =>
                HashCode.Combine(hashcode, value?.GetHashCode()));

    /// <summary>
    /// Gets the components that are used for equality comparison.
    /// Derived classes must implement this method to return all the components
    /// that should be considered when comparing two value objects for equality.
    /// </summary>
    /// <returns>An enumerable of objects that represent the equality components</returns>
    protected abstract IEnumerable<object?> GetEqualityComponents();

    /// <summary>
    /// Determines whether the values of this value object are equal to another value object
    /// </summary>
    /// <param name="valueObject">The other value object</param>
    /// <returns>True if the values are equal; otherwise, false</returns>
    private bool ValuesAreEqual(ValueObject valueObject) =>
        GetEqualityComponents().SequenceEqual(valueObject.GetEqualityComponents());
}
