namespace Kantoku.SharedKernel.BuildingBlocks;

/// <summary>
/// Base class for strongly-typed identifiers.
/// Provides type safety and prevents mixing different types of IDs.
/// </summary>
/// <typeparam name="T">The type that this ID represents</typeparam>
public abstract class StronglyTypedId<T> : ValueObject, IEquatable<StronglyTypedId<T>>
    where T : StronglyTypedId<T>
{
    /// <summary>
    /// Gets the underlying Guid value
    /// </summary>
    public Guid Value { get; }

    /// <summary>
    /// Initializes a new instance with the specified Guid value
    /// </summary>
    /// <param name="value">The Guid value</param>
    /// <exception cref="ArgumentException">Thrown when the value is empty</exception>
    protected StronglyTypedId(Guid value)
    {
        if (value == Guid.Empty)
        {
            throw new ArgumentException("ID cannot be empty", nameof(value));
        }
        Value = value;
    }

    /// <summary>
    /// Creates a new instance with a new Guid
    /// </summary>
    /// <returns>A new strongly-typed ID</returns>
    public static T New() => (T)Activator.CreateInstance(typeof(T), Guid.NewGuid())!;

    /// <summary>
    /// Creates an instance from a Guid value
    /// </summary>
    /// <param name="value">The Guid value</param>
    /// <returns>A strongly-typed ID</returns>
    public static T From(Guid value) => (T)Activator.CreateInstance(typeof(T), value)!;

    /// <summary>
    /// Implicit conversion to Guid
    /// </summary>
    /// <param name="id">The strongly-typed ID</param>
    public static implicit operator Guid(StronglyTypedId<T> id) => id.Value;

    /// <summary>
    /// Determines whether this ID is equal to another ID of the same type
    /// </summary>
    /// <param name="other">The other ID</param>
    /// <returns>True if the IDs are equal; otherwise, false</returns>
    public bool Equals(StronglyTypedId<T>? other) => other is not null && Value.Equals(other.Value);

    /// <summary>
    /// Returns the string representation of the ID
    /// </summary>
    /// <returns>The string representation of the underlying Guid</returns>
    public override string ToString() => Value.ToString();

    /// <summary>
    /// Gets the equality components for value object comparison
    /// </summary>
    /// <returns>The Guid value as the equality component</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Value;
    }
}

/// <summary>
/// Base class for value objects in Domain-Driven Design.
/// Value objects are immutable objects that are defined by their attributes rather than their identity.
/// Two value objects are considered equal if all their attributes are equal.
/// </summary>
public abstract class ValueObject
{
    /// <summary>
    /// Determines whether two value objects are equal
    /// </summary>
    /// <param name="a">The first value object</param>
    /// <param name="b">The second value object</param>
    /// <returns>True if the value objects are equal; otherwise, false</returns>
    public static bool operator ==(ValueObject? a, ValueObject? b)
    {
        if (a is null && b is null)
        {
            return true;
        }

        if (a is null || b is null)
        {
            return false;
        }

        return a.Equals(b);
    }

    /// <summary>
    /// Determines whether two value objects are not equal
    /// </summary>
    /// <param name="a">The first value object</param>
    /// <param name="b">The second value object</param>
    /// <returns>True if the value objects are not equal; otherwise, false</returns>
    public static bool operator !=(ValueObject? a, ValueObject? b) =>
        !(a == b);

    /// <summary>
    /// Determines whether this value object is equal to another value object
    /// </summary>
    /// <param name="other">The other value object</param>
    /// <returns>True if the value objects are equal; otherwise, false</returns>
    public virtual bool Equals(ValueObject? other) =>
        other is not null && ValuesAreEqual(other);

    /// <summary>
    /// Determines whether this value object is equal to the specified object
    /// </summary>
    /// <param name="obj">The object to compare</param>
    /// <returns>True if the objects are equal; otherwise, false</returns>
    public override bool Equals(object? obj) =>
        obj is ValueObject valueObject && ValuesAreEqual(valueObject);

    /// <summary>
    /// Returns the hash code for this value object
    /// </summary>
    /// <returns>A hash code for this value object</returns>
    public override int GetHashCode() =>
        GetEqualityComponents().Aggregate(
            default(int),
            (hashcode, value) =>
                HashCode.Combine(hashcode, value?.GetHashCode()));

    /// <summary>
    /// Gets the components that are used for equality comparison.
    /// Derived classes must implement this method to return all the components
    /// that should be considered when comparing two value objects for equality.
    /// </summary>
    /// <returns>An enumerable of objects that represent the equality components</returns>
    protected abstract IEnumerable<object?> GetEqualityComponents();

    /// <summary>
    /// Determines whether the values of this value object are equal to another value object
    /// </summary>
    /// <param name="valueObject">The other value object</param>
    /// <returns>True if the values are equal; otherwise, false</returns>
    private bool ValuesAreEqual(ValueObject valueObject) =>
        GetEqualityComponents().SequenceEqual(valueObject.GetEqualityComponents());
}
