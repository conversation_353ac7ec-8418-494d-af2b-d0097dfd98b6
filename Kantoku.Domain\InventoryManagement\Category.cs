using Kantoku.SharedKernel;

namespace Kantoku.Domain.InventoryManagement;

/// <summary>
/// Entity representing an item category with hierarchical structure
/// </summary>
public class Category : Entity<Guid>
{
    private readonly List<Category> _children = new();

    public Guid OrgId { get; private set; }
    public Guid? ParentId { get; private set; }
    public string CategoryCode { get; private set; } = null!;
    public string CategoryName { get; private set; } = null!;
    public string? Description { get; private set; }
    public string CategoryType { get; private set; } = "GENERAL"; // Based on CategoryConstant from API
    public bool IsActive { get; private set; } = true;
    public bool IsDefault { get; private set; } = false;
    public int SortOrder { get; private set; } = 0;

    // Navigation properties
    public Category? Parent { get; private set; }
    public IReadOnlyCollection<Category> Children => _children.AsReadOnly();

    // Private constructor for EF Core
    private Category() : base() { }

    /// <summary>
    /// Creates a new category
    /// </summary>
    public Category(
        Guid id,
        Guid orgId,
        string categoryCode,
        string categoryName,
        string categoryType = "GENERAL",
        string? description = null,
        Guid? parentId = null,
        int sortOrder = 0) : base(id)
    {
        OrgId = orgId;
        SetCategoryCode(categoryCode);
        SetCategoryName(categoryName);
        SetCategoryType(categoryType);
        Description = description;
        ParentId = parentId;
        SortOrder = sortOrder;
    }

    /// <summary>
    /// Updates category information
    /// </summary>
    public void Update(
        string categoryName,
        string? description = null,
        int? sortOrder = null)
    {
        SetCategoryName(categoryName);
        Description = description;
        
        if (sortOrder.HasValue)
            SortOrder = sortOrder.Value;
    }

    /// <summary>
    /// Updates the category type
    /// </summary>
    public void UpdateCategoryType(string categoryType)
    {
        SetCategoryType(categoryType);
    }

    /// <summary>
    /// Sets the parent category
    /// </summary>
    public void SetParent(Category? parent)
    {
        if (parent != null)
        {
            if (parent.OrgId != OrgId)
                throw new InvalidOperationException("Parent category must belong to the same organization");

            if (parent.Id.Equals(Id))
                throw new InvalidOperationException("Category cannot be its own parent");

            if (IsDescendantOf(parent))
                throw new InvalidOperationException("Cannot set a descendant as parent (circular reference)");
        }

        Parent = parent;
        ParentId = parent?.Id;
    }

    /// <summary>
    /// Adds a child category
    /// </summary>
    public void AddChild(Category child)
    {
        if (child == null)
            throw new ArgumentNullException(nameof(child));

        if (child.OrgId != OrgId)
            throw new InvalidOperationException("Child category must belong to the same organization");

        if (child.Id.Equals(Id))
            throw new InvalidOperationException("Category cannot be its own child");

        if (_children.Any(c => c.Id.Equals(child.Id)))
            throw new InvalidOperationException("Child category already exists");

        child.SetParent(this);
        _children.Add(child);
    }

    /// <summary>
    /// Removes a child category
    /// </summary>
    public void RemoveChild(Category child)
    {
        if (child == null)
            throw new ArgumentNullException(nameof(child));

        if (_children.Remove(child))
        {
            child.SetParent(null);
        }
    }

    /// <summary>
    /// Activates the category
    /// </summary>
    public void Activate()
    {
        IsActive = true;
    }

    /// <summary>
    /// Deactivates the category
    /// </summary>
    public void Deactivate()
    {
        if (_children.Any(c => c.IsActive))
            throw new InvalidOperationException("Cannot deactivate category with active children");

        IsActive = false;
    }

    /// <summary>
    /// Sets the category as default
    /// </summary>
    public void SetAsDefault()
    {
        IsDefault = true;
    }

    /// <summary>
    /// Unsets the category as default
    /// </summary>
    public void UnsetAsDefault()
    {
        IsDefault = false;
    }

    /// <summary>
    /// Updates the sort order
    /// </summary>
    public void UpdateSortOrder(int sortOrder)
    {
        SortOrder = sortOrder;
    }

    /// <summary>
    /// Gets the full path of the category (e.g., "Parent > Child > Grandchild")
    /// </summary>
    public string GetFullPath(string separator = " > ")
    {
        var path = new List<string>();
        var current = this;
        
        while (current != null)
        {
            path.Insert(0, current.CategoryName);
            current = current.Parent;
        }
        
        return string.Join(separator, path);
    }

    /// <summary>
    /// Gets the depth level of the category (root = 0)
    /// </summary>
    public int GetDepthLevel()
    {
        var level = 0;
        var current = Parent;
        
        while (current != null)
        {
            level++;
            current = current.Parent;
        }
        
        return level;
    }

    /// <summary>
    /// Gets all descendant categories
    /// </summary>
    public IEnumerable<Category> GetAllDescendants()
    {
        var descendants = new List<Category>();
        
        foreach (var child in _children)
        {
            descendants.Add(child);
            descendants.AddRange(child.GetAllDescendants());
        }
        
        return descendants;
    }

    /// <summary>
    /// Gets all ancestor categories
    /// </summary>
    public IEnumerable<Category> GetAllAncestors()
    {
        var ancestors = new List<Category>();
        var current = Parent;
        
        while (current != null)
        {
            ancestors.Add(current);
            current = current.Parent;
        }
        
        return ancestors;
    }

    /// <summary>
    /// Checks if this category is a root category
    /// </summary>
    public bool IsRoot => Parent == null;

    /// <summary>
    /// Checks if this category is a leaf category (has no children)
    /// </summary>
    public bool IsLeaf => !_children.Any();

    /// <summary>
    /// Checks if this category has children
    /// </summary>
    public bool HasChildren => _children.Any();

    /// <summary>
    /// Checks if this category is a descendant of the specified category
    /// </summary>
    private bool IsDescendantOf(Category category)
    {
        var current = Parent;
        while (current != null)
        {
            if (current.Id.Equals(category.Id))
                return true;
            current = current.Parent;
        }
        return false;
    }

    // Private helper methods
    private void SetCategoryCode(string categoryCode)
    {
        if (string.IsNullOrWhiteSpace(categoryCode))
            throw new ArgumentException("Category code cannot be null or empty", nameof(categoryCode));

        if (categoryCode.Length > 50)
            throw new ArgumentException("Category code cannot exceed 50 characters", nameof(categoryCode));

        CategoryCode = categoryCode.Trim();
    }

    private void SetCategoryName(string categoryName)
    {
        if (string.IsNullOrWhiteSpace(categoryName))
            throw new ArgumentException("Category name cannot be null or empty", nameof(categoryName));

        if (categoryName.Length > 200)
            throw new ArgumentException("Category name cannot exceed 200 characters", nameof(categoryName));

        CategoryName = categoryName.Trim();
    }

    private void SetCategoryType(string categoryType)
    {
        if (string.IsNullOrWhiteSpace(categoryType))
            throw new ArgumentException("Category type cannot be null or empty", nameof(categoryType));

        // Based on CategoryConstant from the API
        var validTypes = new[] { 
            "EMPLOYEE", "OUTSOURCE_DAILY", "OUTSOURCE_CONTRACT", "OVERTIME", 
            "MATERIAL", "FUEL", "EQUIPMENT", "LEASE", "SERVICE", "OTHER", "GENERAL" 
        };
        
        if (!validTypes.Contains(categoryType.ToUpperInvariant()))
            throw new ArgumentException($"Invalid category type: {categoryType}. Valid types are: {string.Join(", ", validTypes)}");

        CategoryType = categoryType.ToUpperInvariant();
    }

    public override string ToString()
    {
        return $"{CategoryCode} - {CategoryName}" + (IsRoot ? "" : $" (under {Parent?.CategoryName})");
    }
}
