﻿namespace Kantoku.Domain.Models;

public class Manufacturer : AuditableEntity
{
    public Guid ManufacturerUid { get; set; }


    public string ManufacturerCode { get; set; } = null!;


    public string ManufacturerName { get; set; } = null!;


    public string? ManufacturerSubName { get; set; }


    public string? CorporateNumber { get; set; }


    public string? Address { get; set; }


    public string? PhoneNumber { get; set; }


    public string? Email { get; set; }


    public ContactPerson? ContactPerson { get; set; }


    public string? Description { get; set; }

    public bool IsDeleted { get; set; }

    public Guid OrgUid { get; set; }

    public string? LogoUrl { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<Item> Items { get; set; } = [];
}
