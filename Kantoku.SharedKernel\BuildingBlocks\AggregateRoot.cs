namespace Kantoku.SharedKernel.BuildingBlocks;

/// <summary>
/// Base class for aggregate roots with Guid identifiers.
/// Aggregate roots are the only entities that can be directly accessed from outside the aggregate.
/// They are responsible for maintaining the consistency of the entire aggregate.
/// </summary>
public abstract class AggregateRoot : Entity, IAggregateRoot
{
    /// <summary>
    /// Initializes a new instance of the AggregateRoot class with a new Guid
    /// </summary>
    protected AggregateRoot() : base()
    {
    }

    /// <summary>
    /// Initializes a new instance of the AggregateRoot class with the specified identifier
    /// </summary>
    /// <param name="id">The unique identifier for this aggregate root</param>
    protected AggregateRoot(Guid id) : base(id)
    {
    }
}

/// <summary>
/// Base class for aggregate roots with strongly-typed identifiers.
/// Aggregate roots are the only entities that can be directly accessed from outside the aggregate.
/// They are responsible for maintaining the consistency of the entire aggregate.
/// </summary>
/// <typeparam name="TId">The type of the aggregate root identifier</typeparam>
public abstract class AggregateRoot<TId> : Entity<TId>, IAggregateRoot where TId : IEquatable<TId>
{
    /// <summary>
    /// Initializes a new instance of the AggregateRoot class with the specified identifier
    /// </summary>
    /// <param name="id">The unique identifier for this aggregate root</param>
    protected AggregateRoot(TId id) : base(id)
    {
    }
}
