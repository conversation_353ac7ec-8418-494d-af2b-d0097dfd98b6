using Kantoku.SharedKernel;

namespace Kantoku.Domain.OrganizationManagement.ValueObjects;

/// <summary>
/// Value object representing organization legal information
/// </summary>
public class LegalInformation : ValueObject
{
    public string? RegistrationNumber { get; private set; }
    public DateOnly? RegistrationDate { get; private set; }
    public bool? RegistrationLicenseType { get; private set; }
    public string? LegalOrgNumber { get; private set; }
    public string? LegalTaxNumber { get; private set; }
    public string? LegalRepresentative { get; private set; }

    private LegalInformation() { } // For EF Core

    public LegalInformation(
        string? registrationNumber = null,
        DateOnly? registrationDate = null,
        bool? registrationLicenseType = null,
        string? legalOrgNumber = null,
        string? legalTaxNumber = null,
        string? legalRepresentative = null)
    {
        RegistrationNumber = registrationNumber?.Trim();
        RegistrationDate = registrationDate;
        RegistrationLicenseType = registrationLicenseType;
        LegalOrgNumber = legalOrgNumber?.Trim();
        LegalTaxNumber = legalTaxNumber?.Trim();
        LegalRepresentative = legalRepresentative?.Trim();

        Validate();
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return RegistrationNumber;
        yield return RegistrationDate;
        yield return RegistrationLicenseType;
        yield return LegalOrgNumber;
        yield return LegalTaxNumber;
        yield return LegalRepresentative;
    }

    private void Validate()
    {
        if (!string.IsNullOrEmpty(RegistrationNumber) && RegistrationNumber.Length > 50)
            throw new ArgumentException("Registration number cannot exceed 50 characters");

        if (RegistrationDate.HasValue && RegistrationDate.Value > DateOnly.FromDateTime(DateTime.Today))
            throw new ArgumentException("Registration date cannot be in the future");

        if (!string.IsNullOrEmpty(LegalOrgNumber) && LegalOrgNumber.Length > 50)
            throw new ArgumentException("Legal organization number cannot exceed 50 characters");

        if (!string.IsNullOrEmpty(LegalTaxNumber) && LegalTaxNumber.Length > 50)
            throw new ArgumentException("Legal tax number cannot exceed 50 characters");

        if (!string.IsNullOrEmpty(LegalRepresentative) && LegalRepresentative.Length > 100)
            throw new ArgumentException("Legal representative name cannot exceed 100 characters");
    }
}
