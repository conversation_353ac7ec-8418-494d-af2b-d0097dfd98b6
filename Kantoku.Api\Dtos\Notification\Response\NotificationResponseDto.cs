using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Notification.Response;

public class NotificationsResponseDto : PagingResponseDto<NotificationResponseDto>
{
}

public class NotificationResponseDto
{
    public string? NotificationId { get; set; }
    public string? Title { get; set; }
    public string? Body { get; set; }
    public string? NotificationType { get; set; }
    public IEnumerable<NotificationTargetResponseDto> Targets { get; set; } = [];
}

public class NotificationTargetResponseDto
{
    public string? NotificationTargetUid { get; set; }
    public string? TargetType { get; set; }
    public ICollection<Guid>? TargetIds { get; set; }
    public string? PublishStatus { get; set; }
    public DateTime? PublishAt { get; set; }
}
