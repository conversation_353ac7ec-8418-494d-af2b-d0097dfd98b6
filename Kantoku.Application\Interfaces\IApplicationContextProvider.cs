// Using the ApplicationContext defined in Kantoku.Application
// No direct using needed if they are in the same root namespace for the project, 
// but good for clarity if ApplicationContext were in a sub-namespace.
// using Kantoku.Application; 

namespace Kantoku.Application.Interfaces
{
    /// <summary>
    /// Provides access to the current ApplicationContext.
    /// </summary>
    public interface IApplicationContextProvider
    {
        /// <summary>
        /// Gets the current ApplicationContext. Returns null if no context is set.
        /// </summary>
        ApplicationContext? Context { get; }
    }
} 