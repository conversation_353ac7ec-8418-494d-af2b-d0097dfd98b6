using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.AccountManagement.Events;
using Kantoku.Domain.AccountManagement.ValueObjects;
using Kantoku.Domain.AccountManagement.Enums;

namespace Kantoku.Domain.AccountManagement;

/// <summary>
/// Account aggregate root representing a user account in the system
/// </summary>
public class Account : FullAuditedEntity<Guid>
{
    private readonly List<UserRole> _userRoles = new();
    private readonly List<AccountSession> _sessions = new();

    public string Username { get; private set; } = null!;
    public string Email { get; private set; } = null!;
    public string? PhoneNumber { get; private set; }
    public string PasswordHash { get; private set; } = null!;
    public string? PasswordSalt { get; private set; }
    
    public AccountStatus Status { get; private set; } = AccountStatus.Active;
    public DateTime? LastLoginDate { get; private set; }
    public DateTime? PasswordChangedDate { get; private set; }
    public int FailedLoginAttempts { get; private set; } = 0;
    public DateTime? LockedUntil { get; private set; }
    
    public bool EmailVerified { get; private set; } = false;
    public bool PhoneVerified { get; private set; } = false;
    public bool TwoFactorEnabled { get; private set; } = false;
    public string? TwoFactorSecret { get; private set; }
    
    public UserPreferences? Preferences { get; private set; }
    public string? ProfileImageUrl { get; private set; }
    public string? TimeZone { get; private set; }
    public string? Language { get; private set; }

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<UserRole> UserRoles => _userRoles.AsReadOnly();
    public IReadOnlyCollection<AccountSession> Sessions => _sessions.AsReadOnly();

    // Private constructor for EF Core
    private Account() : base() { }

    /// <summary>
    /// Creates a new account
    /// </summary>
    public Account(
        Guid id,
        string username,
        string email,
        string passwordHash,
        string? passwordSalt = null,
        string? phoneNumber = null,
        string? timeZone = null,
        string? language = null) : base(id)
    {
        SetUsername(username);
        SetEmail(email);
        SetPasswordHash(passwordHash, passwordSalt);
        PhoneNumber = phoneNumber?.Trim();
        TimeZone = timeZone?.Trim();
        Language = language?.Trim();
        PasswordChangedDate = DateTime.UtcNow;

        AddDomainEvent(new AccountCreatedEvent(this));
    }

    /// <summary>
    /// Updates basic account information
    /// </summary>
    public void UpdateBasicInfo(
        string email,
        string? phoneNumber = null,
        string? timeZone = null,
        string? language = null,
        string? profileImageUrl = null)
    {
        SetEmail(email);
        PhoneNumber = phoneNumber?.Trim();
        TimeZone = timeZone?.Trim();
        Language = language?.Trim();
        ProfileImageUrl = profileImageUrl?.Trim();

        AddDomainEvent(new AccountUpdatedEvent(this));
    }

    /// <summary>
    /// Changes the account password
    /// </summary>
    public void ChangePassword(string newPasswordHash, string? newPasswordSalt = null)
    {
        SetPasswordHash(newPasswordHash, newPasswordSalt);
        PasswordChangedDate = DateTime.UtcNow;
        FailedLoginAttempts = 0; // Reset failed attempts on password change
        LockedUntil = null; // Unlock account if it was locked

        AddDomainEvent(new AccountPasswordChangedEvent(this));
    }

    /// <summary>
    /// Records a successful login
    /// </summary>
    public void RecordSuccessfulLogin()
    {
        LastLoginDate = DateTime.UtcNow;
        FailedLoginAttempts = 0;
        LockedUntil = null;

        AddDomainEvent(new AccountLoginEvent(this, true));
    }

    /// <summary>
    /// Records a failed login attempt
    /// </summary>
    public void RecordFailedLogin()
    {
        FailedLoginAttempts++;
        
        // Lock account after 5 failed attempts for 30 minutes
        if (FailedLoginAttempts >= 5)
        {
            LockedUntil = DateTime.UtcNow.AddMinutes(30);
            AddDomainEvent(new AccountLockedEvent(this, "Too many failed login attempts"));
        }

        AddDomainEvent(new AccountLoginEvent(this, false));
    }

    /// <summary>
    /// Locks the account
    /// </summary>
    public void Lock(string reason, DateTime? lockUntil = null)
    {
        Status = AccountStatus.Locked;
        LockedUntil = lockUntil ?? DateTime.UtcNow.AddDays(30); // Default 30 days

        AddDomainEvent(new AccountLockedEvent(this, reason));
    }

    /// <summary>
    /// Unlocks the account
    /// </summary>
    public void Unlock()
    {
        if (Status == AccountStatus.Locked)
        {
            Status = AccountStatus.Active;
            LockedUntil = null;
            FailedLoginAttempts = 0;

            AddDomainEvent(new AccountUnlockedEvent(this));
        }
    }

    /// <summary>
    /// Suspends the account
    /// </summary>
    public void Suspend(string reason)
    {
        Status = AccountStatus.Suspended;
        AddDomainEvent(new AccountSuspendedEvent(this, reason));
    }

    /// <summary>
    /// Activates the account
    /// </summary>
    public void Activate()
    {
        if (Status != AccountStatus.Active)
        {
            Status = AccountStatus.Active;
            LockedUntil = null;
            AddDomainEvent(new AccountActivatedEvent(this));
        }
    }

    /// <summary>
    /// Deactivates the account
    /// </summary>
    public void Deactivate(string reason)
    {
        Status = AccountStatus.Inactive;
        AddDomainEvent(new AccountDeactivatedEvent(this, reason));
    }

    /// <summary>
    /// Verifies the email address
    /// </summary>
    public void VerifyEmail()
    {
        if (!EmailVerified)
        {
            EmailVerified = true;
            AddDomainEvent(new AccountEmailVerifiedEvent(this));
        }
    }

    /// <summary>
    /// Verifies the phone number
    /// </summary>
    public void VerifyPhone()
    {
        if (!PhoneVerified)
        {
            PhoneVerified = true;
            AddDomainEvent(new AccountPhoneVerifiedEvent(this));
        }
    }

    /// <summary>
    /// Enables two-factor authentication
    /// </summary>
    public void EnableTwoFactor(string secret)
    {
        if (string.IsNullOrWhiteSpace(secret))
            throw new ArgumentException("Two-factor secret cannot be null or empty", nameof(secret));

        TwoFactorEnabled = true;
        TwoFactorSecret = secret;

        AddDomainEvent(new AccountTwoFactorEnabledEvent(this));
    }

    /// <summary>
    /// Disables two-factor authentication
    /// </summary>
    public void DisableTwoFactor()
    {
        if (TwoFactorEnabled)
        {
            TwoFactorEnabled = false;
            TwoFactorSecret = null;

            AddDomainEvent(new AccountTwoFactorDisabledEvent(this));
        }
    }

    /// <summary>
    /// Updates user preferences
    /// </summary>
    public void UpdatePreferences(UserPreferences preferences)
    {
        Preferences = preferences;
        AddDomainEvent(new AccountPreferencesUpdatedEvent(this));
    }

    /// <summary>
    /// Adds a role to the user
    /// </summary>
    public void AddRole(UserRole userRole)
    {
        if (userRole == null)
            throw new ArgumentNullException(nameof(userRole));

        if (_userRoles.Any(ur => ur.RoleId == userRole.RoleId))
            throw new InvalidOperationException("User already has this role");

        _userRoles.Add(userRole);
        AddDomainEvent(new AccountRoleAddedEvent(this, userRole));
    }

    /// <summary>
    /// Removes a role from the user
    /// </summary>
    public void RemoveRole(Guid roleId)
    {
        var userRole = _userRoles.FirstOrDefault(ur => ur.RoleId == roleId);
        if (userRole != null)
        {
            _userRoles.Remove(userRole);
            AddDomainEvent(new AccountRoleRemovedEvent(this, userRole));
        }
    }

    /// <summary>
    /// Checks if the account can login
    /// </summary>
    public bool CanLogin => Status == AccountStatus.Active && 
                           (LockedUntil == null || LockedUntil <= DateTime.UtcNow);

    /// <summary>
    /// Checks if the account is locked
    /// </summary>
    public bool IsLocked => Status == AccountStatus.Locked || 
                           (LockedUntil.HasValue && LockedUntil > DateTime.UtcNow);

    /// <summary>
    /// Gets the display name for the account
    /// </summary>
    public string DisplayName => Username;

    // Private helper methods
    private void SetUsername(string username)
    {
        if (string.IsNullOrWhiteSpace(username))
            throw new ArgumentException("Username cannot be null or empty", nameof(username));

        if (username.Length < 3 || username.Length > 50)
            throw new ArgumentException("Username must be between 3 and 50 characters", nameof(username));

        Username = username.Trim().ToLowerInvariant();
    }

    private void SetEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be null or empty", nameof(email));

        if (email.Length > 255)
            throw new ArgumentException("Email cannot exceed 255 characters", nameof(email));

        if (!IsValidEmail(email))
            throw new ArgumentException("Invalid email format", nameof(email));

        Email = email.Trim().ToLowerInvariant();
    }

    private void SetPasswordHash(string passwordHash, string? passwordSalt)
    {
        if (string.IsNullOrWhiteSpace(passwordHash))
            throw new ArgumentException("Password hash cannot be null or empty", nameof(passwordHash));

        PasswordHash = passwordHash;
        PasswordSalt = passwordSalt;
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
