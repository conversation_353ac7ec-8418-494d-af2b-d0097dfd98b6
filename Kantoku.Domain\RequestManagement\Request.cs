using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.RequestManagement.ValueObjects;
using Kantoku.Domain.RequestManagement.Events;
using Kantoku.Domain.RequestManagement.Enums;

namespace Kantoku.Domain.RequestManagement;

/// <summary>
/// Request aggregate root representing various types of requests in the system
/// </summary>
public class Request : FullAuditedEntity<Guid>
{
    private readonly List<RequestApproval> _approvals = new();

    public Guid OrgId { get; private set; }
    public Guid RequesterId { get; private set; }
    public string RequestCode { get; private set; } = null!;
    public RequestType RequestType { get; private set; } = null!;
    public string Title { get; private set; } = null!;
    public string? Description { get; private set; }
    public RequestStatus Status { get; private set; } = RequestStatus.Draft;
    public RequestPriority Priority { get; private set; } = RequestPriority.Normal;
    
    public RequestDates Dates { get; private set; } = null!;
    public RequestDetails? Details { get; private set; }
    
    public DateTime? SubmittedDate { get; private set; }
    public DateTime? CompletedDate { get; private set; }
    public string? CompletionNotes { get; private set; }

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<RequestApproval> Approvals => _approvals.AsReadOnly();

    // Private constructor for EF Core
    private Request() : base() { }

    /// <summary>
    /// Creates a new request
    /// </summary>
    public Request(
        Guid id,
        Guid orgId,
        Guid requesterId,
        string requestCode,
        RequestType requestType,
        string title,
        RequestDates dates,
        string? description = null,
        RequestPriority priority = null) : base(id)
    {
        OrgId = orgId;
        RequesterId = requesterId;
        SetRequestCode(requestCode);
        RequestType = requestType ?? throw new ArgumentNullException(nameof(requestType));
        SetTitle(title);
        Dates = dates ?? throw new ArgumentNullException(nameof(dates));
        Description = description;
        Priority = priority ?? RequestPriority.Normal;

        AddDomainEvent(new RequestCreatedEvent(this));
    }

    /// <summary>
    /// Updates basic request information
    /// </summary>
    public void UpdateBasicInfo(
        string title,
        string? description = null,
        RequestPriority priority = null)
    {
        if (Status != RequestStatus.Draft)
            throw new InvalidOperationException("Can only update basic info for draft requests");

        SetTitle(title);
        Description = description;
        Priority = priority ?? Priority;

        AddDomainEvent(new RequestUpdatedEvent(this));
    }

    /// <summary>
    /// Updates request dates
    /// </summary>
    public void UpdateDates(RequestDates dates)
    {
        if (Status == RequestStatus.Completed || Status == RequestStatus.Cancelled)
            throw new InvalidOperationException("Cannot update dates for completed or cancelled requests");

        Dates = dates ?? throw new ArgumentNullException(nameof(dates));
        AddDomainEvent(new RequestDatesUpdatedEvent(this));
    }

    /// <summary>
    /// Updates request details
    /// </summary>
    public void UpdateDetails(RequestDetails details)
    {
        if (Status != RequestStatus.Draft && Status != RequestStatus.Submitted)
            throw new InvalidOperationException("Can only update details for draft or submitted requests");

        Details = details;
        AddDomainEvent(new RequestDetailsUpdatedEvent(this));
    }

    /// <summary>
    /// Submits the request for approval
    /// </summary>
    public void Submit()
    {
        if (Status != RequestStatus.Draft)
            throw new InvalidOperationException("Can only submit draft requests");

        ValidateForSubmission();

        Status = RequestStatus.Submitted;
        SubmittedDate = DateTime.UtcNow;

        AddDomainEvent(new RequestSubmittedEvent(this));
    }

    /// <summary>
    /// Withdraws the request (returns to draft)
    /// </summary>
    public void Withdraw()
    {
        if (Status != RequestStatus.Submitted)
            throw new InvalidOperationException("Can only withdraw submitted requests");

        Status = RequestStatus.Draft;
        SubmittedDate = null;

        AddDomainEvent(new RequestWithdrawnEvent(this));
    }

    /// <summary>
    /// Adds an approval step to the request
    /// </summary>
    public void AddApproval(RequestApproval approval)
    {
        if (approval == null)
            throw new ArgumentNullException(nameof(approval));

        if (Status != RequestStatus.Submitted && Status != RequestStatus.InReview)
            throw new InvalidOperationException("Can only add approvals to submitted or in-review requests");

        if (_approvals.Any(a => a.ApproverId == approval.ApproverId && a.Status == ApprovalStatus.Pending))
            throw new InvalidOperationException("Approver already has a pending approval for this request");

        _approvals.Add(approval);
        
        if (Status == RequestStatus.Submitted)
        {
            Status = RequestStatus.InReview;
        }

        AddDomainEvent(new RequestApprovalAddedEvent(this, approval));
    }

    /// <summary>
    /// Processes an approval decision
    /// </summary>
    public void ProcessApproval(Guid approverId, bool isApproved, string? comments = null)
    {
        var approval = _approvals.FirstOrDefault(a => a.ApproverId == approverId && a.Status == ApprovalStatus.Pending);
        if (approval == null)
            throw new InvalidOperationException("No pending approval found for this approver");

        if (isApproved)
        {
            approval.Approve(comments);
            AddDomainEvent(new RequestApprovedEvent(this, approval));

            // Check if all required approvals are complete
            if (AreAllApprovalsComplete())
            {
                Status = RequestStatus.Approved;
                AddDomainEvent(new RequestFullyApprovedEvent(this));
            }
        }
        else
        {
            approval.Reject(comments);
            Status = RequestStatus.Rejected;
            AddDomainEvent(new RequestRejectedEvent(this, approval));
        }
    }

    /// <summary>
    /// Completes the request
    /// </summary>
    public void Complete(string? completionNotes = null)
    {
        if (Status != RequestStatus.Approved)
            throw new InvalidOperationException("Can only complete approved requests");

        Status = RequestStatus.Completed;
        CompletedDate = DateTime.UtcNow;
        CompletionNotes = completionNotes;

        AddDomainEvent(new RequestCompletedEvent(this));
    }

    /// <summary>
    /// Cancels the request
    /// </summary>
    public void Cancel(string? reason = null)
    {
        if (Status == RequestStatus.Completed)
            throw new InvalidOperationException("Cannot cancel completed requests");

        Status = RequestStatus.Cancelled;
        CompletionNotes = reason;

        AddDomainEvent(new RequestCancelledEvent(this));
    }

    /// <summary>
    /// Checks if the request is overdue
    /// </summary>
    public bool IsOverdue
    {
        get
        {
            if (Status == RequestStatus.Completed || Status == RequestStatus.Cancelled)
                return false;

            return Dates.DueDate.HasValue && Dates.DueDate.Value < DateTime.Today;
        }
    }

    /// <summary>
    /// Checks if the request requires approval
    /// </summary>
    public bool RequiresApproval => RequestType.RequiresApproval;

    /// <summary>
    /// Gets the current approver (if any)
    /// </summary>
    public Guid? GetCurrentApproverId()
    {
        return _approvals.FirstOrDefault(a => a.Status == ApprovalStatus.Pending)?.ApproverId;
    }

    /// <summary>
    /// Checks if all required approvals are complete
    /// </summary>
    private bool AreAllApprovalsComplete()
    {
        return _approvals.All(a => a.Status != ApprovalStatus.Pending);
    }

    /// <summary>
    /// Validates the request before submission
    /// </summary>
    private void ValidateForSubmission()
    {
        if (string.IsNullOrWhiteSpace(Title))
            throw new InvalidOperationException("Title is required for submission");

        if (RequestType.RequiresDetails && Details == null)
            throw new InvalidOperationException("Details are required for this request type");

        if (Dates.StartDate.HasValue && Dates.EndDate.HasValue && Dates.StartDate > Dates.EndDate)
            throw new InvalidOperationException("Start date cannot be after end date");
    }

    // Private helper methods
    private void SetRequestCode(string requestCode)
    {
        if (string.IsNullOrWhiteSpace(requestCode))
            throw new ArgumentException("Request code cannot be null or empty", nameof(requestCode));

        if (requestCode.Length > 50)
            throw new ArgumentException("Request code cannot exceed 50 characters", nameof(requestCode));

        RequestCode = requestCode.Trim();
    }

    private void SetTitle(string title)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be null or empty", nameof(title));

        if (title.Length > 200)
            throw new ArgumentException("Title cannot exceed 200 characters", nameof(title));

        Title = title.Trim();
    }
}
