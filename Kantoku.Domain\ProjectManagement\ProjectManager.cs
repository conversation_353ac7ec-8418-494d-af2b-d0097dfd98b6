using Kantoku.SharedKernel;

namespace Kantoku.Domain.ProjectManagement;

/// <summary>
/// Entity representing a project manager assignment
/// </summary>
public class ProjectManager : Entity<Guid>
{
    public Guid ProjectId { get; private set; }
    public Guid EmployeeId { get; private set; }
    public DateTime AssignedDate { get; private set; }
    public DateTime? RemovedDate { get; private set; }
    public bool IsActive { get; private set; } = true;
    public bool IsPrimaryManager { get; private set; } = false;
    public string? Responsibilities { get; private set; }

    // Private constructor for EF Core
    private ProjectManager() : base() { }

    /// <summary>
    /// Creates a new project manager assignment
    /// </summary>
    public ProjectManager(
        Guid id,
        Guid projectId,
        Guid employeeId,
        bool isPrimaryManager = false,
        string? responsibilities = null,
        DateTime? assignedDate = null) : base(id)
    {
        ProjectId = projectId;
        EmployeeId = employeeId;
        IsPrimaryManager = isPrimaryManager;
        Responsibilities = responsibilities;
        AssignedDate = assignedDate ?? DateTime.UtcNow;
    }

    /// <summary>
    /// Updates manager responsibilities
    /// </summary>
    public void UpdateResponsibilities(string? responsibilities)
    {
        if (!IsActive)
            throw new InvalidOperationException("Cannot update responsibilities for inactive manager");

        Responsibilities = responsibilities;
    }

    /// <summary>
    /// Sets as primary manager
    /// </summary>
    public void SetAsPrimary()
    {
        if (!IsActive)
            throw new InvalidOperationException("Cannot set inactive manager as primary");

        IsPrimaryManager = true;
    }

    /// <summary>
    /// Removes primary manager status
    /// </summary>
    public void RemovePrimaryStatus()
    {
        IsPrimaryManager = false;
    }

    /// <summary>
    /// Removes the manager from the project
    /// </summary>
    public void Remove()
    {
        if (!IsActive)
            throw new InvalidOperationException("Manager is already removed");

        IsActive = false;
        RemovedDate = DateTime.UtcNow;
        IsPrimaryManager = false;
    }

    /// <summary>
    /// Reactivates the manager assignment
    /// </summary>
    public void Reactivate()
    {
        if (IsActive)
            throw new InvalidOperationException("Manager is already active");

        IsActive = true;
        RemovedDate = null;
    }
}
