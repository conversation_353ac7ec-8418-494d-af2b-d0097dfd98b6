using Kantoku.SharedKernel;

namespace Kantoku.Domain.RequestManagement.ValueObjects;

/// <summary>
/// Value object representing detailed information for specific request types
/// </summary>
public class RequestDetails : ValueObject
{
    public string RequestTypeCode { get; private set; } = null!;
    public Dictionary<string, object> Properties { get; private set; } = new();

    private RequestDetails() { } // For EF Core

    public RequestDetails(string requestTypeCode, Dictionary<string, object>? properties = null)
    {
        SetRequestTypeCode(requestTypeCode);
        Properties = properties ?? new Dictionary<string, object>();
        ValidateProperties();
    }

    /// <summary>
    /// Creates leave request details
    /// </summary>
    public static RequestDetails CreateLeaveDetails(
        string leaveTypeCode,
        string? reason = null,
        bool isHalfDay = false,
        string? emergencyContact = null)
    {
        var properties = new Dictionary<string, object>
        {
            ["LeaveTypeCode"] = leaveTypeCode,
            ["IsHalfDay"] = isHalfDay
        };

        if (!string.IsNullOrEmpty(reason))
            properties["Reason"] = reason;

        if (!string.IsNullOrEmpty(emergencyContact))
            properties["EmergencyContact"] = emergencyContact;

        return new RequestDetails("LEAVE", properties);
    }

    /// <summary>
    /// Creates overtime request details
    /// </summary>
    public static RequestDetails CreateOvertimeDetails(
        decimal estimatedHours,
        string? justification = null,
        Guid? projectId = null)
    {
        var properties = new Dictionary<string, object>
        {
            ["EstimatedHours"] = estimatedHours
        };

        if (!string.IsNullOrEmpty(justification))
            properties["Justification"] = justification;

        if (projectId.HasValue)
            properties["ProjectId"] = projectId.Value;

        return new RequestDetails("OVERTIME", properties);
    }

    /// <summary>
    /// Creates equipment request details
    /// </summary>
    public static RequestDetails CreateEquipmentDetails(
        string equipmentName,
        int quantity,
        string? specifications = null,
        decimal? estimatedCost = null,
        string? vendor = null)
    {
        var properties = new Dictionary<string, object>
        {
            ["EquipmentName"] = equipmentName,
            ["Quantity"] = quantity
        };

        if (!string.IsNullOrEmpty(specifications))
            properties["Specifications"] = specifications;

        if (estimatedCost.HasValue)
            properties["EstimatedCost"] = estimatedCost.Value;

        if (!string.IsNullOrEmpty(vendor))
            properties["Vendor"] = vendor;

        return new RequestDetails("EQUIPMENT", properties);
    }

    /// <summary>
    /// Creates travel request details
    /// </summary>
    public static RequestDetails CreateTravelDetails(
        string destination,
        string purpose,
        decimal? estimatedCost = null,
        string? transportationMode = null,
        string? accommodationNeeds = null)
    {
        var properties = new Dictionary<string, object>
        {
            ["Destination"] = destination,
            ["Purpose"] = purpose
        };

        if (estimatedCost.HasValue)
            properties["EstimatedCost"] = estimatedCost.Value;

        if (!string.IsNullOrEmpty(transportationMode))
            properties["TransportationMode"] = transportationMode;

        if (!string.IsNullOrEmpty(accommodationNeeds))
            properties["AccommodationNeeds"] = accommodationNeeds;

        return new RequestDetails("TRAVEL", properties);
    }

    /// <summary>
    /// Creates expense reimbursement details
    /// </summary>
    public static RequestDetails CreateExpenseDetails(
        decimal amount,
        string currency,
        string category,
        string? receiptNumber = null,
        string? vendor = null)
    {
        var properties = new Dictionary<string, object>
        {
            ["Amount"] = amount,
            ["Currency"] = currency,
            ["Category"] = category
        };

        if (!string.IsNullOrEmpty(receiptNumber))
            properties["ReceiptNumber"] = receiptNumber;

        if (!string.IsNullOrEmpty(vendor))
            properties["Vendor"] = vendor;

        return new RequestDetails("EXPENSE", properties);
    }

    /// <summary>
    /// Gets a property value by key
    /// </summary>
    public T? GetProperty<T>(string key)
    {
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
            return typedValue;
        
        return default(T);
    }

    /// <summary>
    /// Sets a property value
    /// </summary>
    public RequestDetails SetProperty(string key, object value)
    {
        var newProperties = new Dictionary<string, object>(Properties)
        {
            [key] = value
        };
        
        return new RequestDetails(RequestTypeCode, newProperties);
    }

    /// <summary>
    /// Removes a property
    /// </summary>
    public RequestDetails RemoveProperty(string key)
    {
        var newProperties = new Dictionary<string, object>(Properties);
        newProperties.Remove(key);
        
        return new RequestDetails(RequestTypeCode, newProperties);
    }

    /// <summary>
    /// Checks if a property exists
    /// </summary>
    public bool HasProperty(string key)
    {
        return Properties.ContainsKey(key);
    }

    /// <summary>
    /// Gets all property keys
    /// </summary>
    public IEnumerable<string> GetPropertyKeys()
    {
        return Properties.Keys;
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return RequestTypeCode;
        
        foreach (var kvp in Properties.OrderBy(p => p.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }

    private void SetRequestTypeCode(string requestTypeCode)
    {
        if (string.IsNullOrWhiteSpace(requestTypeCode))
            throw new ArgumentException("Request type code cannot be null or empty", nameof(requestTypeCode));

        RequestTypeCode = requestTypeCode.ToUpperInvariant();
    }

    private void ValidateProperties()
    {
        // Validate based on request type
        switch (RequestTypeCode)
        {
            case "LEAVE":
                ValidateLeaveProperties();
                break;
            case "OVERTIME":
                ValidateOvertimeProperties();
                break;
            case "EQUIPMENT":
                ValidateEquipmentProperties();
                break;
            case "TRAVEL":
                ValidateTravelProperties();
                break;
            case "EXPENSE":
                ValidateExpenseProperties();
                break;
        }
    }

    private void ValidateLeaveProperties()
    {
        if (!HasProperty("LeaveTypeCode"))
            throw new ArgumentException("Leave requests must specify LeaveTypeCode");
    }

    private void ValidateOvertimeProperties()
    {
        if (!HasProperty("EstimatedHours"))
            throw new ArgumentException("Overtime requests must specify EstimatedHours");

        var hours = GetProperty<decimal>("EstimatedHours");
        if (hours <= 0)
            throw new ArgumentException("EstimatedHours must be greater than 0");
    }

    private void ValidateEquipmentProperties()
    {
        if (!HasProperty("EquipmentName"))
            throw new ArgumentException("Equipment requests must specify EquipmentName");

        if (!HasProperty("Quantity"))
            throw new ArgumentException("Equipment requests must specify Quantity");

        var quantity = GetProperty<int>("Quantity");
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be greater than 0");
    }

    private void ValidateTravelProperties()
    {
        if (!HasProperty("Destination"))
            throw new ArgumentException("Travel requests must specify Destination");

        if (!HasProperty("Purpose"))
            throw new ArgumentException("Travel requests must specify Purpose");
    }

    private void ValidateExpenseProperties()
    {
        if (!HasProperty("Amount"))
            throw new ArgumentException("Expense requests must specify Amount");

        if (!HasProperty("Currency"))
            throw new ArgumentException("Expense requests must specify Currency");

        if (!HasProperty("Category"))
            throw new ArgumentException("Expense requests must specify Category");

        var amount = GetProperty<decimal>("Amount");
        if (amount <= 0)
            throw new ArgumentException("Amount must be greater than 0");
    }
}
