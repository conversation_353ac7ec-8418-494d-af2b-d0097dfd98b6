﻿namespace Kantoku.Domain.Models;

public class Org : AuditableEntity
{
    public Guid OrgUid { get; set; }


    public string OrgCode { get; set; } = null!;


    public string OrgName { get; set; } = null!;


    public string? OrgSubName { get; set; }


    public string? PostalCode { get; set; }


    public string? Address { get; set; }


    public string? PhoneNumber { get; set; }


    public string? Email { get; set; }


    public string? Fax { get; set; }


    public string? Website { get; set; }


    public string? RegistrationNumber { get; set; }


    public DateOnly? RegistrationDate { get; set; }


    public bool? RegistrationLicenseType { get; set; }


    public string? LegalOrgNumber { get; set; }


    public string? LegalTaxNumber { get; set; }


    public string? LegalRepresentative { get; set; }


    public string? Description { get; set; }

    public string? LogoUrl { get; set; }

    public bool EnableAutoCheckOut { get; set; } = false;

    public string EmployeeRankingDefinitionType { get; set; } = "DIRECT"; // DIRECT, INDIRECT

    public bool IsDeleted { get; set; } = false;

    public virtual ICollection<Employee> Employees { get; set; } = [];

    public virtual ICollection<EventCalendar> EventCalendars { get; set; } = [];

    public virtual ICollection<Position> Positions { get; set; } = [];

    public virtual ICollection<Project> Projects { get; set; } = [];

    public virtual ICollection<Ranking> Rankings { get; set; } = [];

    public virtual ICollection<Structure> Structures { get; set; } = [];

    public virtual ICollection<Role> Roles { get; set; } = [];

    public virtual ICollection<WorkShift> WorkShifts { get; set; } = [];

    public virtual ICollection<GlobalConfig> GlobalConfigs { get; set; } = [];

    public virtual ICollection<OutSource> OutSources { get; set; } = [];

    public virtual ICollection<Customer> Customers { get; set; } = [];

    public virtual ICollection<EmployeeInvitation> EmployeeInvitations { get; set; } = [];

    // Cost
    public virtual ICollection<InputCost> InputCosts { get; set; } = [];

    public virtual ICollection<Vendor> Vendors { get; set; } = [];

    public virtual ICollection<Category> Categories { get; set; } = [];

    public virtual ICollection<Manufacturer> Manufacturers { get; set; } = [];

    public virtual ICollection<EntryType> EntryTypes { get; set; } = [];

    public virtual ICollection<PaymentType> PaymentTypes { get; set; } = [];

    public virtual ICollection<Item> Items { get; set; } = [];

    public virtual ICollection<Construction> Constructions { get; set; } = [];

    public virtual ICollection<Contractor> Contractors { get; set; } = [];

    public virtual ICollection<ConstructionCost> ConstructionCosts { get; set; } = [];

    public virtual ICollection<ConstructionPaymentRequest> ConstructionPaymentRequests { get; set; } = [];

    public virtual ICollection<InputCostItem> InputCostItems { get; set; } = [];

    public virtual ICollection<ItemPrice> ItemPrices { get; set; } = [];

    public virtual ICollection<ProjectDailyReport> ProjectDailyReports { get; set; } = [];

    public virtual ICollection<Notification> Notifications { get; set; } = [];
}
