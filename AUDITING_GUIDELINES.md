# Auditing System Implementation Guidelines

## 1. Introduction

This document outlines the strategy and implementation steps for a comprehensive auditing system within the Kantoku service. The goal is to track entity changes (creations, updates, soft deletes) in a modern, effective, and performant manner, adhering to SOLID and DDD principles.

## 2. Auditing Requirements

The auditing system must fulfill two main requirements:

1.  **Quick Audit Information (On-Entity)**:
    *   `CreatedAtUtc`: Timestamp of entity creation.
    *   `CreatedBy`: Identifier of the user/system that created the entity.
    *   `LastModifiedAtUtc`: Timestamp of the last entity modification.
    *   `LastModifiedBy`: Identifier of the user/system that last modified the entity.
    *   `IsDeleted`: Flag indicating if the entity is soft-deleted.
    *   `DeletedAtUtc`: Timestamp of soft deletion.
    *   `DeletedBy`: Identifier of the user/system that soft-deleted the entity.

2.  **Detailed Audit Log (Separate Log)**:
    *   A chronological record of all significant changes to audited entities.
    *   Each entry should capture:
        *   **WHO**: The user or system principal responsible for the change.
        *   **WHAT ACTION**: The type of operation (e.g., `CREATE`, `UPDATE`, `DELETE`).
        *   **WHICH ENTITY**: The type and primary key of the entity that was changed.
        *   **WHAT FIELD(S)**: For updates, the specific fields that were modified, including their old and new values.
        *   **WHEN**: The timestamp of the change.

## 3. Implementation Strategy

### Core Concepts

*   **Auditable Entity Interfaces**: A set of interfaces (`ICreationAudited`, `IModificationAudited`, `ISoftDeleteAudited`, `IFullAudited`) will define the contract for entities that require auditing.
*   **Base Audited Entity Class**: A generic base class (e.g., `FullAuditedEntity<TKey>`) will implement these interfaces, providing common audit properties.
*   **`AuditLog` Entity**: A dedicated entity (`AuditLog.cs`) to store detailed change records.
*   **Current User Service**: An abstraction (`ICurrentUserService`) to retrieve the ID of the current user performing the operation.

### Mechanism

1.  **Automatic Population of Quick Audit Properties**:
    *   The application's `DbContext` will override `SaveChanges` / `SaveChangesAsync`.
    *   Before saving, it will inspect entities in the Change Tracker.
    *   For entities implementing auditable interfaces, it will automatically populate `CreatedAtUtc`, `CreatedBy`, `LastModifiedAtUtc`, `LastModifiedBy`, etc., based on the entity's state (`Added`, `Modified`).
    *   For soft deletes, it will set `IsDeleted`, `DeletedAtUtc`, and `DeletedBy` instead of physically removing the record.

2.  **Detailed Logging using Domain Events**:
    *   **Domain Events**: Define events like `EntityCreatedEvent`, `EntityUpdatedEvent`, `EntitySoftDeletedEvent`. These events will carry data about the change (entity type, ID, user ID, timestamp, changed values).
    *   **Event Publishing**: The `DbContext` (or a domain service) will publish these events after successfully detecting changes but ideally before `SaveChanges` commits them, or as part of the same transaction.
    *   **Event Handlers**: Dedicated event handlers (e.g., `AuditLogEventHandler`) will subscribe to these domain events.
    *   **Persisting Audit Logs**: Upon receiving an event, the handler will construct and save an `AuditLog` entity to the database. This decouples audit logging from the primary business transaction, though it should occur within the same transaction to ensure consistency.

## 4. Detailed Implementation Steps

### A. Define Auditable Interfaces & Base Classes

Location: `Kantoku.Domain/Common/Auditing/` (new folder)

1.  **`ICreationAudited.cs`**:
    ```csharp
    public interface ICreationAudited
    {
        DateTime CreatedAtUtc { get; set; }
        Guid? CreatedBy { get; set; } // Assuming user ID is Guid, adjust if necessary
    }
    ```
2.  **`IModificationAudited.cs`**:
    ```csharp
    public interface IModificationAudited
    {
        DateTime? LastModifiedAtUtc { get; set; }
        Guid? LastModifiedBy { get; set; }
    }
    ```
3.  **`ISoftDeleteAudited.cs`**:
    ```csharp
    public interface ISoftDeleteAudited
    {
        bool IsDeleted { get; set; }
        DateTime? DeletedAtUtc { get; set; }
        Guid? DeletedBy { get; set; }
    }
    ```
4.  **`IFullAudited.cs`**:
    ```csharp
    public interface IFullAudited : ICreationAudited, IModificationAudited, ISoftDeleteAudited
    {
    }
    ```
5.  **`FullAuditedEntity.cs`** (Example for Guid PKs):
    ```csharp
    // Using a common base like Entity<TKey> if you have one
    public abstract class FullAuditedEntity : IFullAudited 
    {
        // Assuming Guid Id is common, or make this generic FullAuditedEntity<TKey>
        // public virtual Guid Id { get; set; } // Example if your entities have a base Id

        public DateTime CreatedAtUtc { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? LastModifiedAtUtc { get; set; }
        public Guid? LastModifiedBy { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAtUtc { get; set; }
        public Guid? DeletedBy { get; set; }
    }
    ```
    *Note: If your entities have a common base class like `Entity<TKey>`, `FullAuditedEntity` should inherit from it.*

### B. Modify Entities to Inherit from Auditable Base Classes

For each entity that requires auditing, change its base class to `FullAuditedEntity` or implement the desired audit interfaces directly.

Example:
```csharp
// Before
// public class YourEntity
// {
//     public Guid Id { get; set; }
//     // ... other properties
// }

// After
// public class YourEntity : FullAuditedEntity
// {
//     public Guid Id { get; set; } // Or comes from a more generic base
//     // ... other properties
// }
```

### C. Refine `AuditLog` Entity

Location: `Kantoku.Api/Databases/Models/AuditLog.cs`

The existing `AuditLog` entity is a good starting point. We'll refine the `OldValues` and `NewValues` to store structured JSON representing property changes.

```csharp
// Kantoku.Api/Databases/Models/AuditLog.cs
namespace Kantoku.Api.Databases.Models;

public class AuditLog
{
    public Guid AuditLogUid { get; set; }
    public string EntityType { get; set; } = null!; // Renamed from EntityName for clarity
    public string EntityId { get; set; } = null!;
    public string ActionType { get; set; } = null!; // e.g., "CREATE", "UPDATE", "SOFT_DELETE"
    public DateTime TimestampUtc { get; set; } // Ensure UTC
    
    // Stores a JSON serialized dictionary/list of property changes
    // For UPDATE: { "PropertyName1": { "Old": "value", "New": "value" }, ... }
    // For CREATE: Contains all property values of the created entity.
    // For DELETE: Contains all property values of the deleted entity.
    public string? Changes { get; set; } // Replaces OldValues and NewValues for simplicity. Can be structured JSON.
                                        // Alternatively, keep OldValues (for DELETE) and NewValues (for CREATE)
                                        // and a specific PropertyChanges (JSON) for UPDATE.
                                        // For now, let's try a single `Changes` field.

    public Guid? UserId { get; set; } // Renamed from AccountUid for generality

    // Optional: Store a summary or reason for the change if applicable
    // public string? ChangeReason { get; set; }

    public virtual Account? User { get; set; } // Navigation to Account entity
}
```
*Consider what information is most useful for `Changes`. A diff format for updates is common.*
*If `AccountUid` specifically refers to your `Account` entity's primary key, and `Account` represents users, then `UserId` mapping to `AccountUid` is fine.*

### D. Implement `ICurrentUserService`

Location: `Kantoku.Application/Interfaces/` (or a shared kernel project if applicable)

1.  **`ICurrentUserService.cs`**:
    ```csharp
    public interface ICurrentUserService
    {
        Guid? UserId { get; }
        // Add other user-related info if needed, e.g., IP address, tenant ID
    }
    ```
2.  **Implementation (`CurrentUserService.cs`)**:
    This service will typically use `IHttpContextAccessor` to get user information from the current HTTP context.
    Location: `Kantoku.Infrastructure/Services/` (or `Kantoku.Api/Services/`)
    ```csharp
    using Microsoft.AspNetCore.Http;
    using System.Security.Claims;
    // ... other necessary using statements ...

    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public Guid? UserId
        {
            get
            {
                var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.NameIdentifier);
                return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
            }
        }
    }
    ```
    *Ensure this is registered for DI.*

### E. Implement `DbContext` Interception & Event Publishing

Location: `Kantoku.Persistence/Context/ApplicationDbContext.cs`

```csharp
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System.Text.Json;
// ... other usings for your entities, ICurrentUserService, auditable interfaces, AuditLog model, domain events ...

public partial class ApplicationDbContext : DbContext // Or your actual DbContext base
{
    private readonly ICurrentUserService _currentUserService;
    // private readonly IDomainEventDispatcher _domainEventDispatcher; // If using a dedicated dispatcher

    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        ICurrentUserService currentUserService
        // IDomainEventDispatcher domainEventDispatcher
        ) : base(options)
    {
        _currentUserService = currentUserService;
        // _domainEventDispatcher = domainEventDispatcher;
    }

    // ... your DbSets, including:
    // public DbSet<AuditLog> AuditLogs { get; set; }


    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        var auditEntries = OnBeforeSaveChanges();
        var result = await base.SaveChangesAsync(cancellationToken);
        await OnAfterSaveChanges(auditEntries); // Pass entries to event publishing
        return result;
    }

    public override int SaveChanges()
    {
        var auditEntries = OnBeforeSaveChanges();
        var result = base.SaveChanges();
        // Synchronous event dispatch or handling if necessary, though async is preferred for audit logs
        // Task.Run(() => OnAfterSaveChanges(auditEntries)).GetAwaiter().GetResult(); 
        OnAfterSaveChanges(auditEntries).GetAwaiter().GetResult();
        return result;
    }

    private List<AuditEntry> OnBeforeSaveChanges()
    {
        ChangeTracker.DetectChanges();
        var auditEntries = new List<AuditEntry>();
        var utcNow = DateTime.UtcNow;
        var userId = _currentUserService.UserId;

        foreach (var entry in ChangeTracker.Entries())
        {
            if (entry.Entity is AuditLog || entry.State == EntityState.Detached || entry.State == EntityState.Unchanged)
                continue;

            var auditEntry = new AuditEntry(entry)
            {
                TableName = entry.Metadata.GetTableName() ?? entry.Entity.GetType().Name,
                UserId = userId,
                Timestamp = utcNow
            };

            if (entry.Entity is ISoftDeleteAudited softDeletable)
            {
                if (entry.State == EntityState.Deleted) // Intercept physical delete for ISoftDeleteAudited entities
                {
                    entry.State = EntityState.Modified; // Change to Modified
                    softDeletable.IsDeleted = true;
                    softDeletable.DeletedAtUtc = utcNow;
                    softDeletable.DeletedBy = userId;
                    auditEntry.ActionType = "SOFT_DELETE";
                }
            }

            switch (entry.State)
            {
                case EntityState.Added:
                    if (entry.Entity is ICreationAudited creationAudited)
                    {
                        creationAudited.CreatedAtUtc = utcNow;
                        creationAudited.CreatedBy = userId;
                    }
                    auditEntry.ActionType = "CREATE";
                    auditEntry.NewValues = GetPropertyValues(entry);
                    break;

                case EntityState.Modified:
                    if (entry.Properties.Any(p => p.IsModified) || auditEntry.ActionType == "SOFT_DELETE") // Ensure actual changes or soft delete
                    {
                        if (entry.Entity is IModificationAudited modificationAudited)
                        {
                            modificationAudited.LastModifiedAtUtc = utcNow;
                            modificationAudited.LastModifiedBy = userId;
                        }
                        auditEntry.ActionType = auditEntry.ActionType ?? "UPDATE"; // Keep SOFT_DELETE if already set
                        
                        auditEntry.OldValues = GetChangedPropertyValues(entry, original: true);
                        auditEntry.NewValues = GetChangedPropertyValues(entry, original: false);
                        
                        // If no properties were actually modified (e.g. only navigation props, or soft delete)
                        // ensure OldValues and NewValues capture relevant state.
                        if (auditEntry.ActionType == "SOFT_DELETE" && (!auditEntry.OldValues.Any() && !auditEntry.NewValues.Any()))
                        {
                           // For soft delete, capture the state before marking as deleted if needed, or key properties.
                           // Or rely on the fact IsDeleted, DeletedAt, DeletedBy are now set.
                           // For simplicity here, we're focusing on explicit property changes.
                        }
                    }
                    else // No actual property modified, skip audit log for this entry
                    {
                         continue; // Skip adding to auditEntries
                    }
                    break;
                
                // EF Core does not track EntityState.Deleted for owned types directly in a way that hits here
                // if the owner is modified. Soft delete handles this.
                // Physical deletes for non-soft-deletable entities could be logged here if needed.
            }
            auditEntries.Add(auditEntry);
        }
        return auditEntries;
    }

    private Dictionary<string, object?> GetPropertyValues(EntityEntry entry)
    {
        var values = new Dictionary<string, object?>();
        foreach (var property in entry.Properties)
        {
            values[property.Metadata.Name] = property.CurrentValue;
        }
        return values;
    }

    private Dictionary<string, object?> GetChangedPropertyValues(EntityEntry entry, bool original)
    {
        var values = new Dictionary<string, object?>();
        foreach (var property in entry.Properties)
        {
            if (property.IsModified || (entry.State == EntityState.Modified && entry.Entity is ISoftDeleteAudited && property.Metadata.Name == nameof(ISoftDeleteAudited.IsDeleted)))
            {
                 values[property.Metadata.Name] = original ? property.OriginalValue : property.CurrentValue;
            }
        }
        return values;
    }


    private async Task OnAfterSaveChanges(List<AuditEntry> auditEntries)
    {
        if (auditEntries == null || auditEntries.Count == 0)
            return;

        var auditLogs = new List<AuditLog>();
        foreach (var auditEntry in auditEntries)
        {
            // Skip if no meaningful changes for UPDATEs
            if (auditEntry.ActionType == "UPDATE" && (auditEntry.OldValues == null || !auditEntry.OldValues.Any()) && (auditEntry.NewValues == null || !auditEntry.NewValues.Any()))
            {
                // This check might be too simplistic if IsModified was true due to complex types/navigations not directly captured as scalar property changes.
                // However, for typical scalar properties, if OldValues/NewValues are empty, it means no tracked scalar props changed.
                // If LastModifiedAtUtc was updated, it's still an update.
                bool modificationAuditUpdated = false;
                if(auditEntry.Entry.Entity is IModificationAudited modEntity)
                {
                    var modTimeProp = auditEntry.Entry.Property(nameof(IModificationAudited.LastModifiedAtUtc));
                    if(modTimeProp.IsModified) modificationAuditUpdated = true;
                }
                if(!modificationAuditUpdated && (auditEntry.OldValues == null || !auditEntry.OldValues.Any())) continue;

            }


            var log = new AuditLog
            {
                AuditLogUid = Guid.NewGuid(),
                EntityType = auditEntry.TableName,
                EntityId = auditEntry.KeyValues.Count > 0 ? JsonSerializer.Serialize(auditEntry.KeyValues) : "N/A",
                ActionType = auditEntry.ActionType,
                TimestampUtc = auditEntry.Timestamp,
                UserId = auditEntry.UserId,
                Changes = JsonSerializer.Serialize(new { Old = auditEntry.OldValues, New = auditEntry.NewValues }, new JsonSerializerOptions { WriteIndented = false, DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull })
            };
            auditLogs.Add(log);

            // Option 1: Directly add to DbContext (simpler, part of same transaction)
            // await this.Set<AuditLog>().AddAsync(log); // Use this.AuditLogs.AddAsync(log) if DbSet is defined

            // Option 2: Publish Domain Event (more decoupled)
            // var domainEvent = new EntityAuditedEvent(log);
            // await _domainEventDispatcher.Dispatch(domainEvent);
        }

        // If using direct add:
        if (auditLogs.Any())
        {
            await this.Set<AuditLog>().AddRangeAsync(auditLogs);
            // Must call SaveChanges again for audit logs, but without triggering this audit logic again.
            // This requires a flag or a separate DbContext instance for audit logs if not careful.
            // A simpler way for direct add is to add to a list and save them in a single batch *after* the main save,
            // but this requires careful handling if the main save fails.
            // The current approach adds them to the ChangeTracker, and the initial SaveChangesAsync will pick them up.
            // This is generally okay.
            // However, the auditEntries are collected *before* the primary save. If that save fails, these are not persisted.
            // For true atomicity, audit logs must be part of the same transaction.
            // The current setup should work because `auditLogs` are added to the same DbContext instance
            // and `base.SaveChangesAsync()` will save them along with the main entities.
            // The crucial part is that `OnBeforeSaveChanges` prepares audit entries for the *current* set of changes.
            //
            // Re-evaluating: The `auditEntries` are generated based on the state *before* `base.SaveChangesAsync()`.
            // The `AuditLog` entities themselves are created *after* `base.SaveChangesAsync()` might have completed.
            // To ensure `AuditLog` entities are saved in the *same transaction*:
            // The `auditLogs` list should be populated in `OnBeforeSaveChanges` directly into the `ChangeTracker`.
            //
            // Let's adjust:
        }
    }


    // Helper class for audit entry data
    public class AuditEntry
    {
        public AuditEntry(EntityEntry entry)
        {
            Entry = entry;
            KeyValues = new Dictionary<string, object>();
            foreach (var prop in entry.Metadata.FindPrimaryKey().Properties)
            {
                KeyValues[prop.Name] = entry.Property(prop.Name).CurrentValue ?? entry.Property(prop.Name).OriginalValue;
            }
        }
        public EntityEntry Entry { get; }
        public string TableName { get; set; } = null!;
        public Dictionary<string, object> KeyValues { get; } // Primary Key values
        public string ActionType { get; set; } = null!; // CREATE, UPDATE, SOFT_DELETE
        public Dictionary<string, object?> OldValues { get; set; } = new();
        public Dictionary<string, object?> NewValues { get; set; } = new();
        public Guid? UserId { get; set; }
        public DateTime Timestamp { get; set; }
        // List<PropertyChange> PropertyChanges { get; set; } = new(); // For more structured changes
    }
}
```
*The `DbContext` logic needs careful implementation to handle transactions and correctly capture changes. The above is a comprehensive starting point. It directly adds `AuditLog` entries to the same `DbContext` to be saved in the same transaction.*
*Using Domain Events for `OnAfterSaveChanges` would involve an `IDomainEventDispatcher` and handlers that then use a `DbContext` (possibly a new scope) to save audit logs. This adds complexity but offers better decoupling if audit logging failures shouldn't roll back primary operations.*
*For simplicity and atomicity, the above example tries to include `AuditLog` entities in the same `SaveChanges` call.*
*A crucial refinement needed in `OnBeforeSaveChanges`: the actual `AuditLog` entities should be created and added to the `DbContext` there, so `base.SaveChangesAsync()` saves them.*

**Revised `DbContext` approach for `OnBeforeSaveChanges` to include AuditLogs directly:**

```csharp
// ... (DbContext class and constructor as before) ...
    // public DbSet<AuditLog> AuditLogs { get; set; } // Ensure this DbSet exists

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        ApplyAuditConcepts(); // This will add AuditLog entities to the change tracker
        return await base.SaveChangesAsync(cancellationToken);
    }

    public override int SaveChanges()
    {
        ApplyAuditConcepts();
        return base.SaveChanges();
    }

    private void ApplyAuditConcepts()
    {
        ChangeTracker.DetectChanges(); // Important
        var utcNow = DateTime.UtcNow;
        var userId = _currentUserService.UserId;
        var auditLogEntriesToAdd = new List<AuditLog>();

        foreach (var entry in ChangeTracker.Entries().ToList()) // .ToList() to allow modification during iteration (adding AuditLogs)
        {
            if (entry.Entity is AuditLog || entry.State == EntityState.Detached || entry.State == EntityState.Unchanged)
                continue;

            string actionType = string.Empty;
            Dictionary<string, object?> oldValues = new();
            Dictionary<string, object?> newValues = new();
            
            var tableName = entry.Metadata.GetTableName() ?? entry.Entity.GetType().Name;
            var primaryKeyValues = new Dictionary<string, object>();
            foreach (var prop in entry.Metadata.FindPrimaryKey().Properties)
            {
                primaryKeyValues[prop.Name] = entry.Property(prop.Name).CurrentValue ?? entry.Property(prop.Name).OriginalValue;
            }
            string entityIdJson = JsonSerializer.Serialize(primaryKeyValues);


            if (entry.Entity is ISoftDeleteAudited softDeletableEntity && entry.State == EntityState.Deleted)
            {
                entry.State = EntityState.Modified; // Convert to Modified state
                softDeletableEntity.IsDeleted = true;
                softDeletableEntity.DeletedAtUtc = utcNow;
                softDeletableEntity.DeletedBy = userId;
                actionType = "SOFT_DELETE";
                
                // Capture all relevant properties before "deletion"
                foreach (var property in entry.Properties)
                {
                    oldValues[property.Metadata.Name] = property.OriginalValue;
                    if (property.IsModified || property.Metadata.Name == nameof(ISoftDeleteAudited.IsDeleted) ||
                        property.Metadata.Name == nameof(ISoftDeleteAudited.DeletedAtUtc) ||
                        property.Metadata.Name == nameof(ISoftDeleteAudited.DeletedBy))
                    {
                         newValues[property.Metadata.Name] = property.CurrentValue; // Capture new audit prop values
                    }
                }
            }

            switch (entry.State)
            {
                case EntityState.Added:
                    if (entry.Entity is ICreationAudited creationAuditedEntity)
                    {
                        creationAuditedEntity.CreatedAtUtc = utcNow;
                        creationAuditedEntity.CreatedBy = userId;
                    }
                    actionType = "CREATE";
                    foreach (var property in entry.Properties)
                    {
                        newValues[property.Metadata.Name] = property.CurrentValue;
                    }
                    break;

                case EntityState.Modified:
                    // If already handled by soft delete, actionType is set. Otherwise, it's an UPDATE.
                    if (string.IsNullOrEmpty(actionType)) actionType = "UPDATE";

                    bool hasRelevantChanges = false;
                    if (entry.Entity is IModificationAudited modificationAuditedEntity)
                    {
                        // Check if any property except LastModifiedAt/By is changed.
                        // If only LastModifiedAt/By are being set by this logic, don't trigger an audit log for *that* change alone
                        // unless other properties also changed.
                        var originalLastModifiedAt = modificationAuditedEntity.LastModifiedAtUtc;

                        modificationAuditedEntity.LastModifiedAtUtc = utcNow;
                        modificationAuditedEntity.LastModifiedBy = userId;
                        
                        // Avoid loop if only audit fields are changing due to this process
                        if (entry.Properties.Any(p => p.IsModified && p.Metadata.Name != nameof(IModificationAudited.LastModifiedAtUtc) && p.Metadata.Name != nameof(IModificationAudited.LastModifiedBy)))
                        {
                             hasRelevantChanges = true;
                        }
                        else if (originalLastModifiedAt != modificationAuditedEntity.LastModifiedAtUtc) // if date actually changed by us
                        {
                            // This means an update happened even if no other user-data fields changed.
                            // For example, a soft delete sets modification time.
                            hasRelevantChanges = true;
                        }

                    } else { // Not IModificationAudited, but still modified
                        hasRelevantChanges = entry.Properties.Any(p => p.IsModified);
                    }
                    
                    if (actionType == "SOFT_DELETE" || (actionType == "UPDATE" && entry.Properties.Any(p => p.IsModified)))
                    {
                         foreach (var property in entry.Properties)
                        {
                            if (property.IsModified || actionType == "SOFT_DELETE") // For soft_delete, capture all original as old, and IsDeleted etc. as new
                            {
                                oldValues[property.Metadata.Name] = property.OriginalValue;
                                newValues[property.Metadata.Name] = property.CurrentValue;
                                hasRelevantChanges = true;
                            }
                        }
                    }

                    if (!hasRelevantChanges && actionType == "UPDATE") continue; // Skip audit if only audit fields were touched by this process and no other changes

                    break;
            }

            if (!string.IsNullOrEmpty(actionType))
            {
                 // Filter out empty old/new value lists for CREATE/DELETE if not desired
                object? changesPayload = null;
                if (actionType == "CREATE") changesPayload = newValues.Any() ? newValues : null;
                else if (actionType == "SOFT_DELETE" || actionType == "DELETE") changesPayload = oldValues.Any() ? oldValues : null; // For soft_delete, newValues might contain the IsDeleted flags
                else if (actionType == "UPDATE") changesPayload = new { Old = oldValues.Any() ? oldValues : null, New = newValues.Any() ? newValues : null };


                if (actionType == "UPDATE" && (!oldValues.Any() && !newValues.Any())) {
                    // If an update action resulted in no changed properties being logged (e.g. complex type modification not caught by IsModified)
                    // or if only IModificationAudited properties were updated by this function, this check might skip logging.
                    // This needs to be robust. If LastModifiedAtUtc was genuinely updated, a log should exist.
                    var modEntity = entry.Entity as IModificationAudited;
                    bool modTimeChangedByLogic = modEntity != null && entry.Property(nameof(IModificationAudited.LastModifiedAtUtc)).IsModified;

                    if (!modTimeChangedByLogic && !oldValues.Any()) continue; // Skip if no actual data changed for an UPDATE
                }


                var auditLog = new AuditLog
                {
                    AuditLogUid = Guid.NewGuid(),
                    EntityType = tableName,
                    EntityId = entityIdJson,
                    ActionType = actionType,
                    TimestampUtc = utcNow,
                    UserId = userId,
                    Changes = changesPayload != null ? JsonSerializer.Serialize(changesPayload, new JsonSerializerOptions { WriteIndented = false, DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull }) : null
                };
                auditLogEntriesToAdd.Add(auditLog);
            }
        }

        if (auditLogEntriesToAdd.Any())
        {
            this.Set<AuditLog>().AddRange(auditLogEntriesToAdd); // Add to change tracker
        }
    }
```

### F. Define Domain Events & Handlers (Alternative to Direct DbContext Logging)

This step is an alternative if you prefer more decoupling than the revised `DbContext` approach above. If using the revised `DbContext` that adds `AuditLog` entities directly, this explicit eventing step for audit logs can be skipped.

1.  **Domain Events** (Location: `Kantoku.Domain/Common/Events/` or similar)
    *   `EntityChangedEvent.cs` (a generic event or specific ones like `EntityCreatedEvent`)
2.  **Event Handlers** (Location: `Kantoku.Application/AuditLogs/EventHandlers/`)
    *   `LogAuditChangeEventHandler.cs`: Subscribes to `EntityChangedEvent`, creates `AuditLog` record, and saves it (potentially using a new `DbContext` scope).

### G. Register Services (Dependency Injection)

In `Startup.cs` or your DI configuration:

```csharp
// For ICurrentUserService
services.AddHttpContextAccessor(); // If not already added
services.AddScoped<ICurrentUserService, CurrentUserService>();

// If using Domain Events for auditing:
// services.AddScoped<IDomainEventDispatcher, MediatRDomainEventDispatcher>(); // Example
// services.AddTransient<INotificationHandler<EntityChangedEvent>, LogAuditChangeEventHandler>();
```

## 5. Usage

1.  Ensure your entity inherits from `FullAuditedEntity` (or relevant interfaces).
2.  The system will automatically handle the audit properties and detailed logging when `SaveChanges` or `SaveChangesAsync` is called on the `DbContext`.

## 6. Querying Audit Information

*   **Quick Audit**: Access properties like `CreatedAtUtc`, `CreatedBy`, `LastModifiedAtUtc`, `LastModifiedBy`, `IsDeleted` directly from the entity instance.
*   **Detailed Audit**: Query the `AuditLogs` table/DbSet.
    ```csharp
    var userAudits = await dbContext.AuditLogs
                                  .Where(a => a.UserId == targetUserId)
                                  .OrderByDescending(a => a.TimestampUtc)
                                  .ToListAsync();

    var entityAudits = await dbContext.AuditLogs
                                   .Where(a => a.EntityType == "YourEntityName" && a.EntityId.Contains(entityPkValue))
                                   .OrderByDescending(a => a.TimestampUtc)
                                   .ToListAsync();
    ```
    The `Changes` JSON can be deserialized for inspection.

---

This guideline provides a comprehensive approach. The `DbContext` interception logic is critical and needs to be robustly tested, especially concerning how changes are detected and what constitutes a auditable change versus an infrastructure update (like `LastModifiedAtUtc` itself).
The revised `DbContext.ApplyAuditConcepts` aims for atomicity by including `AuditLog` entries in the same transaction.

</code_block_to_apply_changes_from> 