namespace Kantoku.Domain.OrganizationManagement;

/// <summary>
/// Repository interface for Organization aggregate
/// </summary>
public interface IOrganizationRepository
{
    /// <summary>
    /// Gets an organization by its ID
    /// </summary>
    Task<Organization?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets an organization by its code
    /// </summary>
    Task<Organization?> GetByCodeAsync(string orgCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all active organizations
    /// </summary>
    Task<IEnumerable<Organization>> GetAllActiveAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an organization with the given code exists
    /// </summary>
    Task<bool> ExistsByCodeAsync(string orgCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an organization with the given code exists (excluding the specified ID)
    /// </summary>
    Task<bool> ExistsByCodeAsync(string orgCode, Guid excludeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new organization
    /// </summary>
    Task AddAsync(Organization organization, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing organization
    /// </summary>
    void Update(Organization organization);

    /// <summary>
    /// Removes an organization
    /// </summary>
    void Remove(Organization organization);

    /// <summary>
    /// Gets a structure by its ID within an organization
    /// </summary>
    Task<Structure?> GetStructureByIdAsync(Guid orgId, Guid structureId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all structures for an organization
    /// </summary>
    Task<IEnumerable<Structure>> GetStructuresAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a position by its ID within an organization
    /// </summary>
    Task<Position?> GetPositionByIdAsync(Guid orgId, Guid positionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all positions for an organization
    /// </summary>
    Task<IEnumerable<Position>> GetPositionsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a ranking by its ID within an organization
    /// </summary>
    Task<Ranking?> GetRankingByIdAsync(Guid orgId, Guid rankingId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all rankings for an organization
    /// </summary>
    Task<IEnumerable<Ranking>> GetRankingsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a work shift by its ID within an organization
    /// </summary>
    Task<WorkShift?> GetWorkShiftByIdAsync(Guid orgId, Guid workShiftId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all work shifts for an organization
    /// </summary>
    Task<IEnumerable<WorkShift>> GetWorkShiftsAsync(Guid orgId, CancellationToken cancellationToken = default);
}
