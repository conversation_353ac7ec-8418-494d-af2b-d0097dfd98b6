using Kantoku.SharedKernel;
using Kantoku.Domain.ProjectManagement.ValueObjects;

namespace Kantoku.Domain.ProjectManagement;

/// <summary>
/// Entity representing a construction within a project
/// </summary>
public class Construction : Entity<Guid>
{
    private readonly List<ConstructionCost> _costs = new();

    public Guid ProjectId { get; private set; }
    public Guid OrgId { get; private set; }
    public string ConstructionCode { get; private set; } = null!;
    public string ConstructionName { get; private set; } = null!;
    public string? Description { get; private set; }
    public string? Location { get; private set; }
    public ConstructionDates Dates { get; private set; } = null!;
    public ConstructionBudget? Budget { get; private set; }
    public string Status { get; private set; } = "PLANNED";
    public bool IsDeleted { get; private set; } = false;

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<ConstructionCost> Costs => _costs.AsReadOnly();

    // Private constructor for EF Core
    private Construction() : base() { }

    /// <summary>
    /// Creates a new construction
    /// </summary>
    public Construction(
        Guid id,
        Guid projectId,
        Guid orgId,
        string constructionCode,
        string constructionName,
        ConstructionDates dates,
        string? description = null,
        string? location = null) : base(id)
    {
        ProjectId = projectId;
        OrgId = orgId;
        SetConstructionCode(constructionCode);
        SetConstructionName(constructionName);
        Dates = dates ?? throw new ArgumentNullException(nameof(dates));
        Description = description;
        Location = location;
    }

    /// <summary>
    /// Updates basic construction information
    /// </summary>
    public void UpdateBasicInfo(
        string constructionName,
        string? description = null,
        string? location = null)
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot update completed construction");

        SetConstructionName(constructionName);
        Description = description;
        Location = location;
    }

    /// <summary>
    /// Updates construction dates
    /// </summary>
    public void UpdateDates(ConstructionDates dates)
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot update dates for completed construction");

        Dates = dates ?? throw new ArgumentNullException(nameof(dates));
    }

    /// <summary>
    /// Updates construction budget
    /// </summary>
    public void UpdateBudget(ConstructionBudget budget)
    {
        Budget = budget;
    }

    /// <summary>
    /// Updates construction status
    /// </summary>
    public void UpdateStatus(string status)
    {
        var validStatuses = new[] { "PLANNED", "IN_PROGRESS", "SUSPENDED", "COMPLETED", "CANCELLED" };
        if (!validStatuses.Contains(status))
            throw new ArgumentException($"Invalid status: {status}");

        Status = status;
    }

    /// <summary>
    /// Starts the construction
    /// </summary>
    public void Start()
    {
        if (Status != "PLANNED")
            throw new InvalidOperationException("Construction can only be started from PLANNED status");

        Status = "IN_PROGRESS";
        
        // Update actual start date if not set
        if (!Dates.ActualStartDate.HasValue)
        {
            var updatedDates = new ConstructionDates(
                Dates.PlannedStartDate,
                Dates.PlannedEndDate,
                DateOnly.FromDateTime(DateTime.Today),
                Dates.ActualEndDate);
            Dates = updatedDates;
        }
    }

    /// <summary>
    /// Completes the construction
    /// </summary>
    public void Complete()
    {
        if (Status != "IN_PROGRESS")
            throw new InvalidOperationException("Construction can only be completed from IN_PROGRESS status");

        Status = "COMPLETED";
        
        // Update actual end date if not set
        if (!Dates.ActualEndDate.HasValue)
        {
            var updatedDates = new ConstructionDates(
                Dates.PlannedStartDate,
                Dates.PlannedEndDate,
                Dates.ActualStartDate,
                DateOnly.FromDateTime(DateTime.Today));
            Dates = updatedDates;
        }
    }

    /// <summary>
    /// Suspends the construction
    /// </summary>
    public void Suspend()
    {
        if (Status != "IN_PROGRESS")
            throw new InvalidOperationException("Construction can only be suspended from IN_PROGRESS status");

        Status = "SUSPENDED";
    }

    /// <summary>
    /// Cancels the construction
    /// </summary>
    public void Cancel()
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot cancel completed construction");

        Status = "CANCELLED";
    }

    /// <summary>
    /// Adds a cost to the construction
    /// </summary>
    public void AddCost(ConstructionCost cost)
    {
        if (cost == null)
            throw new ArgumentNullException(nameof(cost));

        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot add costs to completed construction");

        _costs.Add(cost);
    }

    /// <summary>
    /// Removes a cost from the construction
    /// </summary>
    public void RemoveCost(Guid costId)
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot remove costs from completed construction");

        var cost = _costs.FirstOrDefault(c => c.Id == costId);
        if (cost != null)
        {
            _costs.Remove(cost);
        }
    }

    /// <summary>
    /// Gets the total cost amount
    /// </summary>
    public decimal GetTotalCost()
    {
        return _costs.Sum(c => c.Amount);
    }

    /// <summary>
    /// Soft deletes the construction
    /// </summary>
    public void Delete()
    {
        if (Status == "IN_PROGRESS")
            throw new InvalidOperationException("Cannot delete construction that is in progress");

        IsDeleted = true;
    }

    /// <summary>
    /// Restores the construction
    /// </summary>
    public void Restore()
    {
        IsDeleted = false;
    }

    /// <summary>
    /// Checks if the construction is completed
    /// </summary>
    public bool IsCompleted => Status == "COMPLETED";

    /// <summary>
    /// Checks if the construction is in progress
    /// </summary>
    public bool IsInProgress => Status == "IN_PROGRESS";

    /// <summary>
    /// Checks if the construction is overdue
    /// </summary>
    public bool IsOverdue => Status != "COMPLETED" && Dates.PlannedEndDate.HasValue && 
                            Dates.PlannedEndDate.Value < DateOnly.FromDateTime(DateTime.Today);

    // Private helper methods
    private void SetConstructionCode(string constructionCode)
    {
        if (string.IsNullOrWhiteSpace(constructionCode))
            throw new ArgumentException("Construction code cannot be null or empty", nameof(constructionCode));

        if (constructionCode.Length > 50)
            throw new ArgumentException("Construction code cannot exceed 50 characters", nameof(constructionCode));

        ConstructionCode = constructionCode.Trim();
    }

    private void SetConstructionName(string constructionName)
    {
        if (string.IsNullOrWhiteSpace(constructionName))
            throw new ArgumentException("Construction name cannot be null or empty", nameof(constructionName));

        if (constructionName.Length > 200)
            throw new ArgumentException("Construction name cannot exceed 200 characters", nameof(constructionName));

        ConstructionName = constructionName.Trim();
    }
}
