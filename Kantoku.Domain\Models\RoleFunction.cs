﻿namespace Kantoku.Domain.Models;

public class RoleFunction : AuditableEntity
{
    public Guid FunctionUid { get; set; }

    public Guid RoleUid { get; set; }


    public bool CanRead { get; set; } = true;


    public bool CanCreate { get; set; } = false;


    public bool CanUpdate { get; set; } = false;


    public bool CanDelete { get; set; } = false;

    public virtual Function Function { get; set; } = null!;

    public virtual Role Role { get; set; } = null!;
}
