using Kantoku.SharedKernel;
using Kantoku.SharedKernel.ValueObjects;

namespace Kantoku.Domain.OrganizationManagement;

/// <summary>
/// Entity representing a job position within an organization
/// </summary>
public class Position : Entity<PositionId>
{
    public OrganizationId OrgId { get; private set; }
    public string PositionCode { get; private set; } = null!;
    public string PositionName { get; private set; } = null!;
    public string? Description { get; private set; }
    public bool IsHidden { get; private set; } = false;
    public bool IsDefault { get; private set; } = false;
    public bool IsDeleted { get; private set; } = false;

    // Private constructor for EF Core
    private Position() : base() { }

    /// <summary>
    /// Creates a new position
    /// </summary>
    public Position(
        PositionId id,
        OrganizationId orgId,
        string positionCode,
        string positionName,
        string? description = null) : base(id)
    {
        OrgId = orgId;
        SetPositionCode(positionCode);
        SetPositionName(positionName);
        Description = description;
    }

    /// <summary>
    /// Updates position information
    /// </summary>
    public void Update(
        string positionName,
        string? description = null)
    {
        SetPositionName(positionName);
        Description = description;
    }

    /// <summary>
    /// Sets the position as default
    /// </summary>
    public void SetAsDefault()
    {
        IsDefault = true;
    }

    /// <summary>
    /// Unsets the position as default
    /// </summary>
    public void UnsetAsDefault()
    {
        IsDefault = false;
    }

    /// <summary>
    /// Hides the position
    /// </summary>
    public void Hide()
    {
        IsHidden = true;
    }

    /// <summary>
    /// Shows the position
    /// </summary>
    public void Show()
    {
        IsHidden = false;
    }

    /// <summary>
    /// Soft deletes the position
    /// </summary>
    public void Delete()
    {
        IsDeleted = true;
    }

    /// <summary>
    /// Restores the position
    /// </summary>
    public void Restore()
    {
        IsDeleted = false;
    }

    // Private helper methods
    private void SetPositionCode(string positionCode)
    {
        if (string.IsNullOrWhiteSpace(positionCode))
            throw new ArgumentException("Position code cannot be null or empty", nameof(positionCode));

        if (positionCode.Length > 50)
            throw new ArgumentException("Position code cannot exceed 50 characters", nameof(positionCode));

        PositionCode = positionCode.Trim();
    }

    private void SetPositionName(string positionName)
    {
        if (string.IsNullOrWhiteSpace(positionName))
            throw new ArgumentException("Position name cannot be null or empty", nameof(positionName));

        if (positionName.Length > 200)
            throw new ArgumentException("Position name cannot exceed 200 characters", nameof(positionName));

        PositionName = positionName.Trim();
    }
}
