namespace Kantoku.Domain.Models;

public class ConstructionCost : AuditableEntity
{
    public Guid ConstructionCostUid { get; set; }
    public Guid OrgUid { get; set; }
    public Guid ConstructionUid { get; set; }


    public DateOnly StartDate { get; set; }


    public DateOnly EndDate { get; set; }


    public long TotalCostAmount { get; set; }


    public int RiskModifiedAmount { get; set; }

    public bool IsDeleted { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Construction Construction { get; set; } = null!;

    public virtual ConstructionPaymentRequest ConstructionPaymentRequest { get; set; } = null!;

    public virtual ICollection<CategorizedCost> CategorizedCosts { get; set; } = [];
}