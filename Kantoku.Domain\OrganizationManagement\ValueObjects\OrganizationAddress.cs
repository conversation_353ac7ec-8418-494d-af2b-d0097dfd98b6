using Kantoku.SharedKernel;

namespace Kantoku.Domain.OrganizationManagement.ValueObjects;

/// <summary>
/// Value object representing an organization's address
/// </summary>
public class OrganizationAddress : ValueObject
{
    public string? PostalCode { get; private set; }
    public string? Address { get; private set; }

    private OrganizationAddress() { } // For EF Core

    public OrganizationAddress(string? postalCode, string? address)
    {
        PostalCode = postalCode?.Trim();
        Address = address?.Trim();

        Validate();
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return PostalCode;
        yield return Address;
    }

    private void Validate()
    {
        if (!string.IsNullOrEmpty(PostalCode) && PostalCode.Length > 20)
            throw new ArgumentException("Postal code cannot exceed 20 characters");

        if (!string.IsNullOrEmpty(Address) && Address.Length > 500)
            throw new ArgumentException("Address cannot exceed 500 characters");
    }

    public override string ToString()
    {
        var parts = new List<string>();
        
        if (!string.IsNullOrEmpty(PostalCode))
            parts.Add($"〒{PostalCode}");
            
        if (!string.IsNullOrEmpty(Address))
            parts.Add(Address);

        return string.Join(" ", parts);
    }
}
