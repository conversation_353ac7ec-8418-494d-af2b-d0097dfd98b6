using Kantoku.Domain.Common.BuildingBlocks;

namespace Kantoku.Application.Specification;

public static class SpecificationEvaluator
{
    public static IQueryable<TEntity> GetQuery<TEntity>(
            IQueryable<TEntity> inputQueryable,
            Specification<TEntity> specification) where TEntity : Entity
    {
        IQueryable<TEntity> query = inputQueryable;

        if (specification.Criteria is not null)
        {
            query = query.Where(specification.Criteria);
        }

        // if (specification.IncludesExpressions is not null)
        // {
        //     query = specification.IncludesExpressions
        //     .Aggregate(query, (current, includeExpression) => current.Include(includeExpression));
        // }

        if (specification.IsPagingEnabled)
        {
            query = query.Skip(specification.Skip).Take(specification.Take);
        }

        if (specification.OrderByExpression is not null)
        {
            query = query.OrderBy(specification.OrderByExpression);
        }
        else if (specification.OrderByDescendingExpression is not null)
        {
            query = query.OrderByDescending(specification.OrderByDescendingExpression);
        }

        if (specification.GroupByExpression is not null)
        {
            query = query.GroupBy(specification.GroupByExpression).SelectMany(x => x);
        }


        return query;
    }
}
