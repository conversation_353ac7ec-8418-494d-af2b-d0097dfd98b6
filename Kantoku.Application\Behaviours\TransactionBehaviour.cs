using Kantoku.Application.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Kantoku.Application.Behaviours
{
    /// <summary>
    /// Behavior that wraps requests in a database transaction
    /// </summary>
    /// <typeparam name="TRequest">The request type</typeparam>
    /// <typeparam name="TResponse">The response type</typeparam>
    public class TransactionBehaviour<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
        where TRequest : notnull
    {
        private readonly ILogger<TransactionBehaviour<TRequest, TResponse>> _logger;
        private readonly IUnitOfWork _unitOfWork;

        public TransactionBehaviour(
            ILogger<TransactionBehaviour<TRequest, TResponse>> logger,
            IUnitOfWork unitOfWork)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        public async Task<TResponse> Handle(
            TRequest request, 
            RequestHandlerDelegate<TResponse> next, 
            CancellationToken cancellationToken)
        {
            // Skip transactions for queries to improve performance
            if (request is Interfaces.IQuery<TResponse>)
            {
                return await next();
            }

            var requestName = typeof(TRequest).Name;

            try
            {
                // Begin transaction
                await _unitOfWork.BeginTransactionAsync();
                _logger.LogInformation("Beginning transaction for {RequestName}", requestName);

                // Handle the request
                var response = await next();

                // Commit transaction
                await _unitOfWork.CommitTransactionAsync();
                _logger.LogInformation("Committed transaction for {RequestName}", requestName);

                return response;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                _logger.LogError(ex, "Error handling transaction for {RequestName}, rolling back", requestName);
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }
    }
} 