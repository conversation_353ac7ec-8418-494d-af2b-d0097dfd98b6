using Kantoku.SharedKernel;

namespace Kantoku.Domain.EmployeeManagement;

/// <summary>
/// Entity representing an employee's role assignment
/// </summary>
public class EmployeeRole : Entity<Guid>
{
    public Guid EmployeeId { get; private set; }
    public Guid RoleId { get; private set; }
    public DateTime AssignedDate { get; private set; }
    public DateTime? RevokedDate { get; private set; }
    public bool IsActive { get; private set; } = true;

    // Private constructor for EF Core
    private EmployeeRole() : base() { }

    /// <summary>
    /// Creates a new employee role assignment
    /// </summary>
    public EmployeeRole(
        Guid id,
        Guid employeeId,
        Guid roleId,
        DateTime? assignedDate = null) : base(id)
    {
        EmployeeId = employeeId;
        RoleId = roleId;
        AssignedDate = assignedDate ?? DateTime.UtcNow;
    }

    /// <summary>
    /// Revokes the role assignment
    /// </summary>
    public void Revoke()
    {
        if (!IsActive)
            throw new InvalidOperationException("Role assignment is already revoked");

        IsActive = false;
        RevokedDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Reactivates the role assignment
    /// </summary>
    public void Reactivate()
    {
        if (IsActive)
            throw new InvalidOperationException("Role assignment is already active");

        IsActive = true;
        RevokedDate = null;
    }
}
