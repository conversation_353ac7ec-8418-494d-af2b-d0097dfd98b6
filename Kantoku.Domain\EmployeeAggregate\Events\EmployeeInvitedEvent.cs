using Kantoku.Domain.Common.BuildingBlocks;

namespace Kantoku.Domain.EmployeeAggregate.Events;

public class EmployeeInvitedEvent : DomainEvent
{
    public Employee InvitedEmployee { get; }
    public Employee InviterEmployee { get; }

    public EmployeeInvitedEvent(Employee invitedEmployee, Employee inviterEmployee)
    {
        InvitedEmployee = invitedEmployee;
        InviterEmployee = inviterEmployee;
    }
}