namespace Kantoku.Domain.EmployeeManagement;

/// <summary>
/// Repository interface for Employee aggregate
/// </summary>
public interface IEmployeeRepository
{
    /// <summary>
    /// Gets an employee by their ID
    /// </summary>
    Task<Employee?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets an employee by their code within an organization
    /// </summary>
    Task<Employee?> GetByCodeAsync(Guid orgId, string employeeCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets an employee by their account ID
    /// </summary>
    Task<Employee?> GetByAccountIdAsync(Guid accountId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all active employees for an organization
    /// </summary>
    Task<IEnumerable<Employee>> GetByOrganizationAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets employees by structure within an organization
    /// </summary>
    Task<IEnumerable<Employee>> GetByStructureAsync(Guid orgId, Guid structureId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets employees by position within an organization
    /// </summary>
    Task<IEnumerable<Employee>> GetByPositionAsync(Guid orgId, Guid positionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets employees by ranking within an organization
    /// </summary>
    Task<IEnumerable<Employee>> GetByRankingAsync(Guid orgId, Guid rankingId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets employees with approval authority within an organization
    /// </summary>
    Task<IEnumerable<Employee>> GetWithApprovalAuthorityAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets organization admins within an organization
    /// </summary>
    Task<IEnumerable<Employee>> GetOrgAdminsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an employee with the given code exists within an organization
    /// </summary>
    Task<bool> ExistsByCodeAsync(Guid orgId, string employeeCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an employee with the given code exists within an organization (excluding the specified ID)
    /// </summary>
    Task<bool> ExistsByCodeAsync(Guid orgId, string employeeCode, Guid excludeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new employee
    /// </summary>
    Task AddAsync(Employee employee, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing employee
    /// </summary>
    void Update(Employee employee);

    /// <summary>
    /// Removes an employee
    /// </summary>
    void Remove(Employee employee);

    /// <summary>
    /// Gets employee leaves for a specific employee
    /// </summary>
    Task<IEnumerable<EmployeeLeave>> GetLeavesAsync(Guid employeeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets employee leaves for a specific employee within a date range
    /// </summary>
    Task<IEnumerable<EmployeeLeave>> GetLeavesAsync(Guid employeeId, DateOnly startDate, DateOnly endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets employee shifts for a specific employee
    /// </summary>
    Task<IEnumerable<EmployeeShift>> GetShiftsAsync(Guid employeeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets employee shifts for a specific employee within a date range
    /// </summary>
    Task<IEnumerable<EmployeeShift>> GetShiftsAsync(Guid employeeId, DateOnly startDate, DateOnly endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets employee roles for a specific employee
    /// </summary>
    Task<IEnumerable<EmployeeRole>> GetRolesAsync(Guid employeeId, CancellationToken cancellationToken = default);
}
