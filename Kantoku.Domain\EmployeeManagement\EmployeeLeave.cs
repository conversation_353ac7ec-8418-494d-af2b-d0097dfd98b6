using Kantoku.SharedKernel;

namespace Kantoku.Domain.EmployeeManagement;

/// <summary>
/// Entity representing an employee's leave record
/// </summary>
public class EmployeeLeave : Entity<Guid>
{
    public Guid EmployeeId { get; private set; }
    public string LeaveTypeCode { get; private set; } = null!;
    public DateOnly StartDate { get; private set; }
    public DateOnly EndDate { get; private set; }
    public decimal Days { get; private set; }
    public string? Reason { get; private set; }
    public string Status { get; private set; } = "PENDING";
    public Guid? ApprovedBy { get; private set; }
    public DateTime? ApprovedDate { get; private set; }
    public string? ApprovalComments { get; private set; }

    // Private constructor for EF Core
    private EmployeeLeave() : base() { }

    /// <summary>
    /// Creates a new employee leave record
    /// </summary>
    public EmployeeLeave(
        Guid id,
        Guid employeeId,
        string leaveTypeCode,
        DateOnly startDate,
        DateOnly endDate,
        string? reason = null) : base(id)
    {
        EmployeeId = employeeId;
        SetLeaveTypeCode(leaveTypeCode);
        SetDates(startDate, endDate);
        Reason = reason;
        CalculateDays();
    }

    /// <summary>
    /// Updates the leave dates
    /// </summary>
    public void UpdateDates(DateOnly startDate, DateOnly endDate)
    {
        if (Status != "PENDING")
            throw new InvalidOperationException("Cannot update dates for non-pending leave");

        SetDates(startDate, endDate);
        CalculateDays();
    }

    /// <summary>
    /// Updates the leave reason
    /// </summary>
    public void UpdateReason(string? reason)
    {
        if (Status != "PENDING")
            throw new InvalidOperationException("Cannot update reason for non-pending leave");

        Reason = reason;
    }

    /// <summary>
    /// Approves the leave request
    /// </summary>
    public void Approve(Guid approvedBy, string? comments = null)
    {
        if (Status != "PENDING")
            throw new InvalidOperationException("Leave is not in pending status");

        Status = "APPROVED";
        ApprovedBy = approvedBy;
        ApprovedDate = DateTime.UtcNow;
        ApprovalComments = comments;
    }

    /// <summary>
    /// Rejects the leave request
    /// </summary>
    public void Reject(Guid rejectedBy, string? comments = null)
    {
        if (Status != "PENDING")
            throw new InvalidOperationException("Leave is not in pending status");

        Status = "REJECTED";
        ApprovedBy = rejectedBy;
        ApprovedDate = DateTime.UtcNow;
        ApprovalComments = comments;
    }

    /// <summary>
    /// Cancels the leave request
    /// </summary>
    public void Cancel()
    {
        if (Status == "CANCELLED")
            throw new InvalidOperationException("Leave is already cancelled");

        Status = "CANCELLED";
    }

    /// <summary>
    /// Checks if this leave overlaps with another leave
    /// </summary>
    public bool OverlapsWith(EmployeeLeave other)
    {
        if (other == null)
            return false;

        return StartDate <= other.EndDate && EndDate >= other.StartDate;
    }

    /// <summary>
    /// Checks if the leave is approved
    /// </summary>
    public bool IsApproved => Status == "APPROVED";

    /// <summary>
    /// Checks if the leave is pending
    /// </summary>
    public bool IsPending => Status == "PENDING";

    /// <summary>
    /// Checks if the leave is rejected
    /// </summary>
    public bool IsRejected => Status == "REJECTED";

    /// <summary>
    /// Checks if the leave is cancelled
    /// </summary>
    public bool IsCancelled => Status == "CANCELLED";

    // Private helper methods
    private void SetLeaveTypeCode(string leaveTypeCode)
    {
        if (string.IsNullOrWhiteSpace(leaveTypeCode))
            throw new ArgumentException("Leave type code cannot be null or empty", nameof(leaveTypeCode));

        LeaveTypeCode = leaveTypeCode.Trim();
    }

    private void SetDates(DateOnly startDate, DateOnly endDate)
    {
        if (startDate > endDate)
            throw new ArgumentException("Start date cannot be after end date");

        if (startDate < DateOnly.FromDateTime(DateTime.Today))
            throw new ArgumentException("Start date cannot be in the past");

        StartDate = startDate;
        EndDate = endDate;
    }

    private void CalculateDays()
    {
        Days = EndDate.DayNumber - StartDate.DayNumber + 1;
    }
}
