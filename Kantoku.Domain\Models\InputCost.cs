﻿namespace Kantoku.Domain.Models;

public class InputCost : AuditableEntity
{
    public Guid InputCostUid { get; set; }
    public Guid ConstructionUid { get; set; }


    public Guid? EntryTypeUid { get; set; }


    public Guid? VendorUid { get; set; }


    public Guid? PaymentTypeUid { get; set; }


    public string? Title { get; set; }


    public DateOnly IssueDate { get; set; }


    public DateOnly? PaymentDate { get; set; }


    public string OriginalNumber { get; set; } = null!;


    public string? Description { get; set; }


    public long? TotalAmount { get; set; }

    public ICollection<string>? ImageUrls { get; set; }

    public Guid OrgUid { get; set; }
    public bool IsDeleted { get; set; }

    public virtual EntryType? EntryType { get; set; }

    public virtual Vendor? Vendor { get; set; }

    public virtual PaymentType? PaymentType { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Construction Construction { get; set; } = null!;

    public virtual ICollection<InputCostItem> InputCostItems { get; set; } = [];
}
