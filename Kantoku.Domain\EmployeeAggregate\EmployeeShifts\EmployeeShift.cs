﻿using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.EmployeeAggregate.EmployeeShifts.Events;

namespace Kantoku.Domain.EmployeeAggregate.EmployeeShifts;

public class EmployeeShift : FullAuditedEntity<Guid>
{
    public Guid? ProjectScheduleId { get; private set; }
    public Guid? MonthlyReportId { get; private set; }

    public Guid EmployeeId { get; private set; }
    public Guid ProjectId { get; private set; }

    public string? WorkingLocation { get; private set; }
    public DateTime? ScheduledStartTime { get; private set; }
    public DateTime? CheckInTime { get; private set; }
    public int? CheckInTimeLastModifier { get; private set; }
    public string? CheckInLocation { get; private set; }

    public DateTime? ScheduledEndTime { get; private set; }
    public DateTime? CheckOutTime { get; private set; }
    public int? CheckOutTimeLastModifier { get; private set; }
    public DateTime? AutoCheckOutTime { get; private set; }
    public string? CheckOutLocation { get; private set; }

    public List<EmployeeShiftBreakTime>? EmployeeShiftBreakTimes { get; private set; }
    public int? BreakTimeLastModifier { get; private set; }

    public float TotalScheduledWorkTime { get; private set; }
    public float TotalWorkTime { get; private set; }
    public float TotalBreakTime { get; private set; }
    public float TotalOverTime { get; private set; }

    public bool IsRequested { get; private set; }
    public bool? IsApproved { get; private set; }
    public Guid? ApprovedBy { get; private set; }
    public DateTime? ApprovedTime { get; private set; }

    public string? Description { get; private set; }
    public string? AssignedRole { get; private set; }

    public Employee Employee { get; private set; } = null!;
    public Employee? Approver { get; private set; }


    public EmployeeShift(Guid id,
        Guid employeeId,
        Guid projectId,
        DateTime scheduledStartTime,
        DateTime scheduledEndTime,
        float totalScheduledWorkTime) : base(id)
    {
        EmployeeId = employeeId;
        ProjectId = projectId;
        ScheduledStartTime = scheduledStartTime;
        ScheduledEndTime = scheduledEndTime;
        TotalScheduledWorkTime = totalScheduledWorkTime;

        AddDomainEvent(new ScheduledEmployeeShiftCreatedEvent(this));
    }

    public EmployeeShift(Guid id,
        Guid employeeId,
        Guid projectId,
        DateTime checkInTime) : base(id)
    {
        EmployeeId = employeeId;
        ProjectId = projectId;
        CheckInTime = checkInTime;

        AddDomainEvent(new EmployeeShiftCreatedEvent(this));
    }

    internal static EmployeeShift CreateScheduledEmployeeShift(
        Guid id,
        Guid employeeId,
        Guid projectId,
        DateTime scheduledStartTime,
        DateTime scheduledEndTime,
        float totalScheduledWorkTime)
    {
        return new EmployeeShift(id, employeeId, projectId, scheduledStartTime, scheduledEndTime, totalScheduledWorkTime);
    }

    internal void UpdateScheduledEmployeeShift(
        DateTime? scheduledStartTime,
        DateTime? scheduledEndTime,
        float? totalScheduledWorkTime)
    {
        ScheduledStartTime = scheduledStartTime ?? ScheduledStartTime;
        ScheduledEndTime = scheduledEndTime ?? ScheduledEndTime;
        TotalScheduledWorkTime = totalScheduledWorkTime ?? TotalScheduledWorkTime;
    }

    internal static EmployeeShift CreateCheckInEmployeeShift(
        Guid id,
        Guid employeeId,
        Guid projectId,
        DateTime checkInTime)
    {
        return new EmployeeShift(id, employeeId, projectId, checkInTime);
    }

    internal void CheckIn(
        DateTime? checkInTime)
    {
        CheckInTime = checkInTime ?? CheckInTime;

        AddDomainEvent(new EmployeeShiftUpdatedEvent(this));
    }

    internal void BreakIn(
        DateTime? breakInTime)
    {
        if (EmployeeShiftBreakTimes is null)
        {
            EmployeeShiftBreakTimes = new List<EmployeeShiftBreakTime> { new EmployeeShiftBreakTime(breakInTime, null) };
        }
        else
        {
            EmployeeShiftBreakTimes.Add(new EmployeeShiftBreakTime(breakInTime, null));
        }

        AddDomainEvent(new EmployeeShiftUpdatedEvent(this));
    }

    internal void BreakOut(
        DateTime? breakOutTime)
    {
        if (EmployeeShiftBreakTimes is null)
        {
            EmployeeShiftBreakTimes = new List<EmployeeShiftBreakTime> { new EmployeeShiftBreakTime(null, breakOutTime) };
        }
        else
        {
            EmployeeShiftBreakTimes.Add(new EmployeeShiftBreakTime(null, breakOutTime));
        }

        AddDomainEvent(new EmployeeShiftUpdatedEvent(this));
    }

    internal void CheckOut(
        DateTime? checkOutTime)
    {
        CheckOutTime = checkOutTime ?? CheckOutTime;

        AddDomainEvent(new EmployeeShiftUpdatedEvent(this));
    }

    internal void UpdateEmployeeShift(
        Guid? projectId,
        string? workingLocation,
        DateTime? checkOutTime,
        DateTime? checkInTime,
        List<EmployeeShiftBreakTime>? employeeShiftBreakTimes)
    {   
        ProjectId = projectId ?? ProjectId;
        WorkingLocation = workingLocation ?? WorkingLocation;
        CheckOutTime = checkOutTime ?? CheckOutTime;
        CheckInTime = checkInTime ?? CheckInTime;
        EmployeeShiftBreakTimes = employeeShiftBreakTimes ?? EmployeeShiftBreakTimes;

        AddDomainEvent(new EmployeeShiftUpdatedEvent(this));
    }
}
