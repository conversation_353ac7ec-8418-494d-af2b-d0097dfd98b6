namespace Kantoku.SharedKernel.Time;

/// <summary>
/// Fixed implementation of IClock that returns a predetermined time.
/// This is useful for testing scenarios where you need predictable time values.
/// </summary>
public class FixedClock : IClock
{
    private readonly DateTime _fixedDateTime;
    private readonly DateTimeOffset _fixedDateTimeOffset;

    /// <summary>
    /// Initializes a new instance of the FixedClock class with the specified UTC time
    /// </summary>
    /// <param name="fixedUtcDateTime">The fixed UTC date and time to return</param>
    public FixedClock(DateTime fixedUtcDateTime)
    {
        if (fixedUtcDateTime.Kind != DateTimeKind.Utc)
            throw new ArgumentException("Fixed date time must be in UTC", nameof(fixedUtcDateTime));

        _fixedDateTime = fixedUtcDateTime;
        _fixedDateTimeOffset = new DateTimeOffset(fixedUtcDateTime);
    }

    /// <summary>
    /// Initializes a new instance of the FixedClock class with the specified DateTimeOffset
    /// </summary>
    /// <param name="fixedDateTimeOffset">The fixed date and time offset to return</param>
    public FixedClock(DateTimeOffset fixedDateTimeOffset)
    {
        _fixedDateTimeOffset = fixedDateTimeOffset;
        _fixedDateTime = fixedDateTimeOffset.UtcDateTime;
    }

    /// <summary>
    /// Gets the fixed UTC date and time
    /// </summary>
    public DateTime UtcNow => _fixedDateTime;

    /// <summary>
    /// Gets the fixed local date and time
    /// </summary>
    public DateTime Now => _fixedDateTime.ToLocalTime();

    /// <summary>
    /// Gets the fixed date (without time component)
    /// </summary>
    public DateTime Today => _fixedDateTime.ToLocalTime().Date;

    /// <summary>
    /// Gets the fixed UTC date and time as DateTimeOffset
    /// </summary>
    public DateTimeOffset UtcNowOffset => _fixedDateTimeOffset.ToUniversalTime();

    /// <summary>
    /// Gets the fixed local date and time as DateTimeOffset
    /// </summary>
    public DateTimeOffset NowOffset => _fixedDateTimeOffset.ToLocalTime();

    /// <summary>
    /// Creates a FixedClock with the current system time
    /// </summary>
    /// <returns>A FixedClock instance with the current UTC time</returns>
    public static FixedClock FromCurrentTime()
    {
        return new FixedClock(DateTime.UtcNow);
    }

    /// <summary>
    /// Creates a FixedClock with a specific date (time set to midnight UTC)
    /// </summary>
    /// <param name="year">The year</param>
    /// <param name="month">The month</param>
    /// <param name="day">The day</param>
    /// <returns>A FixedClock instance with the specified date</returns>
    public static FixedClock FromDate(int year, int month, int day)
    {
        return new FixedClock(new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Utc));
    }

    /// <summary>
    /// Creates a FixedClock with a specific date and time in UTC
    /// </summary>
    /// <param name="year">The year</param>
    /// <param name="month">The month</param>
    /// <param name="day">The day</param>
    /// <param name="hour">The hour</param>
    /// <param name="minute">The minute</param>
    /// <param name="second">The second</param>
    /// <returns>A FixedClock instance with the specified date and time</returns>
    public static FixedClock FromDateTime(int year, int month, int day, int hour, int minute, int second)
    {
        return new FixedClock(new DateTime(year, month, day, hour, minute, second, DateTimeKind.Utc));
    }
}
