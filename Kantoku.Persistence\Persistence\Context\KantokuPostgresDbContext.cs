using Kantoku.Application.Interfaces;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System.Reflection;
using Kantoku.Domain.Common.Auditing; 
using System.Text.Json;
using System.Text.Json.Serialization; 
using Kantoku.Domain.Common.Attributes; 

namespace Kantoku.Persistence.Context
{
    public class KantokuPostgresDbContext : DbContext, IUnitOfWork // IUnitOfWork can be implemented by DbContext
    {
        private readonly IDomainEventService _domainEventService;
        private readonly IDbContextSchema? _schema; // Optional schema provider
        private readonly IApplicationContextProvider _applicationContextProvider; // Added

        // Example: DbSet for one of your entities (add others as needed)
        // public DbSet<Employee> Employees { get; set; }

        // DbSet for AuditLog from Kantoku.Domain.Models
        public DbSet<AuditLog> AuditLogs { get; set; } = null!;

        public KantokuPostgresDbContext(
            DbContextOptions<KantokuPostgresDbContext> options,
            IDomainEventService domainEventService,
            IApplicationContextProvider applicationContextProvider, // Added
            IDbContextSchema? schema = null) : base(options)
        {
            _domainEventService = domainEventService ?? throw new ArgumentNullException(nameof(domainEventService));
            _applicationContextProvider = applicationContextProvider ?? throw new ArgumentNullException(nameof(applicationContextProvider)); // Added
            _schema = schema;
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            if (_schema != null && !string.IsNullOrWhiteSpace(_schema.Schema))
            {
                modelBuilder.HasDefaultSchema(_schema.Schema);
            }

            // Ensure AuditLog entity is configured if needed (e.g., if it has specific requirements)
            // modelBuilder.Entity<AuditLog>().ToTable("AuditLogs", _schema?.Schema ?? "public");
            // If AuditLog uses Account navigation, ensure Account is also known to this DbContext
            // or configure the relationship appropriately.

            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            base.OnModelCreating(modelBuilder);
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            await OnBeforeSaveChangesLogic(cancellationToken); // Auditing logic call
            await DispatchDomainEventsAsync(cancellationToken);
            return await base.SaveChangesAsync(cancellationToken);
        }

        public override int SaveChanges()
        {
            OnBeforeSaveChangesLogic(default).GetAwaiter().GetResult(); // Synchronous wrapper for auditing logic
            DispatchDomainEventsAsync(default).GetAwaiter().GetResult(); // Consider if domain events should also be dispatched synchronously here
            return base.SaveChanges();
        }

        private async Task OnBeforeSaveChangesLogic(CancellationToken cancellationToken = default)
        {
            ChangeTracker.DetectChanges();

            var userId = _applicationContextProvider.Context?.UserId; // Changed to use IApplicationContextProvider
            var timestamp = DateTime.UtcNow;

            HandleSoftDeletes(userId, timestamp);
            HandleAuditProperties(userId, timestamp);

            // Await this task if it becomes truly async (e.g., I/O bound operations for audit log prep)
            // For now, it populates a list that is then batch-added to the DbContext, 
            // so making it part of the same transaction flow is key.
            // It doesn't call SaveChanges itself, so it's safe to call before base.SaveChangesAsync.
            await PrepareDetailedAuditLogs(userId, timestamp, cancellationToken);
        }

        private void HandleAuditProperties(Guid? userId, DateTime timestamp)
        {
            // Logic for old IAuditableEntity (if it exists in Kantoku.Domain.Models and is used)
            // This part assumes an IAuditableEntity interface similar to the one previously in Kantoku.Api
            // If Kantoku.Domain.Models.IAuditableEntity is different or not used, this block might need adjustment/removal.
            try
            { // Defensive coding in case IAuditableEntity isn't found or properties are different
                var oldAuditableEntries = ChangeTracker.Entries<IAuditableEntity>();
                foreach (var entry in oldAuditableEntries)
                {
                    string? userIdString = userId?.ToString();
                    dynamic entity = entry.Entity;

                    if (entry.State == EntityState.Added)
                    {
                        if (entity.GetType().GetProperty("CreatedBy") != null) entity.CreatedBy = userIdString;
                        if (entity.GetType().GetProperty("CreatedTime") != null) entity.CreatedTime = timestamp;
                        if (entity.GetType().GetProperty("LastModifiedBy") != null) entity.LastModifiedBy = userIdString;
                        if (entity.GetType().GetProperty("LastModifiedTime") != null) entity.LastModifiedTime = timestamp;
                    }
                    else if (entry.State == EntityState.Modified)
                    {
                        if (entity.GetType().GetProperty("LastModifiedBy") != null) entity.LastModifiedBy = userIdString;
                        if (entity.GetType().GetProperty("LastModifiedTime") != null) entity.LastModifiedTime = timestamp;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log this error - indicates a mismatch with expected IAuditableEntity structure
                // Consider using a logger injected into the DbContext if available, or rethrow if critical
                System.Diagnostics.Debug.WriteLine($"Error handling old IAuditableEntity: {ex.Message}");
            }

            foreach (var entry in ChangeTracker.Entries())
            {
                if (entry.State == EntityState.Added)
                {
                    if (entry.Entity is ICreationAudited creationAuditedEntity)
                    {
                        creationAuditedEntity.CreatedAtUtc = timestamp;
                        creationAuditedEntity.CreatedBy = userId;
                    }
                    if (entry.Entity is IModificationAudited modificationAuditedEntityOnCreate)
                    {
                        modificationAuditedEntityOnCreate.LastModifiedAtUtc = timestamp;
                        modificationAuditedEntityOnCreate.LastModifiedBy = userId;
                    }
                }
                else if (entry.State == EntityState.Modified)
                {
                    if (entry.Entity is IModificationAudited modificationAuditedEntity)
                    {
                        bool isSoftDeleteModification = false;
                        if (entry.Entity is ISoftDeleteAudited sd && sd.IsDeleted)
                        {
                            var originalIsDeleted = false;
                            try { originalIsDeleted = entry.OriginalValues.GetValue<bool>(nameof(ISoftDeleteAudited.IsDeleted)); } catch { }
                            if (!originalIsDeleted && sd.IsDeleted) isSoftDeleteModification = true;
                        }

                        if (isSoftDeleteModification || entry.Properties.Any(p => p.IsModified &&
                                                  p.Metadata.Name != nameof(IModificationAudited.LastModifiedAtUtc) &&
                                                  p.Metadata.Name != nameof(IModificationAudited.LastModifiedBy)))
                        {
                            modificationAuditedEntity.LastModifiedAtUtc = timestamp;
                            modificationAuditedEntity.LastModifiedBy = userId;
                        }
                        else if (modificationAuditedEntity.LastModifiedAtUtc == null)
                        {
                            modificationAuditedEntity.LastModifiedAtUtc = timestamp;
                            modificationAuditedEntity.LastModifiedBy = userId;
                        }
                    }
                }
            }
        }

        private void HandleSoftDeletes(Guid? userId, DateTime timestamp)
        {
            var softDeleteEntries = ChangeTracker.Entries<ISoftDeleteAudited>()
                .Where(e => e.State == EntityState.Deleted)
                .ToList();

            foreach (var entry in softDeleteEntries)
            {
                entry.State = EntityState.Modified;
                var entity = entry.Entity;
                entity.IsDeleted = true;
                entity.DeletedAtUtc = timestamp;
                entity.DeletedBy = userId;

                if (entity is IModificationAudited modificationAuditedEntity)
                {
                    modificationAuditedEntity.LastModifiedAtUtc = timestamp;
                    modificationAuditedEntity.LastModifiedBy = userId;
                }
            }
        }

        private async Task PrepareDetailedAuditLogs(Guid? userId, DateTime timestamp, CancellationToken cancellationToken)
        {
            ChangeTracker.DetectChanges();

            var entriesToAudit = ChangeTracker.Entries()
                .Where(e => e.State != EntityState.Unchanged && e.State != EntityState.Detached && !(e.Entity is AuditLog))
                .ToList();

            var auditLogRecords = new List<AuditLog>();

            foreach (var entry in entriesToAudit)
            {
                var auditLog = CreateAuditLogRecord(entry, userId, timestamp);
                if (auditLog != null)
                {
                    auditLogRecords.Add(auditLog);
                }
            }

            if (auditLogRecords.Count != 0)
            {
                await AuditLogs.AddRangeAsync(auditLogRecords, cancellationToken);
            }
        }

        private AuditLog? CreateAuditLogRecord(EntityEntry entry, Guid? userId, DateTime timestampUtc)
        {
            string actionType;
            Dictionary<string, object?> oldValues = new();
            Dictionary<string, object?> newValues = new();
            object? changesPayload = null;

            var entityType = entry.Metadata.GetTableName() ?? entry.Entity.GetType().Name;

            switch (entry.State)
            {
                case EntityState.Added:
                    actionType = "CREATE";
                    newValues = GetChangedPropertyValues(entry, forCreate: true);
                    if (newValues.Any()) changesPayload = new { New = newValues };
                    else changesPayload = new { New = new Dictionary<string, object?>() }; // Ensure New key exists even if empty
                    break;

                case EntityState.Modified:
                    if (entry.Entity is ISoftDeleteAudited softDeletableEntity && softDeletableEntity.IsDeleted)
                    {
                        var originalIsDeleted = false;
                        try { originalIsDeleted = entry.OriginalValues.GetValue<bool>(nameof(ISoftDeleteAudited.IsDeleted)); } catch { }
                        actionType = (!originalIsDeleted && softDeletableEntity.IsDeleted) ? "SOFT_DELETE" : "UPDATE";
                    }
                    else
                    {
                        actionType = "UPDATE";
                    }

                    oldValues = GetChangedPropertyValues(entry, forOldValues: true);
                    newValues = GetChangedPropertyValues(entry, forNewValues: true);

                    if (actionType == "UPDATE" && !oldValues.Any() && !newValues.Any())
                    {
                        // If no scalar properties were actually modified for an UPDATE, skip logging.
                        // This relies on GetChangedPropertyValues only returning modified scalar props for updates.
                        return null;
                    }

                    if (oldValues.Any() || newValues.Any() || actionType == "SOFT_DELETE")
                    {
                        changesPayload = new { Old = oldValues.Any() ? oldValues : null, New = newValues.Any() ? newValues : null };
                    }
                    else return null;
                    break;

                case EntityState.Deleted:
                    actionType = "DELETE";
                    oldValues = GetChangedPropertyValues(entry, forDelete: true);
                    if (oldValues.Any()) changesPayload = new { Old = oldValues };
                    else return null;
                    break;

                default:
                    return null;
            }

            // If changesPayload is still null here (e.g. CREATE with no audited props, but we still want to log creation event)
            // For UPDATE/DELETE, if no specific properties changed, we might have returned null already.
            if (changesPayload == null && actionType == "CREATE")
            {
                changesPayload = new { New = new Dictionary<string, object?>() }; // Log empty New for CREATE if no props
            }
            else if (changesPayload == null) return null; // For other actions, if no changes, don't log

            return new AuditLog
            {
                AuditLogUid = Guid.NewGuid(), // Consider a sequential Guid or other strategy if preferred
                EntityType = entityType,
                EntityId = GetEntityId(entry),
                ActionType = actionType,
                UserId = userId,
                TimestampUtc = timestampUtc,
                Changes = JsonSerializer.Serialize(changesPayload, new JsonSerializerOptions { DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull })
            };
        }

        private static Dictionary<string, object?> GetChangedPropertyValues(EntityEntry entry, bool forCreate = false, bool forOldValues = false, bool forNewValues = false, bool forDelete = false)
        {
            var values = new Dictionary<string, object?>();
            foreach (var property in entry.Properties
                .Where(p =>
                    // Ensure PropertyInfo exists to check for custom attributes
                    p.Metadata.PropertyInfo != null && 
                    // Check for [AuditablePropertyAttribute]
                    p.Metadata.PropertyInfo.GetCustomAttributes(typeof(AuditablePropertyAttribute), true).Length != 0 && 
                    !p.Metadata.IsShadowProperty() &&
                    // Basic scalar property checks (can be refined further if complex types are also marked Auditable)
                    (!typeof(System.Collections.IEnumerable).IsAssignableFrom(p.Metadata.ClrType) || p.Metadata.ClrType == typeof(string)) &&
                    (!p.Metadata.ClrType.IsClass || p.Metadata.ClrType == typeof(string) || p.Metadata.GetValueConverter() != null) // Allow classes with value converters or strings
                ))
            {
                if (forCreate)
                {
                    values[property.Metadata.Name] = property.CurrentValue;
                }
                else if (forDelete)
                {
                    values[property.Metadata.Name] = property.OriginalValue;
                }
                else if (forOldValues && property.IsModified)
                {
                    values[property.Metadata.Name] = property.OriginalValue;
                }
                else if (forNewValues && property.IsModified)
                {
                    values[property.Metadata.Name] = property.CurrentValue;
                }
            }
            return values;
        }

        private string GetEntityId(EntityEntry entry)
        {
            var primaryKey = entry.Metadata.FindPrimaryKey();
            if (primaryKey == null) return "(Unknown Key)";

            var keyValues = primaryKey.Properties
                .Select(p => entry.Property(p.Name).CurrentValue)
                .ToList();

            if (!keyValues.Any() || keyValues.All(kv => kv == null || kv.Equals(GetDefaultValue(kv.GetType()))))
            {
                // Try original values if current key values are default (e.g. for deleted entities where PK might be reset by some ORMs)
                keyValues = primaryKey.Properties.Select(p => entry.Property(p.Name).OriginalValue).ToList();
                if (!keyValues.Any() || keyValues.All(kv => kv == null || kv.Equals(GetDefaultValue(kv.GetType()))))
                {
                    return "(Key not set or default)";
                }
            }

            return keyValues.Count == 1 ?
                   keyValues.First()?.ToString() ?? "(null)" :
                   JsonSerializer.Serialize(keyValues.Select(kv => kv?.ToString()));
        }
        private static object? GetDefaultValue(Type type)
        {
            if (type.IsValueType)
            {
                return Activator.CreateInstance(type);
            }
            return null;
        }

        // DispatchDomainEventsAsync and IUnitOfWork methods remain as they were
        private async Task DispatchDomainEventsAsync(CancellationToken cancellationToken = default)
        {
            // var domainEventEntities = ChangeTracker.Entries<AggregateRoot<System.Guid>>()
            //     .Select(po => po.Entity)
            //     .Where(po => po.DomainEvents.Any())
            //     .ToArray();

            // foreach (var entity in domainEventEntities)
            // {
            //     // Note: This publishes events before saving changes.
            //     // Consider Outbox pattern for more robust event publishing after transaction commit.
            //     await _domainEventService.PublishAsync(entity, cancellationToken);
            // }
            await Task.CompletedTask;
        }

        // IUnitOfWork explicit implementation for transaction control if not handled by a behavior
        public async Task BeginTransactionAsync()
        {
            if (Database.CurrentTransaction == null)
            {
                await Database.BeginTransactionAsync();
            }
        }

        public async Task CommitTransactionAsync()
        {
            try
            {
                await SaveChangesAsync(); // This will now include audit logic
                Database.CurrentTransaction?.Commit();
            }
            catch
            {
                await RollbackTransactionAsync();
                throw;
            }
            finally
            {
                if (Database.CurrentTransaction != null)
                {
                    Database.CurrentTransaction.Dispose();
                }
            }
        }

        public async Task RollbackTransactionAsync()
        {
            await Task.CompletedTask;
            // try
            // {
            //     Database.CurrentTransaction?.Rollback();
            // }
            // finally
            // {
            //     if (Database.CurrentTransaction != null)
            //     {
            //         Database.CurrentTransaction.Dispose();
            //     }
            // }
        }
    }
}