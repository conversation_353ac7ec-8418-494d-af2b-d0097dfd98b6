namespace Kantoku.SharedKernel.Pagination;

/// <summary>
/// Represents a paged result containing a subset of items and pagination metadata
/// </summary>
/// <typeparam name="T">The type of items in the result</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// Gets the items in the current page
    /// </summary>
    public IReadOnlyList<T> Items { get; private set; }

    /// <summary>
    /// Gets the current page number (1-based)
    /// </summary>
    public int PageNumber { get; private set; }

    /// <summary>
    /// Gets the page size (number of items per page)
    /// </summary>
    public int PageSize { get; private set; }

    /// <summary>
    /// Gets the total number of items across all pages
    /// </summary>
    public long TotalCount { get; private set; }

    /// <summary>
    /// Gets the total number of pages
    /// </summary>
    public int TotalPages { get; private set; }

    /// <summary>
    /// Gets a value indicating whether there is a previous page
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// Gets a value indicating whether there is a next page
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// Gets the number of the first item on the current page (1-based)
    /// </summary>
    public long FirstItemOnPage => TotalCount == 0 ? 0 : (PageNumber - 1) * PageSize + 1;

    /// <summary>
    /// Gets the number of the last item on the current page (1-based)
    /// </summary>
    public long LastItemOnPage => Math.Min(PageNumber * PageSize, TotalCount);

    /// <summary>
    /// Gets a value indicating whether this is the first page
    /// </summary>
    public bool IsFirstPage => PageNumber == 1;

    /// <summary>
    /// Gets a value indicating whether this is the last page
    /// </summary>
    public bool IsLastPage => PageNumber == TotalPages;

    /// <summary>
    /// Initializes a new instance of the PagedResult class
    /// </summary>
    /// <param name="items">The items in the current page</param>
    /// <param name="pageNumber">The current page number (1-based)</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalCount">The total number of items</param>
    public PagedResult(IEnumerable<T> items, int pageNumber, int pageSize, long totalCount)
    {
        if (pageNumber < 1)
            throw new ArgumentOutOfRangeException(nameof(pageNumber), "Page number must be greater than 0.");
        
        if (pageSize < 1)
            throw new ArgumentOutOfRangeException(nameof(pageSize), "Page size must be greater than 0.");
        
        if (totalCount < 0)
            throw new ArgumentOutOfRangeException(nameof(totalCount), "Total count cannot be negative.");

        Items = items?.ToList() ?? throw new ArgumentNullException(nameof(items));
        PageNumber = pageNumber;
        PageSize = pageSize;
        TotalCount = totalCount;
        TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
    }

    /// <summary>
    /// Creates an empty paged result
    /// </summary>
    /// <param name="pageNumber">The current page number</param>
    /// <param name="pageSize">The page size</param>
    /// <returns>An empty paged result</returns>
    public static PagedResult<T> Empty(int pageNumber = 1, int pageSize = 10)
    {
        return new PagedResult<T>([], pageNumber, pageSize, 0);
    }

    /// <summary>
    /// Creates a paged result from a collection
    /// </summary>
    /// <param name="source">The source collection</param>
    /// <param name="pageNumber">The current page number (1-based)</param>
    /// <param name="pageSize">The page size</param>
    /// <returns>A paged result</returns>
    public static PagedResult<T> Create(IEnumerable<T> source, int pageNumber, int pageSize)
    {
        var items = source?.ToList() ?? throw new ArgumentNullException(nameof(source));
        var totalCount = items.Count;
        var pagedItems = items.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
        
        return new PagedResult<T>(pagedItems, pageNumber, pageSize, totalCount);
    }

    /// <summary>
    /// Maps the items in the paged result to a different type
    /// </summary>
    /// <typeparam name="TResult">The target type</typeparam>
    /// <param name="mapper">The mapping function</param>
    /// <returns>A new paged result with mapped items</returns>
    public PagedResult<TResult> Map<TResult>(Func<T, TResult> mapper)
    {
        if (mapper == null)
            throw new ArgumentNullException(nameof(mapper));

        var mappedItems = Items.Select(mapper).ToList();
        return new PagedResult<TResult>(mappedItems, PageNumber, PageSize, TotalCount);
    }

    /// <summary>
    /// Gets pagination metadata
    /// </summary>
    /// <returns>Pagination metadata</returns>
    public PaginationMetadata GetMetadata()
    {
        return new PaginationMetadata
        {
            PageNumber = PageNumber,
            PageSize = PageSize,
            TotalCount = TotalCount,
            TotalPages = TotalPages,
            HasPreviousPage = HasPreviousPage,
            HasNextPage = HasNextPage,
            FirstItemOnPage = FirstItemOnPage,
            LastItemOnPage = LastItemOnPage,
            IsFirstPage = IsFirstPage,
            IsLastPage = IsLastPage
        };
    }
}

/// <summary>
/// Represents pagination metadata
/// </summary>
public class PaginationMetadata
{
    /// <summary>
    /// Gets or sets the current page number (1-based)
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Gets or sets the page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Gets or sets the total number of items
    /// </summary>
    public long TotalCount { get; set; }

    /// <summary>
    /// Gets or sets the total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether there is a previous page
    /// </summary>
    public bool HasPreviousPage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether there is a next page
    /// </summary>
    public bool HasNextPage { get; set; }

    /// <summary>
    /// Gets or sets the number of the first item on the current page
    /// </summary>
    public long FirstItemOnPage { get; set; }

    /// <summary>
    /// Gets or sets the number of the last item on the current page
    /// </summary>
    public long LastItemOnPage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether this is the first page
    /// </summary>
    public bool IsFirstPage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether this is the last page
    /// </summary>
    public bool IsLastPage { get; set; }
}
