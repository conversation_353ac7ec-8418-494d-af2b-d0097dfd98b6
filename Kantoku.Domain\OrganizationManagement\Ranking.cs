using Kantoku.SharedKernel;
using Kantoku.SharedKernel.ValueObjects;

namespace Kantoku.Domain.OrganizationManagement;

/// <summary>
/// Entity representing an employee ranking within an organization
/// </summary>
public class Ranking : Entity<RankingId>
{
    public OrganizationId OrgId { get; private set; }
    public string RankingCode { get; private set; } = null!;
    public string RankingName { get; private set; } = null!;
    public string? Description { get; private set; }
    public int Level { get; private set; }
    public decimal? BaseSalary { get; private set; }
    public bool IsHidden { get; private set; } = false;
    public bool IsDefault { get; private set; } = false;
    public bool IsDeleted { get; private set; } = false;

    // Private constructor for EF Core
    private Ranking() : base() { }

    /// <summary>
    /// Creates a new ranking
    /// </summary>
    public Ranking(
        RankingId id,
        OrganizationId orgId,
        string rankingCode,
        string rankingName,
        int level,
        string? description = null,
        decimal? baseSalary = null) : base(id)
    {
        OrgId = orgId;
        SetRankingCode(rankingCode);
        SetRankingName(rankingName);
        SetLevel(level);
        Description = description;
        BaseSalary = baseSalary;
    }

    /// <summary>
    /// Updates ranking information
    /// </summary>
    public void Update(
        string rankingName,
        int level,
        string? description = null,
        decimal? baseSalary = null)
    {
        SetRankingName(rankingName);
        SetLevel(level);
        Description = description;
        BaseSalary = baseSalary;
    }

    /// <summary>
    /// Updates the base salary for this ranking
    /// </summary>
    public void UpdateBaseSalary(decimal? baseSalary)
    {
        if (baseSalary.HasValue && baseSalary.Value < 0)
            throw new ArgumentException("Base salary cannot be negative", nameof(baseSalary));

        BaseSalary = baseSalary;
    }

    /// <summary>
    /// Sets the ranking as default
    /// </summary>
    public void SetAsDefault()
    {
        IsDefault = true;
    }

    /// <summary>
    /// Unsets the ranking as default
    /// </summary>
    public void UnsetAsDefault()
    {
        IsDefault = false;
    }

    /// <summary>
    /// Hides the ranking
    /// </summary>
    public void Hide()
    {
        IsHidden = true;
    }

    /// <summary>
    /// Shows the ranking
    /// </summary>
    public void Show()
    {
        IsHidden = false;
    }

    /// <summary>
    /// Soft deletes the ranking
    /// </summary>
    public void Delete()
    {
        IsDeleted = true;
    }

    /// <summary>
    /// Restores the ranking
    /// </summary>
    public void Restore()
    {
        IsDeleted = false;
    }

    // Private helper methods
    private void SetRankingCode(string rankingCode)
    {
        if (string.IsNullOrWhiteSpace(rankingCode))
            throw new ArgumentException("Ranking code cannot be null or empty", nameof(rankingCode));

        if (rankingCode.Length > 50)
            throw new ArgumentException("Ranking code cannot exceed 50 characters", nameof(rankingCode));

        RankingCode = rankingCode.Trim();
    }

    private void SetRankingName(string rankingName)
    {
        if (string.IsNullOrWhiteSpace(rankingName))
            throw new ArgumentException("Ranking name cannot be null or empty", nameof(rankingName));

        if (rankingName.Length > 200)
            throw new ArgumentException("Ranking name cannot exceed 200 characters", nameof(rankingName));

        RankingName = rankingName.Trim();
    }

    private void SetLevel(int level)
    {
        if (level < 1)
            throw new ArgumentException("Ranking level must be greater than 0", nameof(level));

        if (level > 100)
            throw new ArgumentException("Ranking level cannot exceed 100", nameof(level));

        Level = level;
    }
}
