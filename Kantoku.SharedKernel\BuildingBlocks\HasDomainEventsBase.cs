namespace Kantoku.SharedKernel.BuildingBlocks;

/// <summary>
/// Base class for entities that can raise domain events.
/// Provides the implementation for managing domain events.
/// </summary>
public abstract class HasDomainEventsBase : IHasDomainEvents
{
    private readonly List<DomainEvent> _domainEvents = [];

    /// <summary>
    /// Gets the collection of domain events raised by this entity
    /// </summary>
    public IReadOnlyCollection<DomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Adds a domain event to the entity
    /// </summary>
    /// <param name="domainEvent">The domain event to add</param>
    /// <exception cref="ArgumentNullException">Thrown when domainEvent is null</exception>
    public void AddDomainEvent(DomainEvent domainEvent)
    {
        ArgumentNullException.ThrowIfNull(domainEvent);
        _domainEvents.Add(domainEvent);
    }

    /// <summary>
    /// Removes a domain event from the entity
    /// </summary>
    /// <param name="domainEvent">The domain event to remove</param>
    /// <exception cref="ArgumentNullException">Thrown when domainEvent is null</exception>
    public void RemoveDomainEvent(DomainEvent domainEvent)
    {
        ArgumentNullException.ThrowIfNull(domainEvent);
        _domainEvents.Remove(domainEvent);
    }

    /// <summary>
    /// Clears all domain events from the entity
    /// </summary>
    public void ClearDomainEvents() => _domainEvents.Clear();
}
