# Kantoku.SharedKernel

The SharedKernel project contains common Domain-Driven Design (DDD) infrastructure and building blocks that are shared across the Kantoku application. This library provides the foundational components needed to implement clean architecture and DDD patterns.

## Overview

The SharedKernel follows DDD principles and provides:

- **Base classes** for domain objects (Entity, AggregateRoot, ValueObject)
- **Domain event infrastructure** for decoupled communication
- **Result patterns** for error handling without exceptions
- **Guard clauses** for input validation
- **Common value objects** (Email, Money, PhoneNumber, Address)
- **Pagination and sorting** primitives
- **Exception framework** for domain-specific errors
- **Time utilities** with clock abstraction for testability

## Structure

```
Kantoku.SharedKernel/
├── BuildingBlocks/          # Core DDD building blocks
├── Results/                 # Result pattern implementations
├── Guards/                  # Guard clause utilities
├── ValueObjects/            # Common value objects
├── Pagination/              # Pagination and sorting primitives
├── Exceptions/              # Domain exception framework
├── Time/                    # Time utilities and abstractions
└── Events/                  # Domain event infrastructure
```

## Key Components

### Building Blocks

#### Entity Base Classes
- `Entity` - Base class for entities with Guid identifiers
- `Entity<TId>` - Base class for entities with strongly-typed identifiers
- `AggregateRoot` - Base class for aggregate roots
- `ValueObject` - Base class for value objects
- `Enumeration` - Base class for type-safe enumerations

#### Domain Events
- `DomainEvent` - Base class for domain events
- `IHasDomainEvents` - Interface for entities that can raise domain events
- `HasDomainEventsBase` - Base implementation for domain event management

### Result Pattern

The Result pattern provides a way to handle operations that can succeed or fail without using exceptions:

```csharp
// Non-generic result for operations that don't return a value
Result result = Result.Success();
Result failedResult = Result.Failure("Operation failed");

// Generic result for operations that return a value
Result<User> userResult = Result<User>.Success(user);
Result<User> notFoundResult = Result<User>.Failure("User not found");
```

### Guard Clauses

Guard clauses help validate method arguments and maintain clean, readable code:

```csharp
public void CreateUser(string email, string name)
{
    var validEmail = Guard.NotNullOrWhiteSpace(email);
    var validName = Guard.NotNullOrWhiteSpace(name);
    var positiveAge = Guard.Positive(age);
    // ... rest of method
}
```

### Value Objects

Common value objects with built-in validation:

```csharp
// Email with validation
var email = Email.Create("<EMAIL>");

// Money with currency support
var price = Money.Create(100.50m, "USD");
var total = price + Money.Create(50.25m, "USD");

// Phone number with formatting
var phone = PhoneNumber.Create("+1234567890");

// Address with multiple formatting options
var address = Address.Create("123 Main St", "Tokyo", "Japan");
```

### Pagination

Type-safe pagination with sorting support:

```csharp
var request = new PagedRequest(pageNumber: 1, pageSize: 10)
    .OrderBy("Name")
    .OrderByDescending("CreatedDate");

var result = new PagedResult<User>(users, request.PageNumber, request.PageSize, totalCount);
```

### Time Utilities

Clock abstraction for testable time-dependent code:

```csharp
// Production code uses SystemClock
IClock clock = new SystemClock();
var now = clock.UtcNow;

// Test code can use FixedClock
IClock testClock = new FixedClock(new DateTime(2023, 1, 1));
var fixedTime = testClock.UtcNow; // Always returns 2023-01-01
```

### Domain Events

Infrastructure for publishing and handling domain events:

```csharp
// Define a domain event
public class UserCreatedEvent : DomainEvent
{
    public Guid UserId { get; }
    public string Email { get; }
    
    public UserCreatedEvent(Guid userId, string email)
    {
        UserId = userId;
        Email = email;
    }
}

// Handle domain events
public class UserCreatedEventHandler : IDomainEventHandler<UserCreatedEvent>
{
    public async Task Handle(UserCreatedEvent domainEvent, CancellationToken cancellationToken)
    {
        // Handle the event (e.g., send welcome email)
    }
}
```

### Exception Framework

Domain-specific exceptions with rich error information:

```csharp
// Business rule violation
throw new BusinessRuleViolationException("UserMustBeActive", "User must be active to perform this operation");

// Entity not found
throw new EntityNotFoundException(typeof(User), userId);

// Entity already exists
throw new EntityAlreadyExistsException(typeof(User), email);
```

## Usage Guidelines

### 1. Entity Design
- Inherit from `Entity` or `Entity<TId>` for entities
- Inherit from `AggregateRoot` for aggregate roots
- Use private setters and expose behavior through methods
- Raise domain events for important business events

### 2. Value Object Design
- Inherit from `ValueObject` for immutable value types
- Implement `GetEqualityComponents()` for equality comparison
- Include validation in constructors
- Provide static factory methods for creation

### 3. Domain Events
- Use domain events for cross-aggregate communication
- Keep events focused on business concepts
- Include relevant data in the event
- Handle events asynchronously when possible

### 4. Error Handling
- Use Result pattern for operations that can fail
- Use domain exceptions for business rule violations
- Provide meaningful error messages and codes
- Include relevant context in exceptions

### 5. Time Handling
- Use `IClock` abstraction instead of `DateTime.Now`
- Inject clock dependencies for testability
- Use UTC times for storage and business logic
- Convert to local time only for display

## Dependencies

- **MediatR.Contracts** - For domain event infrastructure
- **System.ComponentModel.Annotations** - For validation attributes

## Integration

To use the SharedKernel in your projects:

1. Add a project reference to `Kantoku.SharedKernel`
2. Register the clock implementation in your DI container:
   ```csharp
   services.AddSingleton<IClock, SystemClock>();
   ```
3. Configure MediatR for domain event handling
4. Inherit from the appropriate base classes for your domain objects

## Testing

The SharedKernel is designed with testability in mind:

- Use `FixedClock` for predictable time in tests
- Mock `IDomainEventDispatcher` for testing event publishing
- Use the Result pattern to test success/failure scenarios
- Value objects are immutable and easy to test

## Best Practices

1. **Keep the SharedKernel minimal** - Only include truly shared concepts
2. **Avoid business logic** - SharedKernel should contain infrastructure, not business rules
3. **Maintain backward compatibility** - Changes should be additive when possible
4. **Document breaking changes** - Clearly communicate any breaking changes
5. **Use semantic versioning** - Follow semantic versioning for releases
