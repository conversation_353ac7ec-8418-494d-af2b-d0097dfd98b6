using Kantoku.SharedKernel;

namespace Kantoku.Domain.InventoryManagement.Enums;

/// <summary>
/// Enumeration representing item units
/// </summary>
public class ItemUnit : Enumeration
{
    public static readonly ItemUnit Piece = new(1, nameof(Piece), "Piece", "pcs");
    public static readonly ItemUnit Kilogram = new(2, nameof(Kilogram), "Kilogram", "kg");
    public static readonly ItemUnit Gram = new(3, nameof(Gram), "Gram", "g");
    public static readonly ItemUnit Liter = new(4, nameof(Liter), "Liter", "L");
    public static readonly ItemUnit Milliliter = new(5, nameof(Milliliter), "Milliliter", "mL");
    public static readonly ItemUnit Meter = new(6, nameof(Meter), "Meter", "m");
    public static readonly ItemUnit Centimeter = new(7, nameof(Centimeter), "Centimeter", "cm");
    public static readonly ItemUnit SquareMeter = new(8, nameof(SquareMeter), "Square Meter", "m²");
    public static readonly ItemUnit CubicMeter = new(9, nameof(CubicMeter), "Cubic Meter", "m³");
    public static readonly ItemUnit Hour = new(10, nameof(Hour), "Hour", "hr");
    public static readonly ItemUnit Day = new(11, nameof(Day), "Day", "day");
    public static readonly ItemUnit Set = new(12, nameof(Set), "Set", "set");
    public static readonly ItemUnit Box = new(13, nameof(Box), "Box", "box");
    public static readonly ItemUnit Pack = new(14, nameof(Pack), "Pack", "pack");

    public string DisplayName { get; private set; }
    public string Symbol { get; private set; }

    private ItemUnit(int id, string name, string displayName, string symbol) : base(id, name)
    {
        DisplayName = displayName;
        Symbol = symbol;
    }

    public static IEnumerable<ItemUnit> GetAll()
    {
        return new[] { Piece, Kilogram, Gram, Liter, Milliliter, Meter, Centimeter, SquareMeter, CubicMeter, Hour, Day, Set, Box, Pack };
    }

    public static ItemUnit FromName(string name)
    {
        var unit = GetAll().FirstOrDefault(u => string.Equals(u.Name, name, StringComparison.OrdinalIgnoreCase));
        if (unit == null)
            throw new ArgumentException($"Unknown item unit: {name}");
        return unit;
    }

    public static ItemUnit FromId(int id)
    {
        var unit = GetAll().FirstOrDefault(u => u.Id == id);
        if (unit == null)
            throw new ArgumentException($"Unknown item unit ID: {id}");
        return unit;
    }

    public bool IsWeight => this == Kilogram || this == Gram;
    public bool IsVolume => this == Liter || this == Milliliter;
    public bool IsLength => this == Meter || this == Centimeter;
    public bool IsArea => this == SquareMeter;
    public bool IsVolumeCubic => this == CubicMeter;
    public bool IsTime => this == Hour || this == Day;
    public bool IsCountable => this == Piece || this == Set || this == Box || this == Pack;
}

/// <summary>
/// Enumeration representing item status
/// </summary>
public class ItemStatus : Enumeration
{
    public static readonly ItemStatus Active = new(1, nameof(Active), "Active");
    public static readonly ItemStatus Inactive = new(2, nameof(Inactive), "Inactive");
    public static readonly ItemStatus Discontinued = new(3, nameof(Discontinued), "Discontinued");
    public static readonly ItemStatus OutOfStock = new(4, nameof(OutOfStock), "Out of Stock");

    public string DisplayName { get; private set; }

    private ItemStatus(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<ItemStatus> GetAll()
    {
        return new[] { Active, Inactive, Discontinued, OutOfStock };
    }

    public static ItemStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown item status: {name}");
        return status;
    }

    public static ItemStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown item status ID: {id}");
        return status;
    }

    public bool IsAvailable => this == Active;
    public bool IsUnavailable => this == Inactive || this == Discontinued || this == OutOfStock;
}

/// <summary>
/// Enumeration representing stock movement types
/// </summary>
public class StockMovementType : Enumeration
{
    public static readonly StockMovementType In = new(1, nameof(In), "Stock In", 1);
    public static readonly StockMovementType Out = new(2, nameof(Out), "Stock Out", -1);
    public static readonly StockMovementType AdjustmentIn = new(3, nameof(AdjustmentIn), "Adjustment In", 1);
    public static readonly StockMovementType AdjustmentOut = new(4, nameof(AdjustmentOut), "Adjustment Out", -1);
    public static readonly StockMovementType Transfer = new(5, nameof(Transfer), "Transfer", 0);
    public static readonly StockMovementType Return = new(6, nameof(Return), "Return", 1);
    public static readonly StockMovementType Damage = new(7, nameof(Damage), "Damage", -1);
    public static readonly StockMovementType Loss = new(8, nameof(Loss), "Loss", -1);

    public string DisplayName { get; private set; }
    public int Direction { get; private set; } // 1 = increase, -1 = decrease, 0 = neutral

    private StockMovementType(int id, string name, string displayName, int direction) : base(id, name)
    {
        DisplayName = displayName;
        Direction = direction;
    }

    public static IEnumerable<StockMovementType> GetAll()
    {
        return new[] { In, Out, AdjustmentIn, AdjustmentOut, Transfer, Return, Damage, Loss };
    }

    public static StockMovementType FromName(string name)
    {
        var type = GetAll().FirstOrDefault(t => string.Equals(t.Name, name, StringComparison.OrdinalIgnoreCase));
        if (type == null)
            throw new ArgumentException($"Unknown stock movement type: {name}");
        return type;
    }

    public static StockMovementType FromId(int id)
    {
        var type = GetAll().FirstOrDefault(t => t.Id == id);
        if (type == null)
            throw new ArgumentException($"Unknown stock movement type ID: {id}");
        return type;
    }

    public bool IsIncoming => Direction > 0;
    public bool IsOutgoing => Direction < 0;
    public bool IsNeutral => Direction == 0;
}
