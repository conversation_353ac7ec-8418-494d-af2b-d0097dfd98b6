using Kantoku.Domain.Common.BuildingBlocks;

namespace Kantoku.Domain.EmployeeAggregate.EmployeeShifts;

public class EmployeeShiftBreakTime : ValueObject
{
    public DateTime? BreakInTime { get; set; }
    public DateTime? BreakOutTime { get; set; }

    public EmployeeShiftBreakTime(
        DateTime? breakInTime,
        DateTime? breakOutTime)
    {
        BreakInTime = breakInTime;
        BreakOutTime = breakOutTime;
    }

    protected override IEnumerable<object?> GetAtomicValues()
    {
        yield return BreakInTime;
        yield return BreakOutTime;
    }
}

