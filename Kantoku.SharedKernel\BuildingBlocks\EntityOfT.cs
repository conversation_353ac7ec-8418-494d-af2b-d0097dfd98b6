namespace Kantoku.SharedKernel.BuildingBlocks;

/// <summary>
/// Base class for entities with strongly-typed identifiers.
/// Entities are objects that have a distinct identity that runs through time and different representations.
/// </summary>
/// <typeparam name="TId">The type of the entity identifier</typeparam>
public abstract class Entity<TId> : HasDomainEventsBase where TId : IEquatable<TId>
{
    /// <summary>
    /// Gets the unique identifier for this entity
    /// </summary>
    public TId Id { get; protected set; }

    /// <summary>
    /// Initializes a new instance of the Entity class with the specified identifier
    /// </summary>
    /// <param name="id">The unique identifier for this entity</param>
    /// <exception cref="ArgumentException">Thrown when the ID is the default value</exception>
    protected Entity(TId id)
    {
        if (EqualityComparer<TId>.Default.Equals(id, default))
        {
            throw new ArgumentException("The ID cannot be the default value.", nameof(id));
        }
        Id = id;
    }

    /// <summary>
    /// Determines whether this entity is transient (not yet persisted)
    /// </summary>
    /// <returns>True if the entity is transient; otherwise, false</returns>
    public bool IsTransient() => EqualityComparer<TId>.Default.Equals(Id, default);

    /// <summary>
    /// Determines whether this entity is equal to the specified object
    /// </summary>
    /// <param name="obj">The object to compare</param>
    /// <returns>True if the entities are equal; otherwise, false</returns>
    public override bool Equals(object? obj)
    {
        if (obj == null || obj is not Entity<TId>)
            return false;

        if (ReferenceEquals(this, obj))
            return true;

        if (GetType() != obj.GetType())
            return false;

        var item = (Entity<TId>)obj;

        if (item.IsTransient() || IsTransient())
            return false;

        return EqualityComparer<TId>.Default.Equals(item.Id, Id);
    }

    /// <summary>
    /// Returns the hash code for this entity
    /// </summary>
    /// <returns>A hash code for this entity</returns>
    public override int GetHashCode()
    {
        if (IsTransient())
            return base.GetHashCode();

        return Id?.GetHashCode() ?? 0;
    }

    /// <summary>
    /// Determines whether two entities are equal
    /// </summary>
    /// <param name="left">The first entity</param>
    /// <param name="right">The second entity</param>
    /// <returns>True if the entities are equal; otherwise, false</returns>
    public static bool operator ==(Entity<TId>? left, Entity<TId>? right)
    {
        if (Equals(left, null))
            return Equals(right, null);

        return left.Equals(right);
    }

    /// <summary>
    /// Determines whether two entities are not equal
    /// </summary>
    /// <param name="left">The first entity</param>
    /// <param name="right">The second entity</param>
    /// <returns>True if the entities are not equal; otherwise, false</returns>
    public static bool operator !=(Entity<TId>? left, Entity<TId>? right)
    {
        return !(left == right);
    }
}
