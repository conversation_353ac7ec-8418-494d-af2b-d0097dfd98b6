using System.Threading;
using System.Threading.Tasks;

namespace Kantoku.Application.Interfaces
{
    /// <summary>
    /// Unit of Work pattern interface to ensure atomic transactions
    /// </summary>
    public interface IUnitOfWork
    {
        /// <summary>
        /// Saves all changes made in this context to the database.
        /// </summary>
        /// <param name="cancellationToken">A CancellationToken to observe while waiting for the task to complete</param>
        /// <returns>A task that represents the asynchronous save operation</returns>
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Begins a new transaction
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        Task BeginTransactionAsync();
        
        /// <summary>
        /// Commits the transaction
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        Task CommitTransactionAsync();
        
        /// <summary>
        /// Rolls back the transaction
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        Task RollbackTransactionAsync();
    }
} 