using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Project.Request;
using Kantoku.Api.Dtos.Project.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public partial interface IProjectService
{
    Task<ResultDto<ProjectSummariesResponseDto>> GetProjectSummaries(int pageNum, int pageSize, DateOnly dateFrom, DateOnly dateTo);
    Task<ResultDto<ProjectDailyReportResponseDto>> GetProjectDailyReport(Guid projectId, DateOnly reportDate);
    Task<ResultDto<ProjectDailyReportResponseDto>> CreateProjectDailyReport(Guid projectId, CreateDailyReportRequestDto request);
    Task<ResultDto<ProjectDailyReportResponseDto>> UpdateProjectDailyReport(Guid reportId, UpdateDailyReportRequestDto request);
    Task<ResultDto<bool>> DeleteProjectDailyReport(Guid reportId);
}

public partial class ProjectService : BaseService<ProjectService>, IProjectService
{
    public async Task<ResultDto<ProjectSummariesResponseDto>> GetProjectSummaries(int pageNum, int pageSize, DateOnly dateFrom, DateOnly dateTo)
    {
        var options = new ProjectQueryableOptions
        {
            IncludedConstructions = true,
            IncludedCustomer = true,
            IncludedContractor = true,
        };
        var (projects, total) = await projectRepository.GetProjectsByManagerId(GetCurrentEmployeeUid(), pageNum, pageSize, options);
        if (projects is null || !projects.Any() || total == 0)
        {
            logger.Information("No projects found for manager {EmployeeId}", GetCurrentEmployeeUid());
            return new ErrorResultDto<ProjectSummariesResponseDto>(ResponseCodeConstant.PROJECT_SUMMARY_NOT_EXIST);
        }
        var categories = await categoryRepository.GetRoot(new CategoryQueryableOptions());
        var items = new List<ProjectSummaryResponseDto>();
        foreach (var project in projects)
        {
            var constructionCosts = await constructionCostRepository.GetByFilter(new ConstructionCostFilter
            {
                ProjectId = project.ProjectUid,
                DateFrom = dateFrom.ToString("yyyy-MM-dd"),
                DateTo = dateTo.ToString("yyyy-MM-dd"),
            }, new ConstructionCostQueryableOptions
            {
                IncludedCategorizedCosts = true,
            });
            var employeeShifts = await employeeShiftRepository.GetByFilter(new EmployeeShiftFilter
            {
                ProjectId = project.ProjectUid,
                TimeFrom = dateFrom.ToDateTime(TimeOnly.MinValue).ToString("yyyy-MM-dd HH:mm:ss"),
                TimeTo = dateTo.ToDateTime(TimeOnly.MaxValue).ToString("yyyy-MM-dd HH:mm:ss"),
            }, new EmployeeShiftQueryableOptions
            {
                IncludedEmployee = true,
            });
            var (leaveRequests, _) = await requestRepository.GetByProject(project.ProjectUid, new RequestFilter
            {
                FromDate = dateFrom.ToDateTime(TimeOnly.MinValue).ToString("yyyy-MM-dd HH:mm:ss"),
                ToDate = dateTo.ToDateTime(TimeOnly.MaxValue).ToString("yyyy-MM-dd HH:mm:ss"),
                RequestTypeCode = RequestTypeConstants.LEAVE,
                StatusCode = StatusConstants.APPROVED,
            }, new RequestQueryableOptions());
            var schedules = await projectScheduleRepository.GetByProjectIdAndDateRange(
                project.ProjectUid, dateFrom, dateTo, new ScheduleQueryableOptions());

            var item = project.ToProjectSummaryResponseDto(
                project.Constructions,
                constructionCosts,
                employeeShifts,
                leaveRequests,
                schedules,
                constructionCosts.SelectMany(c => c.CategorizedCosts),
                categories,
                GetCurrentLanguageCode()
            );
            items.Add(item);
        }

        var result = new ProjectSummariesResponseDto
        {
            Items = items,
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = total
        };
        return new SuccessResultDto<ProjectSummariesResponseDto>(result);
    }

    public async Task<ResultDto<ProjectDailyReportResponseDto>> GetProjectDailyReport(Guid projectId, DateOnly reportDate)
    {
        var options = new ProjectDailyReportQueryableOptions
        {
            IncludedProject = true,
        };
        var filter = new ProjectDailyReportFilter
        {
            ReportDate = reportDate.ToString("yyyy-MM-dd"),
        };
        var report = await projectDailyReportRepository.GetByProjectIdAndDate(projectId, reportDate, options);
        if (report is null)
        {
            logger.Information("Project {ProjectId} not found", projectId);
            return new ErrorResultDto<ProjectDailyReportResponseDto>(ResponseCodeConstant.PROJECT_DAILY_REPORT_NOT_EXIST);
        }

        var employeeIds = report.EmployeeWorkload?.Select(e => e.EmployeeUid).Distinct() ?? [];
        var outSourceIds = report.OutSourceWorkload?.Select(o => o.OutSourceUid).Distinct() ?? [];

        var outSources = await outSourceRepository.GetByIds(outSourceIds, new OutSourceQueryableOptions());
        var employees = await employeeRepository.GetByIds(employeeIds, new EmployeeQueryableOptions
        {
            IncludedEmployeeRanks = true,
        });
        var result = report.ToProjectDailyReportResponseDto(
            employees,
            outSources
        );
        return new SuccessResultDto<ProjectDailyReportResponseDto>(result);
    }

    public async Task<ResultDto<ProjectDailyReportResponseDto>> CreateProjectDailyReport(Guid projectId, CreateDailyReportRequestDto request)
    {
        var isExist = await projectDailyReportRepository.IsExist(projectId, DateOnly.Parse(request.ReportDate!));
        if (isExist)
        {
            logger.Information("Project daily report {ProjectId} already exists", projectId);
            return new ErrorResultDto<ProjectDailyReportResponseDto>(ResponseCodeConstant.PROJECT_DAILY_REPORT_EXIST);
        }
        var newReport = request.ToEntity(projectId);
        if (newReport is null)
        {
            logger.Error("Error creating project daily report");
            return new ErrorResultDto<ProjectDailyReportResponseDto>(ResponseCodeConstant.PROJECT_DAILY_REPORT_CREATE_FAILED);
        }
        var createdReport = await projectDailyReportRepository.Create(newReport, new ProjectDailyReportQueryableOptions
        {
            IncludedProject = true,
        });
        if (createdReport is null)
        {
            logger.Error("Error creating project daily report");
            return new ErrorResultDto<ProjectDailyReportResponseDto>(ResponseCodeConstant.PROJECT_DAILY_REPORT_CREATE_FAILED);
        }
        var employeeIds = createdReport.EmployeeWorkload?.Select(e => e.EmployeeUid).Distinct() ?? [];
        var outSourceIds = createdReport.OutSourceWorkload?.Select(o => o.OutSourceUid).Distinct() ?? [];

        var outSources = await outSourceRepository.GetByIds(outSourceIds, new OutSourceQueryableOptions());
        var employees = await employeeRepository.GetByIds(employeeIds, new EmployeeQueryableOptions());
        var result = createdReport.ToProjectDailyReportResponseDto(
            employees,
            outSources
        );
        return new SuccessResultDto<ProjectDailyReportResponseDto>(result);
    }

    public async Task<ResultDto<ProjectDailyReportResponseDto>> UpdateProjectDailyReport(Guid reportId, UpdateDailyReportRequestDto request)
    {
        var report = await projectDailyReportRepository.GetById(reportId, new ProjectDailyReportQueryableOptions());
        if (report is null)
        {
            logger.Information("Project daily report {ReportId} not found", reportId);
            return new ErrorResultDto<ProjectDailyReportResponseDto>(ResponseCodeConstant.PROJECT_DAILY_REPORT_NOT_EXIST);
        }

        report.UpdateFromDto(request);

        var updatedReport = await projectDailyReportRepository.Update(report, new ProjectDailyReportQueryableOptions
        {
            IncludedProject = true,
        });
        if (updatedReport is null)
        {
            logger.Error("Error updating project daily report");
            return new ErrorResultDto<ProjectDailyReportResponseDto>(ResponseCodeConstant.PROJECT_DAILY_REPORT_UPDATE_FAILED);
        }
        var employeeIds = updatedReport.EmployeeWorkload?.Select(e => e.EmployeeUid).Distinct() ?? [];
        var outSourceIds = updatedReport.OutSourceWorkload?.Select(o => o.OutSourceUid).Distinct() ?? [];

        var outSources = await outSourceRepository.GetByIds(outSourceIds, new OutSourceQueryableOptions());
        var employees = await employeeRepository.GetByIds(employeeIds, new EmployeeQueryableOptions());

        var result = updatedReport.ToProjectDailyReportResponseDto(
            employees,
            outSources
        );
        return new SuccessResultDto<ProjectDailyReportResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteProjectDailyReport(Guid reportId)
    {
        var report = await projectDailyReportRepository.GetById(reportId, new ProjectDailyReportQueryableOptions());
        if (report is null)
        {
            logger.Information("Project daily report {ReportId} not found", reportId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.PROJECT_DAILY_REPORT_NOT_EXIST, false);
        }
        report.IsDeleted = true;
        var deletedReport = await projectDailyReportRepository.Update(report, new ProjectDailyReportQueryableOptions());
        if (deletedReport is null)
        {
            logger.Error("Error deleting project daily report");
            return new ErrorResultDto<bool>(ResponseCodeConstant.PROJECT_DAILY_REPORT_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }
}

