namespace Kantoku.Domain.Models;

public class DeviceToken
{
    public Guid DeviceTokenUid { get; set; }
    public Guid EmployeeUid { get; set; }


    public string? Platform { get; set; }


    public string? OsVersion { get; set; }


    public string? DeviceId { get; set; }


    public DateTime? LastActive { get; set; }


    public string? AppVersion { get; set; }


    public string? FirebaseToken { get; set; }

    public virtual Employee Employee { get; set; } = null!;
}

public static class Platform
{
    public const string MACOS = nameof(MACOS);
    public const string WINDOWS = nameof(WINDOWS);
    public const string WEB = nameof(WEB);
    public const string IOS = nameof(IOS);
    public const string ANDROID = nameof(ANDROID);
}
