using Kantoku.SharedKernel;

namespace Kantoku.Domain.EmployeeManagement.Enums;

/// <summary>
/// Enumeration representing employee working status
/// </summary>
public class WorkingStatus : Enumeration
{
    public static readonly WorkingStatus Invited = new(1, nameof(Invited), "Invited");
    public static readonly WorkingStatus Active = new(2, nameof(Active), "Active");
    public static readonly WorkingStatus OnLeave = new(3, nameof(OnLeave), "On Leave");
    public static readonly WorkingStatus Suspended = new(4, nameof(Suspended), "Suspended");
    public static readonly WorkingStatus Terminated = new(5, nameof(Terminated), "Terminated");
    public static readonly WorkingStatus Retired = new(6, nameof(Retired), "Retired");

    public string DisplayName { get; private set; }

    private WorkingStatus(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<WorkingStatus> GetAll()
    {
        return new[] { Invited, Active, OnLeave, Suspended, Terminated, Retired };
    }

    public static WorkingStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown working status: {name}");
        return status;
    }

    public static WorkingStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown working status ID: {id}");
        return status;
    }

    public bool IsActive => this == Active;
    public bool IsInactive => this == Suspended || this == Terminated || this == Retired;
    public bool CanWork => this == Active || this == OnLeave;
    public bool RequiresApproval => this == Invited;
}
