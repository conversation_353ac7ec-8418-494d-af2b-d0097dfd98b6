﻿namespace Kantoku.Domain.Models;

public class Position : AuditableEntity
{
    public Guid PositionUid { get; set; }


    public string PositionCode { get; set; } = null!;


    public string PositionName { get; set; } = null!;


    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }

    public virtual ICollection<Employee> Employees { get; set; } = [];

    public virtual Org Org { get; set; } = null!;
}
