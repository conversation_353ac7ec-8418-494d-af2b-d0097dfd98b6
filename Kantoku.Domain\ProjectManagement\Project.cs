using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.ProjectManagement.ValueObjects;
using Kantoku.Domain.ProjectManagement.Events;
using Kantoku.Domain.ProjectManagement.Enums;

namespace Kantoku.Domain.ProjectManagement;

/// <summary>
/// Project aggregate root representing a project in the system
/// </summary>
public class Project : FullAuditedEntity<Guid>
{
    private readonly List<ProjectManager> _managers = new();
    private readonly List<ProjectSchedule> _schedules = new();
    private readonly List<Construction> _constructions = new();
    private readonly List<ProjectDailyReport> _dailyReports = new();

    public Guid OrgId { get; private set; }
    public string ProjectCode { get; private set; } = null!;
    public string ProjectName { get; private set; } = null!;
    public string? Description { get; private set; }
    public string? Address { get; private set; }

    public Guid? ProjectTypeId { get; private set; }
    public Guid? CustomerId { get; private set; }
    public Guid? ContractorId { get; private set; }

    public ProjectStatus Status { get; private set; } = ProjectStatus.Planning;
    public ProjectDates Dates { get; private set; } = null!;
    public ProjectBudget? Budget { get; private set; }

    public int MonthlyReportDate { get; private set; } = 25;
    public bool IsOffice { get; private set; } = false;
    public bool IsDefault { get; private set; } = false;

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<ProjectManager> Managers => _managers.AsReadOnly();
    public IReadOnlyCollection<ProjectSchedule> Schedules => _schedules.AsReadOnly();
    public IReadOnlyCollection<Construction> Constructions => _constructions.AsReadOnly();
    public IReadOnlyCollection<ProjectDailyReport> DailyReports => _dailyReports.AsReadOnly();

    // Private constructor for EF Core
    private Project() : base() { }

    /// <summary>
    /// Creates a new project
    /// </summary>
    public Project(
        Guid id,
        Guid orgId,
        string projectCode,
        string projectName,
        ProjectDates dates,
        string? description = null,
        string? address = null) : base(id)
    {
        OrgId = orgId;
        SetProjectCode(projectCode);
        SetProjectName(projectName);
        Dates = dates ?? throw new ArgumentNullException(nameof(dates));
        Description = description;
        Address = address;

        AddDomainEvent(new ProjectCreatedEvent(this));
    }

    /// <summary>
    /// Updates basic project information
    /// </summary>
    public void UpdateBasicInfo(
        string projectName,
        string? description = null,
        string? address = null)
    {
        SetProjectName(projectName);
        Description = description;
        Address = address;

        AddDomainEvent(new ProjectUpdatedEvent(this));
    }

    /// <summary>
    /// Updates project dates
    /// </summary>
    public void UpdateDates(ProjectDates dates)
    {
        Dates = dates ?? throw new ArgumentNullException(nameof(dates));
        AddDomainEvent(new ProjectDatesUpdatedEvent(this));
    }

    /// <summary>
    /// Updates project budget
    /// </summary>
    public void UpdateBudget(ProjectBudget budget)
    {
        Budget = budget;
        AddDomainEvent(new ProjectBudgetUpdatedEvent(this));
    }

    /// <summary>
    /// Updates project status
    /// </summary>
    public void UpdateStatus(ProjectStatus status)
    {
        var oldStatus = Status;
        Status = status;

        if (oldStatus != status)
        {
            AddDomainEvent(new ProjectStatusChangedEvent(this, oldStatus, status));
        }
    }

    /// <summary>
    /// Assigns project type, customer, and contractor
    /// </summary>
    public void AssignExternalEntities(
        Guid? projectTypeId = null,
        Guid? customerId = null,
        Guid? contractorId = null)
    {
        ProjectTypeId = projectTypeId;
        CustomerId = customerId;
        ContractorId = contractorId;

        AddDomainEvent(new ProjectExternalEntitiesAssignedEvent(this));
    }

    /// <summary>
    /// Updates project settings
    /// </summary>
    public void UpdateSettings(
        int? monthlyReportDate = null,
        bool? isOffice = null,
        bool? isDefault = null)
    {
        if (monthlyReportDate.HasValue)
            SetMonthlyReportDate(monthlyReportDate.Value);

        IsOffice = isOffice ?? IsOffice;
        IsDefault = isDefault ?? IsDefault;
    }

    /// <summary>
    /// Adds a project manager
    /// </summary>
    public void AddManager(ProjectManager manager)
    {
        if (manager == null)
            throw new ArgumentNullException(nameof(manager));

        if (_managers.Any(m => m.EmployeeId == manager.EmployeeId))
            throw new InvalidOperationException("Employee is already a manager of this project");

        _managers.Add(manager);
        AddDomainEvent(new ProjectManagerAddedEvent(this, manager));
    }

    /// <summary>
    /// Removes a project manager
    /// </summary>
    public void RemoveManager(Guid employeeId)
    {
        var manager = _managers.FirstOrDefault(m => m.EmployeeId == employeeId);
        if (manager != null)
        {
            _managers.Remove(manager);
            AddDomainEvent(new ProjectManagerRemovedEvent(this, manager));
        }
    }

    /// <summary>
    /// Adds a project schedule
    /// </summary>
    public void AddSchedule(ProjectSchedule schedule)
    {
        if (schedule == null)
            throw new ArgumentNullException(nameof(schedule));

        // Check for overlapping schedules
        if (_schedules.Any(s => s.OverlapsWith(schedule)))
            throw new InvalidOperationException("Schedule overlaps with existing schedule");

        _schedules.Add(schedule);
        AddDomainEvent(new ProjectScheduleAddedEvent(this, schedule));
    }

    /// <summary>
    /// Adds a construction to the project
    /// </summary>
    public void AddConstruction(Construction construction)
    {
        if (construction == null)
            throw new ArgumentNullException(nameof(construction));

        _constructions.Add(construction);
        AddDomainEvent(new ConstructionAddedEvent(this, construction));
    }

    /// <summary>
    /// Adds a daily report to the project
    /// </summary>
    public void AddDailyReport(ProjectDailyReport dailyReport)
    {
        if (dailyReport == null)
            throw new ArgumentNullException(nameof(dailyReport));

        // Check if report for this date already exists
        if (_dailyReports.Any(r => r.ReportDate == dailyReport.ReportDate))
            throw new InvalidOperationException($"Daily report for {dailyReport.ReportDate} already exists");

        _dailyReports.Add(dailyReport);
        AddDomainEvent(new ProjectDailyReportAddedEvent(this, dailyReport));
    }

    /// <summary>
    /// Starts the project
    /// </summary>
    public void Start()
    {
        if (Status != ProjectStatus.Planning)
            throw new InvalidOperationException("Project can only be started from Planning status");

        Status = ProjectStatus.InProgress;
        
        // Update actual start date if not set
        if (!Dates.ActualStartDate.HasValue)
        {
            var updatedDates = new ProjectDates(
                Dates.ExpectedStartDate,
                Dates.ExpectedEndDate,
                DateOnly.FromDateTime(DateTime.Today),
                Dates.ActualEndDate);
            Dates = updatedDates;
        }

        AddDomainEvent(new ProjectStartedEvent(this));
    }

    /// <summary>
    /// Completes the project
    /// </summary>
    public void Complete()
    {
        if (Status != ProjectStatus.InProgress)
            throw new InvalidOperationException("Project can only be completed from InProgress status");

        Status = ProjectStatus.Completed;
        
        // Update actual end date if not set
        if (!Dates.ActualEndDate.HasValue)
        {
            var updatedDates = new ProjectDates(
                Dates.ExpectedStartDate,
                Dates.ExpectedEndDate,
                Dates.ActualStartDate,
                DateOnly.FromDateTime(DateTime.Today));
            Dates = updatedDates;
        }

        AddDomainEvent(new ProjectCompletedEvent(this));
    }

    /// <summary>
    /// Suspends the project
    /// </summary>
    public void Suspend()
    {
        if (Status != ProjectStatus.InProgress)
            throw new InvalidOperationException("Project can only be suspended from InProgress status");

        Status = ProjectStatus.Suspended;
        AddDomainEvent(new ProjectSuspendedEvent(this));
    }

    /// <summary>
    /// Cancels the project
    /// </summary>
    public void Cancel()
    {
        if (Status == ProjectStatus.Completed || Status == ProjectStatus.Cancelled)
            throw new InvalidOperationException("Cannot cancel a completed or already cancelled project");

        Status = ProjectStatus.Cancelled;
        AddDomainEvent(new ProjectCancelledEvent(this));
    }

    // Private helper methods
    private void SetProjectCode(string projectCode)
    {
        if (string.IsNullOrWhiteSpace(projectCode))
            throw new ArgumentException("Project code cannot be null or empty", nameof(projectCode));

        if (projectCode.Length > 50)
            throw new ArgumentException("Project code cannot exceed 50 characters", nameof(projectCode));

        ProjectCode = projectCode.Trim();
    }

    private void SetProjectName(string projectName)
    {
        if (string.IsNullOrWhiteSpace(projectName))
            throw new ArgumentException("Project name cannot be null or empty", nameof(projectName));

        if (projectName.Length > 200)
            throw new ArgumentException("Project name cannot exceed 200 characters", nameof(projectName));

        ProjectName = projectName.Trim();
    }

    private void SetMonthlyReportDate(int monthlyReportDate)
    {
        if (monthlyReportDate < 1 || monthlyReportDate > 31)
            throw new ArgumentException("Monthly report date must be between 1 and 31", nameof(monthlyReportDate));

        MonthlyReportDate = monthlyReportDate;
    }
}
