﻿namespace Kantoku.Domain.Models;

public class ProjectSchedule : AuditableEntity
{
    public Guid ProjectScheduleUid { get; set; }
    public Guid ProjectUid { get; set; }


    public DateOnly WorkingDate { get; set; }


    public float PlannedWorkload { get; set; }


    public float PresignedWorkload { get; set; }


    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;

    public virtual Project Project { get; set; } = null!;

    public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = [];

    public virtual ICollection<OutSourceShift> OutSourceShifts { get; set; } = [];
}
