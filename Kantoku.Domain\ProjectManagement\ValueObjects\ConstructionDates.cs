using Kantoku.SharedKernel;

namespace Kantoku.Domain.ProjectManagement.ValueObjects;

/// <summary>
/// Value object representing construction dates
/// </summary>
public class ConstructionDates : ValueObject
{
    public DateOnly? PlannedStartDate { get; private set; }
    public DateOnly? PlannedEndDate { get; private set; }
    public DateOnly? ActualStartDate { get; private set; }
    public DateOnly? ActualEndDate { get; private set; }

    private ConstructionDates() { } // For EF Core

    public ConstructionDates(
        DateOnly? plannedStartDate,
        DateOnly? plannedEndDate,
        DateOnly? actualStartDate = null,
        DateOnly? actualEndDate = null)
    {
        ValidateDates(plannedStartDate, plannedEndDate, actualStartDate, actualEndDate);
        
        PlannedStartDate = plannedStartDate;
        PlannedEndDate = plannedEndDate;
        ActualStartDate = actualStartDate;
        ActualEndDate = actualEndDate;
    }

    /// <summary>
    /// Creates construction dates with only planned dates
    /// </summary>
    public static ConstructionDates CreatePlanned(DateOnly? plannedStartDate, DateOnly? plannedEndDate)
    {
        return new ConstructionDates(plannedStartDate, plannedEndDate);
    }

    /// <summary>
    /// Updates the actual start date
    /// </summary>
    public ConstructionDates WithActualStartDate(DateOnly actualStartDate)
    {
        return new ConstructionDates(PlannedStartDate, PlannedEndDate, actualStartDate, ActualEndDate);
    }

    /// <summary>
    /// Updates the actual end date
    /// </summary>
    public ConstructionDates WithActualEndDate(DateOnly actualEndDate)
    {
        return new ConstructionDates(PlannedStartDate, PlannedEndDate, ActualStartDate, actualEndDate);
    }

    /// <summary>
    /// Gets the planned duration in days
    /// </summary>
    public int? PlannedDurationDays
    {
        get
        {
            if (PlannedStartDate.HasValue && PlannedEndDate.HasValue)
                return PlannedEndDate.Value.DayNumber - PlannedStartDate.Value.DayNumber + 1;
            return null;
        }
    }

    /// <summary>
    /// Gets the actual duration in days
    /// </summary>
    public int? ActualDurationDays
    {
        get
        {
            if (ActualStartDate.HasValue && ActualEndDate.HasValue)
                return ActualEndDate.Value.DayNumber - ActualStartDate.Value.DayNumber + 1;
            return null;
        }
    }

    /// <summary>
    /// Checks if the construction is behind schedule
    /// </summary>
    public bool IsBehindSchedule
    {
        get
        {
            if (!PlannedEndDate.HasValue)
                return false;

            var today = DateOnly.FromDateTime(DateTime.Today);
            
            // If construction is completed, check if it finished late
            if (ActualEndDate.HasValue)
                return ActualEndDate.Value > PlannedEndDate.Value;
            
            // If construction is ongoing, check if we're past the planned end date
            return today > PlannedEndDate.Value;
        }
    }

    /// <summary>
    /// Checks if the construction started late
    /// </summary>
    public bool StartedLate
    {
        get
        {
            if (!PlannedStartDate.HasValue || !ActualStartDate.HasValue)
                return false;
            
            return ActualStartDate.Value > PlannedStartDate.Value;
        }
    }

    /// <summary>
    /// Gets the delay in days (positive if behind schedule, negative if ahead)
    /// </summary>
    public int? DelayDays
    {
        get
        {
            if (!PlannedEndDate.HasValue)
                return null;

            var endDate = ActualEndDate ?? DateOnly.FromDateTime(DateTime.Today);
            return endDate.DayNumber - PlannedEndDate.Value.DayNumber;
        }
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return PlannedStartDate;
        yield return PlannedEndDate;
        yield return ActualStartDate;
        yield return ActualEndDate;
    }

    private static void ValidateDates(
        DateOnly? plannedStartDate,
        DateOnly? plannedEndDate,
        DateOnly? actualStartDate,
        DateOnly? actualEndDate)
    {
        // Validate planned dates
        if (plannedStartDate.HasValue && plannedEndDate.HasValue)
        {
            if (plannedStartDate.Value > plannedEndDate.Value)
                throw new ArgumentException("Planned start date cannot be after planned end date");
        }

        // Validate actual dates
        if (actualStartDate.HasValue && actualEndDate.HasValue)
        {
            if (actualStartDate.Value > actualEndDate.Value)
                throw new ArgumentException("Actual start date cannot be after actual end date");
        }
    }
}
