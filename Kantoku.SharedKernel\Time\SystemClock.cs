namespace Kantoku.SharedKernel.Time;

/// <summary>
/// System implementation of IClock that returns the actual current time
/// </summary>
public class SystemClock : IClock
{
    /// <summary>
    /// Gets the current UTC date and time
    /// </summary>
    public DateTime UtcNow => DateTime.UtcNow;

    /// <summary>
    /// Gets the current local date and time
    /// </summary>
    public DateTime Now => DateTime.Now;

    /// <summary>
    /// Gets the current date (without time component)
    /// </summary>
    public DateTime Today => DateTime.Today;

    /// <summary>
    /// Gets the current UTC date and time as DateTimeOffset
    /// </summary>
    public DateTimeOffset UtcNowOffset => DateTimeOffset.UtcNow;

    /// <summary>
    /// Gets the current local date and time as DateTimeOffset
    /// </summary>
    public DateTimeOffset NowOffset => DateTimeOffset.Now;
}
