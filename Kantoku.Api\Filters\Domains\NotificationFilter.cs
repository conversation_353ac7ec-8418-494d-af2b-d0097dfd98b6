using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class NotificationFilter : BaseFilter
{
    [FromQuery(Name = "titleKeyword")]
    public string? TitleKeyword { get; set; }

    [FromQuery(Name = "bodyKeyword")]
    public string? BodyKeyword { get; set; }
}

public class EmployeeNotificationFilter : NotificationFilter
{
    [FromRoute(Name = "isRead")]
    public bool? IsRead { get; set; }
}

public class OrgNotificationFilter : NotificationFilter
{
    [FromQuery(Name = "type")]
    public string? Type { get; set; }

    [FromQuery(Name = "targetType")]
    public string? TargetType { get; set; }

    [FromQuery(Name = "status")]
    public string? Status { get; set; }
}



