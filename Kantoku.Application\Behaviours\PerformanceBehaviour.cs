using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace Kantoku.Application.Behaviours
{
    /// <summary>
    /// Behavior that monitors and logs slow-running requests
    /// </summary>
    /// <typeparam name="TRequest">The request type</typeparam>
    /// <typeparam name="TResponse">The response type</typeparam>
    public class PerformanceBehaviour<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
        where TRequest : notnull
    {
        private readonly ILogger<PerformanceBehaviour<TRequest, TResponse>> _logger;
        private readonly Stopwatch _timer;

        // Threshold in milliseconds for when to log warning about slow requests
        private const int ThresholdInMs = 500;

        public PerformanceBehaviour(ILogger<PerformanceBehaviour<TRequest, TResponse>> logger)
        {
            _logger = logger;
            _timer = new Stopwatch();
        }

        public async Task<TResponse> Handle(
            TRequest request, 
            RequestHandlerDelegate<TResponse> next, 
            CancellationToken cancellationToken)
        {
            _timer.Start();

            var response = await next();

            _timer.Stop();

            var elapsedMilliseconds = _timer.ElapsedMilliseconds;

            if (elapsedMilliseconds > ThresholdInMs)
            {
                var requestName = typeof(TRequest).Name;

                _logger.LogWarning(
                    "Long running request: {RequestName} ({ElapsedMilliseconds} milliseconds)",
                    requestName,
                    elapsedMilliseconds);
            }

            return response;
        }
    }
} 