using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.MonthlyAttReport;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class MonthlyReportMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a MonthlyReport entity to a MonthlyReportResponseDto   
    /// </summary>
    /// <param name="report">The MonthlyReport entity to map</param>
    /// <param name="currentLeave">The current leave of the employee</param>
    /// <returns>A MonthlyReportResponseDto</returns>
    public static MonthlyReportResponseDto ToMonthlyReportResponseDto(this MonthlyReport report, EmployeeLeave? currentLeave = null)
    {
        var totalAvailableLeave = 0.0f;
        if (currentLeave is not null)
        {
            totalAvailableLeave = currentLeave.BaseLeave + (currentLeave.LastRemainLeave ?? 0.0f);
        }

        var usedLeave = (float)(report.SelfLeaveUsed + report.OrgLeaveUsed);
        var remainLeave = (float)(totalAvailableLeave - usedLeave);

        return new MonthlyReportResponseDto
        {
            EmployeeId = report.EmployeeUid.ToString(),
            EmployeeName = report.Employee?.EmployeeName,
            EmployeeCode = report.Employee?.EmployeeCode,
            WorkTime = (float?)Math.Round(report.TotalWorkHours, 2),
            Overtime = (float?)Math.Round(report.TotalOvertime, 2),
            WorkDays = (float?)Math.Round(report.TotalWorkDays, 2),
            Offdays = (float?)Math.Round(report.TotalOffDays, 2),
            UsedLeaves = (float?)Math.Round(usedLeave, 2),
            RemainLeaves = (float?)Math.Round(remainLeave, 2),
            Comment = report.Description,
            IsRequested = report.IsRequested,
            IsApproved = report.IsApproved,
            ApproverName = report.Approver?.EmployeeName,
            ApprovedTime = DateTimeHelper.ParseToLocalTime(report?.ApprovedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
        };
    }

    /// <summary>
    /// Maps a MonthlyReport entity to a MonthlyReportResponseDto   
    /// </summary>
    /// <param name="reports">The MonthlyReport entities to map</param>
    /// <param name="currentLeaves">The current leaves of the employees</param>
    /// <returns>A MonthlyReportResponseDto</returns>
    public static IEnumerable<MonthlyReportResponseDto> ToMonthlyReportsResponseDto(
        this IEnumerable<MonthlyReport> reports, IEnumerable<EmployeeLeave> currentLeaves)
    {
        if (reports is null || !reports.Any())
        {
            yield break;
        }
        foreach (var report in reports)
        {
            var currentLeave = currentLeaves
                .Where(l => l.EmployeeUid == report.EmployeeUid)
                .FirstOrDefault();

            if (currentLeave is null)
            {
                yield return new MonthlyReportResponseDto
                {
                    EmployeeId = report.EmployeeUid.ToString(),
                    EmployeeName = report.Employee?.EmployeeName,
                    EmployeeCode = report.Employee?.EmployeeCode,
                    WorkTime = (float?)Math.Round(report.TotalWorkHours, 2),
                    Overtime = (float?)Math.Round(report.TotalOvertime, 2),
                    WorkDays = (float?)Math.Round(report.TotalWorkDays, 2),
                    Offdays = (float?)Math.Round(report.TotalOffDays, 2),
                    UsedLeaves = (float?)Math.Round(0.0f, 2),
                    RemainLeaves = (float?)Math.Round(0.0f, 2),
                    Comment = report.Description,
                    IsRequested = report.IsRequested,
                    IsApproved = report.IsApproved,
                    ApproverName = report.Approver?.EmployeeName,
                    ApprovedTime = DateTimeHelper.ParseToLocalTime(report?.ApprovedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
                };
            }

            var totalAvailableLeave = 0.0f;
            totalAvailableLeave = currentLeave!.BaseLeave + (currentLeave.LastRemainLeave ?? 0.0f);

            var usedLeave = (float)(report!.SelfLeaveUsed + report!.OrgLeaveUsed);
            var remainLeave = (float)(totalAvailableLeave - usedLeave);

            yield return new MonthlyReportResponseDto
            {
                EmployeeId = report.EmployeeUid.ToString(),
                EmployeeName = report.Employee?.EmployeeName,
                EmployeeCode = report.Employee?.EmployeeCode,
                WorkTime = (float?)Math.Round(report.TotalWorkHours, 2),
                Overtime = (float?)Math.Round(report.TotalOvertime, 2),
                WorkDays = (float?)Math.Round(report.TotalWorkDays, 2),
                Offdays = (float?)Math.Round(report.TotalOffDays, 2),
                UsedLeaves = (float?)Math.Round(usedLeave, 2),
                RemainLeaves = (float?)Math.Round(remainLeave, 2),
                Comment = report.Description,
                IsRequested = report.IsRequested,
                IsApproved = report.IsApproved,
                ApproverName = report.Approver?.EmployeeName,
                ApprovedTime = DateTimeHelper.ParseToLocalTime(report?.ApprovedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
            };
        }
    }


    #endregion

    #region DTO to Entity mappings



    #endregion
}