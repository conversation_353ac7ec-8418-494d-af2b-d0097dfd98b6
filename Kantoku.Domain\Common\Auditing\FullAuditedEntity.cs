using Kantoku.Domain.Common.BuildingBlocks;

namespace Kantoku.Domain.Common.Auditing
{
    public abstract class FullAuditedEntity<TId> : Entity<TId>, IFullAudited
        where TId : class, IEquatable<TId>
    {
        // Parameterless constructor for EF Core
        protected FullAuditedEntity() : base() { }

        protected FullAuditedEntity(TId id) : base(id) { }

        // ICreationAudited
        public DateTime CreatedAtUtc { get; set; }
        public Guid? CreatedBy { get; set; }

        // IModificationAudited
        public DateTime? LastModifiedAtUtc { get; set; }
        public Guid? LastModifiedBy { get; set; }

        // ISoftDeleteAudited
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAtUtc { get; set; }
        public Guid? DeletedBy { get; set; }
    }
}