﻿namespace Kantoku.Domain.Models;

public class UserInfo : AuditableEntity
{
    public Guid UserInfoUid { get; set; }
    public Guid AccountUid { get; set; }


    public string Name { get; set; } = null!;


    public string? Address { get; set; }


    public string? Phone { get; set; }


    public bool? Gender { get; set; }


    public DateOnly? Birthday { get; set; }

    public string? AvatarUrl { get; set; }

    public virtual Account Account { get; set; } = null!;
}
