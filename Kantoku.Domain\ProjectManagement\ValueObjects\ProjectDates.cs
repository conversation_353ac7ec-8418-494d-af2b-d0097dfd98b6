using Kantoku.SharedKernel;

namespace Kantoku.Domain.ProjectManagement.ValueObjects;

/// <summary>
/// Value object representing project dates
/// </summary>
public class ProjectDates : ValueObject
{
    public DateOnly? ExpectedStartDate { get; private set; }
    public DateOnly? ExpectedEndDate { get; private set; }
    public DateOnly? ActualStartDate { get; private set; }
    public DateOnly? ActualEndDate { get; private set; }

    private ProjectDates() { } // For EF Core

    public ProjectDates(
        DateOnly? expectedStartDate,
        DateOnly? expectedEndDate,
        DateOnly? actualStartDate = null,
        DateOnly? actualEndDate = null)
    {
        ValidateDates(expectedStartDate, expectedEndDate, actualStartDate, actualEndDate);
        
        ExpectedStartDate = expectedStartDate;
        ExpectedEndDate = expectedEndDate;
        ActualStartDate = actualStartDate;
        ActualEndDate = actualEndDate;
    }

    /// <summary>
    /// Creates project dates with only expected dates
    /// </summary>
    public static ProjectDates CreateExpected(DateOnly? expectedStartDate, DateOnly? expectedEndDate)
    {
        return new ProjectDates(expectedStartDate, expectedEndDate);
    }

    /// <summary>
    /// Updates the actual start date
    /// </summary>
    public ProjectDates WithActualStartDate(DateOnly actualStartDate)
    {
        return new ProjectDates(ExpectedStartDate, ExpectedEndDate, actualStartDate, ActualEndDate);
    }

    /// <summary>
    /// Updates the actual end date
    /// </summary>
    public ProjectDates WithActualEndDate(DateOnly actualEndDate)
    {
        return new ProjectDates(ExpectedStartDate, ExpectedEndDate, ActualStartDate, actualEndDate);
    }

    /// <summary>
    /// Gets the expected duration in days
    /// </summary>
    public int? ExpectedDurationDays
    {
        get
        {
            if (ExpectedStartDate.HasValue && ExpectedEndDate.HasValue)
                return ExpectedEndDate.Value.DayNumber - ExpectedStartDate.Value.DayNumber + 1;
            return null;
        }
    }

    /// <summary>
    /// Gets the actual duration in days
    /// </summary>
    public int? ActualDurationDays
    {
        get
        {
            if (ActualStartDate.HasValue && ActualEndDate.HasValue)
                return ActualEndDate.Value.DayNumber - ActualStartDate.Value.DayNumber + 1;
            return null;
        }
    }

    /// <summary>
    /// Checks if the project is behind schedule
    /// </summary>
    public bool IsBehindSchedule
    {
        get
        {
            if (!ExpectedEndDate.HasValue)
                return false;

            var today = DateOnly.FromDateTime(DateTime.Today);
            
            // If project is completed, check if it finished late
            if (ActualEndDate.HasValue)
                return ActualEndDate.Value > ExpectedEndDate.Value;
            
            // If project is ongoing, check if we're past the expected end date
            return today > ExpectedEndDate.Value;
        }
    }

    /// <summary>
    /// Checks if the project started late
    /// </summary>
    public bool StartedLate
    {
        get
        {
            if (!ExpectedStartDate.HasValue || !ActualStartDate.HasValue)
                return false;
            
            return ActualStartDate.Value > ExpectedStartDate.Value;
        }
    }

    /// <summary>
    /// Gets the delay in days (positive if behind schedule, negative if ahead)
    /// </summary>
    public int? DelayDays
    {
        get
        {
            if (!ExpectedEndDate.HasValue)
                return null;

            var endDate = ActualEndDate ?? DateOnly.FromDateTime(DateTime.Today);
            return endDate.DayNumber - ExpectedEndDate.Value.DayNumber;
        }
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return ExpectedStartDate;
        yield return ExpectedEndDate;
        yield return ActualStartDate;
        yield return ActualEndDate;
    }

    private static void ValidateDates(
        DateOnly? expectedStartDate,
        DateOnly? expectedEndDate,
        DateOnly? actualStartDate,
        DateOnly? actualEndDate)
    {
        // Validate expected dates
        if (expectedStartDate.HasValue && expectedEndDate.HasValue)
        {
            if (expectedStartDate.Value > expectedEndDate.Value)
                throw new ArgumentException("Expected start date cannot be after expected end date");
        }

        // Validate actual dates
        if (actualStartDate.HasValue && actualEndDate.HasValue)
        {
            if (actualStartDate.Value > actualEndDate.Value)
                throw new ArgumentException("Actual start date cannot be after actual end date");
        }

        // Validate actual start date against expected
        if (expectedStartDate.HasValue && actualStartDate.HasValue)
        {
            // Allow some flexibility - actual start can be before expected (early start)
            // but warn if it's significantly different
            var daysDifference = Math.Abs(actualStartDate.Value.DayNumber - expectedStartDate.Value.DayNumber);
            if (daysDifference > 365) // More than a year difference seems unreasonable
                throw new ArgumentException("Actual start date is too far from expected start date");
        }

        // Validate actual end date against expected
        if (expectedEndDate.HasValue && actualEndDate.HasValue)
        {
            // Allow flexibility for project completion
            var daysDifference = Math.Abs(actualEndDate.Value.DayNumber - expectedEndDate.Value.DayNumber);
            if (daysDifference > 365) // More than a year difference seems unreasonable
                throw new ArgumentException("Actual end date is too far from expected end date");
        }
    }
}
