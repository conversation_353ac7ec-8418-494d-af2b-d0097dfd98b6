using Kantoku.SharedKernel.Guards;

namespace Kantoku.SharedKernel.Pagination;

/// <summary>
/// Represents sorting criteria for a query
/// </summary>
public class SortCriteria
{
    /// <summary>
    /// Gets the property name to sort by
    /// </summary>
    public string PropertyName { get; private set; }

    /// <summary>
    /// Gets the sort direction
    /// </summary>
    public SortDirection Direction { get; private set; }

    /// <summary>
    /// Initializes a new instance of the SortCriteria class
    /// </summary>
    /// <param name="propertyName">The property name to sort by</param>
    /// <param name="direction">The sort direction</param>
    public SortCriteria(string propertyName, SortDirection direction = SortDirection.Ascending)
    {
        PropertyName = Guard.NotNullOrWhiteSpace(propertyName);
        Direction = direction;
    }

    /// <summary>
    /// Creates ascending sort criteria
    /// </summary>
    /// <param name="propertyName">The property name to sort by</param>
    /// <returns>Ascending sort criteria</returns>
    public static SortCriteria Ascending(string propertyName)
    {
        return new SortCriteria(propertyName, SortDirection.Ascending);
    }

    /// <summary>
    /// Creates descending sort criteria
    /// </summary>
    /// <param name="propertyName">The property name to sort by</param>
    /// <returns>Descending sort criteria</returns>
    public static SortCriteria Descending(string propertyName)
    {
        return new SortCriteria(propertyName, SortDirection.Descending);
    }

    /// <summary>
    /// Gets a value indicating whether the sort direction is ascending
    /// </summary>
    public bool IsAscending => Direction == SortDirection.Ascending;

    /// <summary>
    /// Gets a value indicating whether the sort direction is descending
    /// </summary>
    public bool IsDescending => Direction == SortDirection.Descending;

    /// <summary>
    /// Returns the string representation of the sort criteria
    /// </summary>
    /// <returns>The sort criteria as a string</returns>
    public override string ToString()
    {
        return $"{PropertyName} {Direction}";
    }

    /// <summary>
    /// Determines whether this sort criteria is equal to another
    /// </summary>
    /// <param name="obj">The other object</param>
    /// <returns>True if the sort criteria are equal; otherwise, false</returns>
    public override bool Equals(object? obj)
    {
        if (obj is not SortCriteria other)
            return false;

        return PropertyName.Equals(other.PropertyName, StringComparison.OrdinalIgnoreCase) &&
               Direction == other.Direction;
    }

    /// <summary>
    /// Returns the hash code for this sort criteria
    /// </summary>
    /// <returns>A hash code for this sort criteria</returns>
    public override int GetHashCode()
    {
        return HashCode.Combine(PropertyName.ToLowerInvariant(), Direction);
    }

    /// <summary>
    /// Determines whether two sort criteria are equal
    /// </summary>
    /// <param name="left">The first sort criteria</param>
    /// <param name="right">The second sort criteria</param>
    /// <returns>True if the sort criteria are equal; otherwise, false</returns>
    public static bool operator ==(SortCriteria? left, SortCriteria? right)
    {
        if (left is null && right is null) return true;
        if (left is null || right is null) return false;
        return left.Equals(right);
    }

    /// <summary>
    /// Determines whether two sort criteria are not equal
    /// </summary>
    /// <param name="left">The first sort criteria</param>
    /// <param name="right">The second sort criteria</param>
    /// <returns>True if the sort criteria are not equal; otherwise, false</returns>
    public static bool operator !=(SortCriteria? left, SortCriteria? right)
    {
        return !(left == right);
    }
}

/// <summary>
/// Enumeration of sort directions
/// </summary>
public enum SortDirection
{
    /// <summary>
    /// Ascending sort direction (A to Z, 1 to 9)
    /// </summary>
    Ascending = 0,

    /// <summary>
    /// Descending sort direction (Z to A, 9 to 1)
    /// </summary>
    Descending = 1
}
