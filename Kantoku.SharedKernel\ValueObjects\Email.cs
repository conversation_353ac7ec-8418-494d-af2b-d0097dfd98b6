using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using Kantoku.SharedKernel.BuildingBlocks;
using Kantoku.SharedKernel.Guards;

namespace Kantoku.SharedKernel.ValueObjects;

/// <summary>
/// Value object representing an email address with validation
/// </summary>
public sealed class Email : ValueObject
{
    private static readonly Regex EmailRegex = new(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    /// <summary>
    /// Gets the email address value
    /// </summary>
    public string Value { get; private set; }

    /// <summary>
    /// Gets the local part of the email address (before @)
    /// </summary>
    public string LocalPart => Value.Split('@')[0];

    /// <summary>
    /// Gets the domain part of the email address (after @)
    /// </summary>
    public string Domain => Value.Split('@')[1];

    /// <summary>
    /// Initializes a new instance of the Email class
    /// </summary>
    /// <param name="value">The email address</param>
    /// <exception cref="ArgumentException">Thrown when the email address is invalid</exception>
    private Email(string value)
    {
        Value = Guard.NotNullOrWhiteSpace(value).Trim().ToLowerInvariant();

        if (!IsValid(Value))
            throw new ArgumentException($"Invalid email address: {value}", nameof(value));
    }

    /// <summary>
    /// Creates a new Email instance
    /// </summary>
    /// <param name="value">The email address</param>
    /// <returns>A new Email instance</returns>
    /// <exception cref="ArgumentException">Thrown when the email address is invalid</exception>
    public static Email Create(string value) => new(value);

    /// <summary>
    /// Tries to create a new Email instance
    /// </summary>
    /// <param name="value">The email address</param>
    /// <param name="email">The created Email instance if successful</param>
    /// <returns>True if the email was created successfully; otherwise, false</returns>
    public static bool TryCreate(string? value, out Email? email)
    {
        email = null;

        if (string.IsNullOrWhiteSpace(value))
            return false;

        try
        {
            email = new Email(value);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validates an email address
    /// </summary>
    /// <param name="email">The email address to validate</param>
    /// <returns>True if the email address is valid; otherwise, false</returns>
    public static bool IsValid(string? email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        email = email.Trim();

        // Basic length check
        if (email.Length > 254) // RFC 5321 limit
            return false;

        // Check for single @ symbol
        var atCount = email.Count(c => c == '@');
        if (atCount != 1)
            return false;

        // Use regex for format validation
        if (!EmailRegex.IsMatch(email))
            return false;

        // Additional validation using built-in EmailAddressAttribute
        var emailAttribute = new EmailAddressAttribute();
        return emailAttribute.IsValid(email);
    }

    /// <summary>
    /// Returns the string representation of the email address
    /// </summary>
    /// <returns>The email address as a string</returns>
    public override string ToString() => Value;

    /// <summary>
    /// Gets the equality components for value object comparison
    /// </summary>
    /// <returns>The equality components</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Value;
    }

    /// <summary>
    /// Implicitly converts an Email to a string
    /// </summary>
    /// <param name="email">The Email instance</param>
    public static implicit operator string(Email email) => email.Value;

    /// <summary>
    /// Explicitly converts a string to an Email
    /// </summary>
    /// <param name="value">The email address string</param>
    public static explicit operator Email(string value) => Create(value);
}
