using Kantoku.SharedKernel;
using Kantoku.Domain.CustomerManagement.Enums;

namespace Kantoku.Domain.CustomerManagement.Events;

/// <summary>
/// Domain event raised when a customer is created
/// </summary>
public class CustomerCreatedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public DateTime OccurredOn { get; }

    public CustomerCreatedEvent(Customer customer)
    {
        Customer = customer;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a customer is updated
/// </summary>
public class CustomerUpdatedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public DateTime OccurredOn { get; }

    public CustomerUpdatedEvent(Customer customer)
    {
        Customer = customer;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when customer address is updated
/// </summary>
public class CustomerAddressUpdatedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public DateTime OccurredOn { get; }

    public CustomerAddressUpdatedEvent(Customer customer)
    {
        Customer = customer;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when customer contact information is updated
/// </summary>
public class CustomerContactInfoUpdatedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public DateTime OccurredOn { get; }

    public CustomerContactInfoUpdatedEvent(Customer customer)
    {
        Customer = customer;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when customer business information is updated
/// </summary>
public class CustomerBusinessInfoUpdatedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public DateTime OccurredOn { get; }

    public CustomerBusinessInfoUpdatedEvent(Customer customer)
    {
        Customer = customer;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when customer status changes
/// </summary>
public class CustomerStatusChangedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public CustomerStatus OldStatus { get; }
    public CustomerStatus NewStatus { get; }
    public DateTime OccurredOn { get; }

    public CustomerStatusChangedEvent(Customer customer, CustomerStatus oldStatus, CustomerStatus newStatus)
    {
        Customer = customer;
        OldStatus = oldStatus;
        NewStatus = newStatus;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when customer financial information is updated
/// </summary>
public class CustomerFinancialInfoUpdatedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public DateTime OccurredOn { get; }

    public CustomerFinancialInfoUpdatedEvent(Customer customer)
    {
        Customer = customer;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when customer VIP status changes
/// </summary>
public class CustomerVipStatusChangedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public bool IsVip { get; }
    public DateTime OccurredOn { get; }

    public CustomerVipStatusChangedEvent(Customer customer, bool isVip)
    {
        Customer = customer;
        IsVip = isVip;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a customer is blacklisted
/// </summary>
public class CustomerBlacklistedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public CustomerBlacklistedEvent(Customer customer, string reason)
    {
        Customer = customer;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a customer is removed from blacklist
/// </summary>
public class CustomerRemovedFromBlacklistEvent : IDomainEvent
{
    public Customer Customer { get; }
    public DateTime OccurredOn { get; }

    public CustomerRemovedFromBlacklistEvent(Customer customer)
    {
        Customer = customer;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a customer is activated
/// </summary>
public class CustomerActivatedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public DateTime OccurredOn { get; }

    public CustomerActivatedEvent(Customer customer)
    {
        Customer = customer;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a customer is deactivated
/// </summary>
public class CustomerDeactivatedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public DateTime OccurredOn { get; }

    public CustomerDeactivatedEvent(Customer customer)
    {
        Customer = customer;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a contact is added to a customer
/// </summary>
public class CustomerContactAddedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public CustomerContact Contact { get; }
    public DateTime OccurredOn { get; }

    public CustomerContactAddedEvent(Customer customer, CustomerContact contact)
    {
        Customer = customer;
        Contact = contact;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a contact is removed from a customer
/// </summary>
public class CustomerContactRemovedEvent : IDomainEvent
{
    public Customer Customer { get; }
    public CustomerContact Contact { get; }
    public DateTime OccurredOn { get; }

    public CustomerContactRemovedEvent(Customer customer, CustomerContact contact)
    {
        Customer = customer;
        Contact = contact;
        OccurredOn = DateTime.UtcNow;
    }
}
