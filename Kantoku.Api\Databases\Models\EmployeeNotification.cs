namespace Kantoku.Api.Databases.Models;

public class EmployeeNotification
{
    public Guid EmployeeNotificationUid { get; set; }
    public Guid EmployeeUid { get; set; }
    public Guid NotificationUid { get; set; }

    public bool IsRead { get; set; }
    public DateTime? ReadAt { get; set; }

    public bool IsDeleted { get; set; }

    public virtual Notification Notification { get; set; } = null!;
    public virtual Employee Employee { get; set; } = null!;
}
