[{"errorStatus": 200, "errorCode": "KAN00", "errorMessage": "Success"}, {"errorStatus": 404, "errorCode": "KAN01", "errorMessage": "Requested resources Not Exist"}, {"errorStatus": 304, "errorCode": "KAN02", "errorMessage": "Resource Not Changed"}, {"errorStatus": 400, "errorCode": "KAN03", "errorMessage": "Resources not changed"}, {"errorStatus": 401, "errorCode": "KAN04", "errorMessage": "Unauthorized"}, {"errorStatus": 403, "errorCode": "KAN05", "errorMessage": "Forbidden"}, {"errorStatus": 500, "errorCode": "KAN500", "errorMessage": "Unknown Error"}, {"errorStatus": 400, "errorCode": "AUTH01", "errorMessage": "Password Incorrect"}, {"errorStatus": 500, "errorCode": "AUTH02", "errorMessage": "Password Changes Failed"}, {"errorStatus": 400, "errorCode": "AUTH03", "errorMessage": "Confirm Password Mismatch"}, {"errorStatus": 400, "errorCode": "AUTH04", "errorMessage": "Password Does Not Meet Requirement"}, {"errorStatus": 500, "errorCode": "AUTH05", "errorMessage": "Password Reset Failed"}, {"errorStatus": 403, "errorCode": "AUTH06", "errorMessage": "User is outsource labors, cannot login"}, {"errorStatus": 500, "errorCode": "AUTH07", "errorMessage": "Password Generate Failed"}, {"errorStatus": 401, "errorCode": "AUTH08", "errorMessage": "Login Failed"}, {"errorStatus": 500, "errorCode": "AUTH09", "errorMessage": "Sign Up Failed"}, {"errorStatus": 500, "errorCode": "AUTH10", "errorMessage": "Sign On Failed"}, {"errorStatus": 500, "errorCode": "AUTH11", "errorMessage": "OTP Generation Failed"}, {"errorStatus": 404, "errorCode": "AUTH12", "errorMessage": "OTP Code Does Not Exist"}, {"errorStatus": 400, "errorCode": "AUTH13", "errorMessage": "OTP Code Incorrect"}, {"errorStatus": 401, "errorCode": "TKN01", "errorMessage": "Token Expired"}, {"errorStatus": 401, "errorCode": "TKN02", "errorMessage": "Token Invalid"}, {"errorStatus": 500, "errorCode": "TKN03", "errorMessage": "Token Validate Failed"}, {"errorStatus": 500, "errorCode": "TKN04", "errorMessage": "Token Generate Failed"}, {"errorStatus": 404, "errorCode": "USR01", "errorMessage": "User Not Exist"}, {"errorStatus": 500, "errorCode": "USR03", "errorMessage": "User Create Failed"}, {"errorStatus": 500, "errorCode": "USR04", "errorMessage": "User Update Failed"}, {"errorStatus": 500, "errorCode": "USR05", "errorMessage": "User Delete Failed"}, {"errorStatus": 500, "errorCode": "USR06", "errorMessage": "Assign Role Failed"}, {"errorStatus": 409, "errorCode": "USR07", "errorMessage": "Login ID Already Exist"}, {"errorStatus": 500, "errorCode": "USR08", "errorMessage": "User Avatar Update Failed"}, {"errorStatus": 404, "errorCode": "USR09", "errorMessage": "User Avatar Not Set"}, {"errorStatus": 500, "errorCode": "USR10", "errorMessage": "User Avatar Delete Failed"}, {"errorStatus": 404, "errorCode": "EML01", "errorMessage": "Employee Leave Not Exist"}, {"errorStatus": 500, "errorCode": "EML02", "errorMessage": "Initial Employee Leave Failed"}, {"errorStatus": 500, "errorCode": "EML03", "errorMessage": "Update Employee Leave Failed"}, {"errorStatus": 500, "errorCode": "EML04", "errorMessage": "Delete Employee Leave Failed"}, {"errorStatus": 409, "errorCode": "EML05", "errorMessage": "Employee Leave Already Exist"}, {"errorStatus": 400, "errorCode": "EML06", "errorMessage": "Employee Leave Expire Date Not Valid"}, {"errorStatus": 500, "errorCode": "CRT01", "errorMessage": "Contract create failed"}, {"errorStatus": 500, "errorCode": "CRT02", "errorMessage": "Contract update failed"}, {"errorStatus": 404, "errorCode": "ORG01", "errorMessage": "Organization Not Exist"}, {"errorStatus": 500, "errorCode": "ORG02", "errorMessage": "Organization Create Failed"}, {"errorStatus": 500, "errorCode": "ORG03", "errorMessage": "Organization Update Failed"}, {"errorStatus": 500, "errorCode": "ORG04", "errorMessage": "Organization Delete Failed"}, {"errorStatus": 403, "errorCode": "ORG05", "errorMessage": "Not Organization Owner"}, {"errorStatus": 403, "errorCode": "ORG06", "errorMessage": "Not Organization Admin"}, {"errorStatus": 404, "errorCode": "STR01", "errorMessage": "Structure Not Exist"}, {"errorStatus": 500, "errorCode": "STR02", "errorMessage": "Structure Create Failed"}, {"errorStatus": 500, "errorCode": "STR03", "errorMessage": "Structure Update Failed"}, {"errorStatus": 500, "errorCode": "STR04", "errorMessage": "Structure Delete Failed"}, {"errorStatus": 400, "errorCode": "STR05", "errorMessage": "Structure Input Data Invalid"}, {"errorStatus": 404, "errorCode": "POS01", "errorMessage": "Position Not Exist"}, {"errorStatus": 500, "errorCode": "POS02", "errorMessage": "Position Create Failed"}, {"errorStatus": 500, "errorCode": "POS03", "errorMessage": "Position Update Failed"}, {"errorStatus": 500, "errorCode": "POS04", "errorMessage": "Position Delete Failed"}, {"errorStatus": 404, "errorCode": "RNK01", "errorMessage": "Ranking Not Exist"}, {"errorStatus": 500, "errorCode": "RNK02", "errorMessage": "Ranking Create Failed"}, {"errorStatus": 500, "errorCode": "RNK03", "errorMessage": "Ranking Update Failed"}, {"errorStatus": 500, "errorCode": "RNK04", "errorMessage": "Ranking Delete Failed"}, {"errorStatus": 404, "errorCode": "WPL01", "errorMessage": "Workplace Not Exist"}, {"errorStatus": 500, "errorCode": "WPL02", "errorMessage": "Workplace Create Failed"}, {"errorStatus": 500, "errorCode": "WPL03", "errorMessage": "Workplace Update Failed"}, {"errorStatus": 404, "errorCode": "GLC01", "errorMessage": "Global Config Not Exist"}, {"errorStatus": 404, "errorCode": "MAIL01", "errorMessage": "Email Template Not Exist"}, {"errorStatus": 500, "errorCode": "MAIL02", "errorMessage": "Email sent failed"}, {"errorStatus": 404, "errorCode": "ROLE01", "errorMessage": "Role Not Exist"}, {"errorStatus": 500, "errorCode": "ROLE02", "errorMessage": "Role create failed"}, {"errorStatus": 500, "errorCode": "ROLE03", "errorMessage": "Role update failed"}, {"errorStatus": 500, "errorCode": "ROLE04", "errorMessage": "Role delete failed"}, {"errorStatus": 404, "errorCode": "COMN01", "errorMessage": "Unit Not Exist"}, {"errorStatus": 404, "errorCode": "COMN02", "errorMessage": "Request Type Not Exist"}, {"errorStatus": 404, "errorCode": "COMN03", "errorMessage": "Leave Type Not Exist"}, {"errorStatus": 404, "errorCode": "COMN04", "errorMessage": "Status Not Exist"}, {"errorStatus": 404, "errorCode": "REQ01", "errorMessage": "Request Not Exist"}, {"errorStatus": 500, "errorCode": "REQ02", "errorMessage": "Request Create Failed"}, {"errorStatus": 500, "errorCode": "REQ03", "errorMessage": "Request Update Failed"}, {"errorStatus": 500, "errorCode": "REQ04", "errorMessage": "Approve Attendance Failed"}, {"errorStatus": 500, "errorCode": "REQ05", "errorMessage": "Approve Request Failed"}, {"errorStatus": 500, "errorCode": "REQ06", "errorMessage": "Reject Request Failed"}, {"errorStatus": 500, "errorCode": "REQ07", "errorMessage": "Cancel request failed"}, {"errorStatus": 400, "errorCode": "REQ08", "errorMessage": "Request Detail Invalid"}, {"errorStatus": 403, "errorCode": "REQ09", "errorMessage": "No Approval Permission"}, {"errorStatus": 400, "errorCode": "REQ10", "errorMessage": "Already Processed Or Cancelled"}, {"errorStatus": 400, "errorCode": "REQ11", "errorMessage": "No Project Assigned with this request"}, {"errorStatus": 400, "errorCode": "REQ12", "errorMessage": "Request Leave On Day Off"}, {"errorStatus": 400, "errorCode": "REQ13", "errorMessage": "Request Attendance Daily Not Checkout"}, {"errorStatus": 500, "errorCode": "REQ14", "errorMessage": "Request Update Status Failed"}, {"errorStatus": 500, "errorCode": "REQ15", "errorMessage": "Request Attendance Daily Create Failed"}, {"errorStatus": 409, "errorCode": "REQ16", "errorMessage": "All shifts for this desired day is requested"}, {"errorStatus": 400, "errorCode": "REQ17", "errorMessage": "Request In/Out On Day Off"}, {"errorStatus": 400, "errorCode": "REQ18", "errorMessage": "Request Attendance Daily Invalid Date"}, {"errorStatus": 409, "errorCode": "REQ19", "errorMessage": "Request Already Approved"}, {"errorStatus": 409, "errorCode": "REQ20", "errorMessage": "Request Already Cancelled"}, {"errorStatus": 500, "errorCode": "REQ21", "errorMessage": "Request Delete Failed"}, {"errorStatus": 404, "errorCode": "TRL01", "errorMessage": "Translator Not Exist"}, {"errorStatus": 404, "errorCode": "SFT01", "errorMessage": "Shift Not Exist"}, {"errorStatus": 500, "errorCode": "SFT02", "errorMessage": "Shift Create Failed"}, {"errorStatus": 500, "errorCode": "SFT03", "errorMessage": "Shift Update Failed"}, {"errorStatus": 500, "errorCode": "SFT04", "errorMessage": "Shift remove failed"}, {"errorStatus": 500, "errorCode": "SFT05", "errorMessage": "Check In Failed"}, {"errorStatus": 500, "errorCode": "SFT06", "errorMessage": "Check Out Failed"}, {"errorStatus": 500, "errorCode": "SFT07", "errorMessage": "Break In Failed"}, {"errorStatus": 500, "errorCode": "SFT08", "errorMessage": "Break Out Failed"}, {"errorStatus": 409, "errorCode": "SFT09", "errorMessage": "Already Checked In"}, {"errorStatus": 409, "errorCode": "SFT10", "errorMessage": "Already In A Break"}, {"errorStatus": 409, "errorCode": "SFT11", "errorMessage": "Not Checked In Yet"}, {"errorStatus": 400, "errorCode": "SFT12", "errorMessage": "Shift Already Ended"}, {"errorStatus": 400, "errorCode": "SFT13", "errorMessage": "Not In A Break"}, {"errorStatus": 400, "errorCode": "SFT14", "errorMessage": "Not Broken Out Yet"}, {"errorStatus": 409, "errorCode": "SFT15", "errorMessage": "Break time exceeded"}, {"errorStatus": 500, "errorCode": "SFT16", "errorMessage": "Create additional shift failed"}, {"errorStatus": 400, "errorCode": "SFT17", "errorMessage": "Cannot Update Approved Shift"}, {"errorStatus": 409, "errorCode": "SFT18", "errorMessage": "Already Checked Out"}, {"errorStatus": 500, "errorCode": "SFT19", "errorMessage": "Delete Shift Failed"}, {"errorStatus": 404, "errorCode": "SCH01", "errorMessage": "Schedule Not Exist"}, {"errorStatus": 500, "errorCode": "SCH02", "errorMessage": "Schedule Create Failed"}, {"errorStatus": 500, "errorCode": "SCH03", "errorMessage": "Schedule Update Failed"}, {"errorStatus": 500, "errorCode": "SCH04", "errorMessage": "Schedule Remove Failed"}, {"errorStatus": 500, "errorCode": "SCH05", "errorMessage": "Export Schedule Failed"}, {"errorStatus": 409, "errorCode": "SCH06", "errorMessage": "Already has scheduled"}, {"errorStatus": 400, "errorCode": "SCH07", "errorMessage": "Schedule Parse Failed"}, {"errorStatus": 404, "errorCode": "PRJ01", "errorMessage": "Project Not Exist"}, {"errorStatus": 500, "errorCode": "PRJ02", "errorMessage": "Project Create Failed"}, {"errorStatus": 500, "errorCode": "PRJ03", "errorMessage": "Project Update Failed"}, {"errorStatus": 500, "errorCode": "PRJ04", "errorMessage": "Project Delete Failed"}, {"errorStatus": 404, "errorCode": "PRJ05", "errorMessage": "Project Summary Not Exist"}, {"errorStatus": 404, "errorCode": "GPS01", "errorMessage": "Location Not Exist"}, {"errorStatus": 400, "errorCode": "GPS02", "errorMessage": "Location info invalid"}, {"errorStatus": 404, "errorCode": "HOL01", "errorMessage": "Holiday Not Exist"}, {"errorStatus": 500, "errorCode": "HOL02", "errorMessage": "Holiday create failed"}, {"errorStatus": 500, "errorCode": "HOL03", "errorMessage": "Holiday update failed"}, {"errorStatus": 404, "errorCode": "URF01", "errorMessage": "Functional Role Not Exist"}, {"errorStatus": 500, "errorCode": "URF02", "errorMessage": "Functional role create failed"}, {"errorStatus": 500, "errorCode": "URF03", "errorMessage": "Functional role update failed"}, {"errorStatus": 404, "errorCode": "FNC01", "errorMessage": "Function Not Exist"}, {"errorStatus": 500, "errorCode": "FNC02", "errorMessage": "Function create failed"}, {"errorStatus": 500, "errorCode": "FNC03", "errorMessage": "Function update failed"}, {"errorStatus": 500, "errorCode": "HST01", "errorMessage": "Tracking history retrieve failed"}, {"errorStatus": 500, "errorCode": "HST02", "errorMessage": "Tracking history create failed"}, {"errorStatus": 404, "errorCode": "MIO01", "errorMessage": "Bucket Not Exist"}, {"errorStatus": 404, "errorCode": "MIO02", "errorMessage": "Object Not Exist"}, {"errorStatus": 404, "errorCode": "MIO03", "errorMessage": "Object Metadata Not Exist"}, {"errorStatus": 500, "errorCode": "MIO04", "errorMessage": "Object upload failed"}, {"errorStatus": 500, "errorCode": "MIO05", "errorMessage": "Object download failed"}, {"errorStatus": 500, "errorCode": "MIO06", "errorMessage": "Object create failed"}, {"errorStatus": 500, "errorCode": "MIO07", "errorMessage": "Object delete failed"}, {"errorStatus": 500, "errorCode": "RPT01", "errorMessage": "Attendance report create failed"}, {"errorStatus": 500, "errorCode": "RPT02", "errorMessage": "Attendance report update failed"}, {"errorStatus": 404, "errorCode": "RPT03", "errorMessage": "Attendance report delete failed"}, {"errorStatus": 409, "errorCode": "RPT04", "errorMessage": "Attendance report has already been approved, cannot update"}, {"errorStatus": 409, "errorCode": "RPT05", "errorMessage": "Project Daily Report Already Exists"}, {"errorStatus": 404, "errorCode": "RPT06", "errorMessage": "Project Daily Report Not Exist"}, {"errorStatus": 500, "errorCode": "RPT07", "errorMessage": "Project Daily Report Create Failed"}, {"errorStatus": 500, "errorCode": "RPT08", "errorMessage": "Project Daily Report Update Failed"}, {"errorStatus": 500, "errorCode": "RPT09", "errorMessage": "Project Daily Report Delete Failed"}, {"errorStatus": 404, "errorCode": "CTG01", "errorMessage": "Category Not Exist"}, {"errorStatus": 500, "errorCode": "CTG02", "errorMessage": "Category create failed"}, {"errorStatus": 500, "errorCode": "CTG03", "errorMessage": "Category update failed"}, {"errorStatus": 500, "errorCode": "CTG04", "errorMessage": "Category Delete Failed"}, {"errorStatus": 404, "errorCode": "VDR01", "errorMessage": "<PERSON><PERSON><PERSON> Not Exist"}, {"errorStatus": 500, "errorCode": "VDR02", "errorMessage": "Vendor create failed"}, {"errorStatus": 500, "errorCode": "VDR03", "errorMessage": "Vendor update failed"}, {"errorStatus": 500, "errorCode": "VDR04", "errorMessage": "Vendor Delete Failed"}, {"errorStatus": 404, "errorCode": "VDR05", "errorMessage": "<PERSON><PERSON><PERSON> Not Found"}, {"errorStatus": 400, "errorCode": "VDR06", "errorMessage": "Vendor input data invalid"}, {"errorStatus": 404, "errorCode": "PRS01", "errorMessage": "Process Not Exist"}, {"errorStatus": 500, "errorCode": "PRS02", "errorMessage": "Process create failed"}, {"errorStatus": 500, "errorCode": "PRS03", "errorMessage": "Process update failed"}, {"errorStatus": 404, "errorCode": "IT01", "errorMessage": "Item Not Exist"}, {"errorStatus": 500, "errorCode": "IT02", "errorMessage": "Item create failed"}, {"errorStatus": 500, "errorCode": "IT03", "errorMessage": "Item update failed"}, {"errorStatus": 500, "errorCode": "IT04", "errorMessage": "Item Delete Failed"}, {"errorStatus": 404, "errorCode": "IT05", "errorMessage": "Item Illustration Not Exist"}, {"errorStatus": 404, "errorCode": "ITP01", "errorMessage": "<PERSON><PERSON> Price Not Exist"}, {"errorStatus": 500, "errorCode": "ITP02", "errorMessage": "<PERSON>em Price Create Failed"}, {"errorStatus": 500, "errorCode": "ITP03", "errorMessage": "Item Price Update Failed"}, {"errorStatus": 404, "errorCode": "IPC01", "errorMessage": "Input Cost Not Exist"}, {"errorStatus": 500, "errorCode": "IPC02", "errorMessage": "Input cost create failed"}, {"errorStatus": 500, "errorCode": "IPC03", "errorMessage": "Input cost update failed"}, {"errorStatus": 500, "errorCode": "IPC04", "errorMessage": "Input cost delete failed"}, {"errorStatus": 404, "errorCode": "ET01", "errorMessage": "Entry Type Not Exist"}, {"errorStatus": 500, "errorCode": "ET02", "errorMessage": "Entry type create failed"}, {"errorStatus": 500, "errorCode": "ET03", "errorMessage": "Entry type update failed"}, {"errorStatus": 500, "errorCode": "ET04", "errorMessage": "Entry Type Delete Failed"}, {"errorStatus": 404, "errorCode": "MFTR01", "errorMessage": "Manufacturer Not Exist"}, {"errorStatus": 500, "errorCode": "MFTR02", "errorMessage": "Manufacturer create failed"}, {"errorStatus": 500, "errorCode": "MFTR03", "errorMessage": "Manufacturer update failed"}, {"errorStatus": 500, "errorCode": "MFTR04", "errorMessage": "Manufacturer Delete Failed"}, {"errorStatus": 404, "errorCode": "MFTR05", "errorMessage": "Manufacturer Logo Not Found"}, {"errorStatus": 404, "errorCode": "IPCI01", "errorMessage": "Input Cost Item Not Exist"}, {"errorStatus": 500, "errorCode": "IPCI02", "errorMessage": "Input cost item create failed"}, {"errorStatus": 500, "errorCode": "IPCI03", "errorMessage": "Input cost item update failed"}, {"errorStatus": 500, "errorCode": "IPCI04", "errorMessage": "Input cost item delete failed"}, {"errorStatus": 404, "errorCode": "PMT01", "errorMessage": "Payment Type Not Found"}, {"errorStatus": 500, "errorCode": "PMT02", "errorMessage": "Payment Type Create Failed"}, {"errorStatus": 500, "errorCode": "PMT03", "errorMessage": "Payment Type Update Failed"}, {"errorStatus": 500, "errorCode": "PMT04", "errorMessage": "Payment Type Delete Failed"}, {"errorStatus": 409, "errorCode": "AC01", "errorMessage": "Account Already Exists"}, {"errorStatus": 404, "errorCode": "AC02", "errorMessage": "Account Not Exist"}, {"errorStatus": 500, "errorCode": "AC03", "errorMessage": "Account Create Failed"}, {"errorStatus": 500, "errorCode": "AC04", "errorMessage": "Account Update Failed"}, {"errorStatus": 500, "errorCode": "AC05", "errorMessage": "Account Delete Failed"}, {"errorStatus": 500, "errorCode": "AC06", "errorMessage": "Account Lock Failed"}, {"errorStatus": 500, "errorCode": "AC07", "errorMessage": "Account <PERSON><PERSON> Failed"}, {"errorStatus": 400, "errorCode": "AC08", "errorMessage": "Email or Login ID Already Exists"}, {"errorStatus": 404, "errorCode": "USI01", "errorMessage": "User Info Not Exist"}, {"errorStatus": 409, "errorCode": "USI02", "errorMessage": "User Info Already Exist"}, {"errorStatus": 500, "errorCode": "USI03", "errorMessage": "User Info Create Failed"}, {"errorStatus": 500, "errorCode": "USI04", "errorMessage": "User Info Update Failed"}, {"errorStatus": 500, "errorCode": "USI05", "errorMessage": "User Info Delete Failed"}, {"errorStatus": 404, "errorCode": "USI06", "errorMessage": "User Info Avatar Not Set"}, {"errorStatus": 500, "errorCode": "USI07", "errorMessage": "User Info Avatar Update Failed"}, {"errorStatus": 500, "errorCode": "USI08", "errorMessage": "User Info Avatar Delete Failed"}, {"errorStatus": 404, "errorCode": "EMP01", "errorMessage": "Employee Not Exist"}, {"errorStatus": 409, "errorCode": "EMP02", "errorMessage": "Employee Already Exist"}, {"errorStatus": 500, "errorCode": "EMP03", "errorMessage": "Employee Create Failed"}, {"errorStatus": 500, "errorCode": "EMP04", "errorMessage": "Employee Update Failed"}, {"errorStatus": 500, "errorCode": "EMP05", "errorMessage": "Employee Delete Failed"}, {"errorStatus": 500, "errorCode": "EMP06", "errorMessage": "Employee Invitation Failed"}, {"errorStatus": 404, "errorCode": "EMP07", "errorMessage": "Employee Invitation Not Exist"}, {"errorStatus": 500, "errorCode": "EMP08", "errorMessage": "Employee Invitation Delete Failed"}, {"errorStatus": 500, "errorCode": "EMP09", "errorMessage": "Employee Invitation Accept Failed"}, {"errorStatus": 500, "errorCode": "EMP10", "errorMessage": "Employee Invitation Reject Failed"}, {"errorStatus": 409, "errorCode": "EMP11", "errorMessage": "Employee Invitation Already Exist"}, {"errorStatus": 500, "errorCode": "EMP12", "errorMessage": "Link account with organization failed"}, {"errorStatus": 500, "errorCode": "FIL01", "errorMessage": "File Upload Failed"}, {"errorStatus": 500, "errorCode": "FIL02", "errorMessage": "File Download Failed"}, {"errorStatus": 500, "errorCode": "FIL03", "errorMessage": "File Delete Failed"}, {"errorStatus": 404, "errorCode": "FIL04", "errorMessage": "File Metadata Not Exist"}, {"errorStatus": 500, "errorCode": "FIL05", "errorMessage": "File Metadata Create Failed"}, {"errorStatus": 500, "errorCode": "FIL06", "errorMessage": "File Metadata Update Failed"}, {"errorStatus": 404, "errorCode": "WOS01", "errorMessage": "Work Shift Not Exist"}, {"errorStatus": 500, "errorCode": "WOS02", "errorMessage": "Work Shift Create Failed"}, {"errorStatus": 500, "errorCode": "WOS03", "errorMessage": "Work Shift Update Failed"}, {"errorStatus": 500, "errorCode": "WOS04", "errorMessage": "Work Shift Delete Failed"}, {"errorStatus": 500, "errorCode": "WOS05", "errorMessage": "Work Shift Assign Failed"}, {"errorStatus": 400, "errorCode": "WOS06", "errorMessage": "Work Shift Break Time Invalid"}, {"errorStatus": 400, "errorCode": "WOS07", "errorMessage": "There is no work shift have been assigned to the working project"}, {"errorStatus": 404, "errorCode": "EVC01", "errorMessage": "Event calendar does not exist"}, {"errorStatus": 500, "errorCode": "EVC02", "errorMessage": "Event calendar creation failed"}, {"errorStatus": 500, "errorCode": "EVC03", "errorMessage": "Event calendar update failed"}, {"errorStatus": 500, "errorCode": "EVC04", "errorMessage": "Event calendar deletion failed"}, {"errorStatus": 400, "errorCode": "EVC05", "errorMessage": "Event calendar date is invalid"}, {"errorStatus": 409, "errorCode": "EVC06", "errorMessage": "Event calendar already exists"}, {"errorStatus": 404, "errorCode": "UNT01", "errorMessage": "Unit Not Exist"}, {"errorStatus": 500, "errorCode": "UNT02", "errorMessage": "Unit Create Failed"}, {"errorStatus": 500, "errorCode": "UNT03", "errorMessage": "Unit Update Failed"}, {"errorStatus": 500, "errorCode": "UNT04", "errorMessage": "Unit Delete Failed"}, {"errorStatus": 404, "errorCode": "OS01", "errorMessage": "Outsource Not Exist"}, {"errorStatus": 500, "errorCode": "OS02", "errorMessage": "Outsource Create Failed"}, {"errorStatus": 500, "errorCode": "OS03", "errorMessage": "Outsource Update Failed"}, {"errorStatus": 500, "errorCode": "OS04", "errorMessage": "Outsource Delete Failed"}, {"errorStatus": 404, "errorCode": "OS05", "errorMessage": "Outsource Price Not Exist"}, {"errorStatus": 500, "errorCode": "OS06", "errorMessage": "Outsource Price Create Failed"}, {"errorStatus": 500, "errorCode": "OS07", "errorMessage": "Outsource Price Update Failed"}, {"errorStatus": 404, "errorCode": "OS08", "errorMessage": "Outsource Logo Not Found"}, {"errorStatus": 404, "errorCode": "PRT01", "errorMessage": "Project Type Not Exist"}, {"errorStatus": 500, "errorCode": "PRT02", "errorMessage": "Project Type Create Failed"}, {"errorStatus": 500, "errorCode": "PRT03", "errorMessage": "Project Type Update Failed"}, {"errorStatus": 404, "errorCode": "CON01", "errorMessage": "Construction Not Exist"}, {"errorStatus": 500, "errorCode": "CON02", "errorMessage": "Construction Create Failed"}, {"errorStatus": 500, "errorCode": "CON03", "errorMessage": "Construction Update Failed"}, {"errorStatus": 500, "errorCode": "CON04", "errorMessage": "Construction Delete Failed"}, {"errorStatus": 409, "errorCode": "CON05", "errorMessage": "Sub Construction Already Exist"}, {"errorStatus": 404, "errorCode": "CONC01", "errorMessage": "Construction Cost Not Exist"}, {"errorStatus": 500, "errorCode": "CONC02", "errorMessage": "Construction Cost Create Failed"}, {"errorStatus": 500, "errorCode": "CONC03", "errorMessage": "Construction Cost Update Failed"}, {"errorStatus": 500, "errorCode": "CONC04", "errorMessage": "Construction Cost Delete Failed"}, {"errorStatus": 404, "errorCode": "CUS01", "errorMessage": "Customer Not Exist"}, {"errorStatus": 500, "errorCode": "CUS02", "errorMessage": "Customer Create Failed"}, {"errorStatus": 500, "errorCode": "CUS03", "errorMessage": "Customer Update Failed"}, {"errorStatus": 500, "errorCode": "CUS04", "errorMessage": "Customer Delete Failed"}, {"errorStatus": 404, "errorCode": "CUS05", "errorMessage": "Customer <PERSON>go Not Found"}, {"errorStatus": 404, "errorCode": "CTR01", "errorMessage": "Contractor Not Exist"}, {"errorStatus": 500, "errorCode": "CTR02", "errorMessage": "Contractor <PERSON><PERSON> Failed"}, {"errorStatus": 500, "errorCode": "CTR03", "errorMessage": "Contractor Update Failed"}, {"errorStatus": 500, "errorCode": "CTR04", "errorMessage": "Contractor Delete Failed"}, {"errorStatus": 404, "errorCode": "CTR05", "errorMessage": "Contractor <PERSON><PERSON> Found"}, {"errorStatus": 404, "errorCode": "CT01", "errorMessage": "Customer Type Not Exist"}, {"errorStatus": 404, "errorCode": "AUD01", "errorMessage": "No audit log found"}]