namespace Kantoku.Domain.Models;

public class EmployeeInvitation : AuditableEntity
{
    public Guid? EmployeeInvitationUid { get; set; }
    public Guid OrgUid { get; set; }

    public string Email { get; set; } = null!;


    public string PreassignedEmployeeCode { get; set; } = null!;


    public ICollection<Guid>? PreassignedRoleUid { get; set; }


    public string? InvitationDescription { get; set; }


    public DateTime? ExpiredTime { get; set; }

    public bool IsAccepted { get; set; }
    public DateTime? AcceptedTime { get; set; }

    public bool IsDeleted { get; set; }
    public virtual Org Org { get; set; } = null!;
}
