using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class DuplicateProjectScheduleRequestDto
{
    /// <summary>
    /// Target project id (*)
    /// </summary>
    [Required]
    [JsonPropertyName("projectId")]
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// Target working date (from this date) (*)
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [DateTimeValidator(typeof(DateOnly))]
    [JsonPropertyName("targetDateFrom")]
    public required DateOnly TargetDateFrom { get; set; }

    /// <summary>
    /// Target working date to (to this date) (*)
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [DateTimeValidator(typeof(DateOnly))]
    [JsonPropertyName("targetDateTo")]
    public required DateOnly TargetDateTo { get; set; }
}