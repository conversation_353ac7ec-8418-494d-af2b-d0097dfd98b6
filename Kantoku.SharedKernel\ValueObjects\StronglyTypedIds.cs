using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.SharedKernel.ValueObjects;

// Organization Management IDs
public sealed class OrganizationId : StronglyTypedId<OrganizationId>
{
    public OrganizationId(Guid value) : base(value) { }
}

public sealed class StructureId : StronglyTypedId<StructureId>
{
    public StructureId(Guid value) : base(value) { }
}

public sealed class PositionId : StronglyTypedId<PositionId>
{
    public PositionId(Guid value) : base(value) { }
}

public sealed class RankingId : StronglyTypedId<RankingId>
{
    public RankingId(Guid value) : base(value) { }
}

public sealed class WorkShiftId : StronglyTypedId<WorkShiftId>
{
    public WorkShiftId(Guid value) : base(value) { }
}

// Employee Management IDs
public sealed class EmployeeId : StronglyTypedId<EmployeeId>
{
    public EmployeeId(Guid value) : base(value) { }
}

public sealed class EmployeeLeaveId : StronglyTypedId<EmployeeLeaveId>
{
    public EmployeeLeaveId(Guid value) : base(value) { }
}

public sealed class EmployeeRoleId : StronglyTypedId<EmployeeRoleId>
{
    public EmployeeRoleId(Guid value) : base(value) { }
}

public sealed class EmployeeShiftId : StronglyTypedId<EmployeeShiftId>
{
    public EmployeeShiftId(Guid value) : base(value) { }
}

// Project Management IDs
public sealed class ProjectId : StronglyTypedId<ProjectId>
{
    public ProjectId(Guid value) : base(value) { }
}

public sealed class ProjectManagerId : StronglyTypedId<ProjectManagerId>
{
    public ProjectManagerId(Guid value) : base(value) { }
}

public sealed class ProjectScheduleId : StronglyTypedId<ProjectScheduleId>
{
    public ProjectScheduleId(Guid value) : base(value) { }
}

public sealed class ConstructionId : StronglyTypedId<ConstructionId>
{
    public ConstructionId(Guid value) : base(value) { }
}

public sealed class ProjectDailyReportId : StronglyTypedId<ProjectDailyReportId>
{
    public ProjectDailyReportId(Guid value) : base(value) { }
}

public sealed class ConstructionCostId : StronglyTypedId<ConstructionCostId>
{
    public ConstructionCostId(Guid value) : base(value) { }
}

// Request Management IDs
public sealed class RequestId : StronglyTypedId<RequestId>
{
    public RequestId(Guid value) : base(value) { }
}

public sealed class RequestApprovalId : StronglyTypedId<RequestApprovalId>
{
    public RequestApprovalId(Guid value) : base(value) { }
}

// Inventory Management IDs
public sealed class ItemId : StronglyTypedId<ItemId>
{
    public ItemId(Guid value) : base(value) { }
}

public sealed class ItemPriceId : StronglyTypedId<ItemPriceId>
{
    public ItemPriceId(Guid value) : base(value) { }
}

public sealed class ItemStockId : StronglyTypedId<ItemStockId>
{
    public ItemStockId(Guid value) : base(value) { }
}

public sealed class CategoryId : StronglyTypedId<CategoryId>
{
    public CategoryId(Guid value) : base(value) { }
}

// Account Management IDs
public sealed class AccountId : StronglyTypedId<AccountId>
{
    public AccountId(Guid value) : base(value) { }
}

public sealed class AccountSessionId : StronglyTypedId<AccountSessionId>
{
    public AccountSessionId(Guid value) : base(value) { }
}

public sealed class UserRoleId : StronglyTypedId<UserRoleId>
{
    public UserRoleId(Guid value) : base(value) { }
}

// Customer Management IDs
public sealed class CustomerId : StronglyTypedId<CustomerId>
{
    public CustomerId(Guid value) : base(value) { }
}

public sealed class CustomerContactId : StronglyTypedId<CustomerContactId>
{
    public CustomerContactId(Guid value) : base(value) { }
}

// Contractor Management IDs
public sealed class ContractorId : StronglyTypedId<ContractorId>
{
    public ContractorId(Guid value) : base(value) { }
}

public sealed class ContractorContactId : StronglyTypedId<ContractorContactId>
{
    public ContractorContactId(Guid value) : base(value) { }
}

public sealed class ContractorCertificationId : StronglyTypedId<ContractorCertificationId>
{
    public ContractorCertificationId(Guid value) : base(value) { }
}

// Notification Management IDs
public sealed class NotificationId : StronglyTypedId<NotificationId>
{
    public NotificationId(Guid value) : base(value) { }
}

public sealed class NotificationTargetId : StronglyTypedId<NotificationTargetId>
{
    public NotificationTargetId(Guid value) : base(value) { }
}

// Additional IDs for other entities
public sealed class RoleId : StronglyTypedId<RoleId>
{
    public RoleId(Guid value) : base(value) { }
}

public sealed class ProjectTypeId : StronglyTypedId<ProjectTypeId>
{
    public ProjectTypeId(Guid value) : base(value) { }
}

public sealed class LeaveTypeId : StronglyTypedId<LeaveTypeId>
{
    public LeaveTypeId(Guid value) : base(value) { }
}

public sealed class VendorId : StronglyTypedId<VendorId>
{
    public VendorId(Guid value) : base(value) { }
}

public sealed class ManufacturerId : StronglyTypedId<ManufacturerId>
{
    public ManufacturerId(Guid value) : base(value) { }
}
