using Kantoku.SharedKernel;
using Kantoku.Domain.ContractorManagement.Enums;
using Kantoku.Domain.ContractorManagement.ValueObjects;

namespace Kantoku.Domain.ContractorManagement.Events;

/// <summary>
/// Domain event raised when a contractor is created
/// </summary>
public class ContractorCreatedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public DateTime OccurredOn { get; }

    public ContractorCreatedEvent(Contractor contractor)
    {
        Contractor = contractor;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a contractor is updated
/// </summary>
public class ContractorUpdatedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public DateTime OccurredOn { get; }

    public ContractorUpdatedEvent(Contractor contractor)
    {
        Contractor = contractor;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when contractor capabilities are updated
/// </summary>
public class ContractorCapabilitiesUpdatedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public DateTime OccurredOn { get; }

    public ContractorCapabilitiesUpdatedEvent(Contractor contractor)
    {
        Contractor = contractor;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when contractor status changes
/// </summary>
public class ContractorStatusChangedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public ContractorStatus OldStatus { get; }
    public ContractorStatus NewStatus { get; }
    public DateTime OccurredOn { get; }

    public ContractorStatusChangedEvent(Contractor contractor, ContractorStatus oldStatus, ContractorStatus newStatus)
    {
        Contractor = contractor;
        OldStatus = oldStatus;
        NewStatus = newStatus;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when contractor insurance information is updated
/// </summary>
public class ContractorInsuranceUpdatedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public DateTime OccurredOn { get; }

    public ContractorInsuranceUpdatedEvent(Contractor contractor)
    {
        Contractor = contractor;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when contractor license information is updated
/// </summary>
public class ContractorLicenseUpdatedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public DateTime OccurredOn { get; }

    public ContractorLicenseUpdatedEvent(Contractor contractor)
    {
        Contractor = contractor;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when contractor preferred status changes
/// </summary>
public class ContractorPreferredStatusChangedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public bool IsPreferred { get; }
    public DateTime OccurredOn { get; }

    public ContractorPreferredStatusChangedEvent(Contractor contractor, bool isPreferred)
    {
        Contractor = contractor;
        IsPreferred = isPreferred;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a contractor is blacklisted
/// </summary>
public class ContractorBlacklistedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public ContractorBlacklistedEvent(Contractor contractor, string reason)
    {
        Contractor = contractor;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a contractor is removed from blacklist
/// </summary>
public class ContractorRemovedFromBlacklistEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public DateTime OccurredOn { get; }

    public ContractorRemovedFromBlacklistEvent(Contractor contractor)
    {
        Contractor = contractor;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a contractor is qualified
/// </summary>
public class ContractorQualifiedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public DateTime OccurredOn { get; }

    public ContractorQualifiedEvent(Contractor contractor)
    {
        Contractor = contractor;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a contractor is disqualified
/// </summary>
public class ContractorDisqualifiedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public ContractorDisqualifiedEvent(Contractor contractor, string reason)
    {
        Contractor = contractor;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a certification is added to a contractor
/// </summary>
public class ContractorCertificationAddedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public ContractorCertification Certification { get; }
    public DateTime OccurredOn { get; }

    public ContractorCertificationAddedEvent(Contractor contractor, ContractorCertification certification)
    {
        Contractor = contractor;
        Certification = certification;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a certification is removed from a contractor
/// </summary>
public class ContractorCertificationRemovedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public ContractorCertification Certification { get; }
    public DateTime OccurredOn { get; }

    public ContractorCertificationRemovedEvent(Contractor contractor, ContractorCertification certification)
    {
        Contractor = contractor;
        Certification = certification;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a contractor contact is added
/// </summary>
public class ContractorContactAddedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public ContractorContact Contact { get; }
    public DateTime OccurredOn { get; }

    public ContractorContactAddedEvent(Contractor contractor, ContractorContact contact)
    {
        Contractor = contractor;
        Contact = contact;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a contractor contact is removed
/// </summary>
public class ContractorContactRemovedEvent : IDomainEvent
{
    public Contractor Contractor { get; }
    public ContractorContact Contact { get; }
    public DateTime OccurredOn { get; }

    public ContractorContactRemovedEvent(Contractor contractor, ContractorContact contact)
    {
        Contractor = contractor;
        Contact = contact;
        OccurredOn = DateTime.UtcNow;
    }
}
