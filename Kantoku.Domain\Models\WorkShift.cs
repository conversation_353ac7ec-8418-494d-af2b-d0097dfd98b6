﻿namespace Kantoku.Domain.Models;

public class WorkShift : AuditableEntity
{
    public Guid WorkShiftUid { get; set; }


    public string WorkShiftCode { get; set; } = null!;


    public string WorkShiftName { get; set; } = null!;


    public TimeOnly CheckInTime { get; set; }


    public TimeOnly CheckOutTime { get; set; }


    public IEnumerable<WorkShiftBreakTime> WorkShiftBreakTimes { get; set; } = [];


    public string? Description { get; set; }

    public float TotalRequiredTime { get; set; }
    public Guid OrgUid { get; set; }
    public bool IsDeleted { get; set; } = false;

    public virtual Org Org { get; set; } = null!;
    public virtual ICollection<ProjectWorkShift> ProjectWorkShifts { get; set; } = [];
}

public class WorkShiftBreakTime
{
    public TimeOnly BreakInTime { get; set; }

    public TimeOnly BreakOutTime { get; set; }
}
