using Kantoku.SharedKernel;

namespace Kantoku.Domain.RequestManagement.Enums;

/// <summary>
/// Enumeration representing request types
/// </summary>
public class RequestType : Enumeration
{
    public static readonly RequestType Leave = new(1, nameof(Leave), "Leave Request", true, true);
    public static readonly RequestType Overtime = new(2, nameof(Overtime), "Overtime Request", true, true);
    public static readonly RequestType Equipment = new(3, nameof(Equipment), "Equipment Request", true, true);
    public static readonly RequestType Travel = new(4, nameof(Travel), "Travel Request", true, true);
    public static readonly RequestType Training = new(5, nameof(Training), "Training Request", true, true);
    public static readonly RequestType Expense = new(6, nameof(Expense), "Expense Reimbursement", true, true);
    public static readonly RequestType Other = new(7, nameof(Other), "Other Request", false, false);

    public string DisplayName { get; private set; }
    public bool RequiresApproval { get; private set; }
    public bool RequiresDetails { get; private set; }

    private RequestType(int id, string name, string displayName, bool requiresApproval, bool requiresDetails) : base(id, name)
    {
        DisplayName = displayName;
        RequiresApproval = requiresApproval;
        RequiresDetails = requiresDetails;
    }

    public static IEnumerable<RequestType> GetAll()
    {
        return new[] { Leave, Overtime, Equipment, Travel, Training, Expense, Other };
    }

    public static RequestType FromName(string name)
    {
        var type = GetAll().FirstOrDefault(t => string.Equals(t.Name, name, StringComparison.OrdinalIgnoreCase));
        if (type == null)
            throw new ArgumentException($"Unknown request type: {name}");
        return type;
    }

    public static RequestType FromId(int id)
    {
        var type = GetAll().FirstOrDefault(t => t.Id == id);
        if (type == null)
            throw new ArgumentException($"Unknown request type ID: {id}");
        return type;
    }
}

/// <summary>
/// Enumeration representing request status
/// </summary>
public class RequestStatus : Enumeration
{
    public static readonly RequestStatus Draft = new(1, nameof(Draft), "Draft");
    public static readonly RequestStatus Submitted = new(2, nameof(Submitted), "Submitted");
    public static readonly RequestStatus InReview = new(3, nameof(InReview), "In Review");
    public static readonly RequestStatus Approved = new(4, nameof(Approved), "Approved");
    public static readonly RequestStatus Rejected = new(5, nameof(Rejected), "Rejected");
    public static readonly RequestStatus Completed = new(6, nameof(Completed), "Completed");
    public static readonly RequestStatus Cancelled = new(7, nameof(Cancelled), "Cancelled");

    public string DisplayName { get; private set; }

    private RequestStatus(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<RequestStatus> GetAll()
    {
        return new[] { Draft, Submitted, InReview, Approved, Rejected, Completed, Cancelled };
    }

    public static RequestStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown request status: {name}");
        return status;
    }

    public static RequestStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown request status ID: {id}");
        return status;
    }

    public bool IsActive => this == Draft || this == Submitted || this == InReview || this == Approved;
    public bool IsCompleted => this == Completed || this == Cancelled;
    public bool CanBeModified => this == Draft;
    public bool CanBeWithdrawn => this == Submitted;
}

/// <summary>
/// Enumeration representing request priority
/// </summary>
public class RequestPriority : Enumeration
{
    public static readonly RequestPriority Low = new(1, nameof(Low), "Low");
    public static readonly RequestPriority Normal = new(2, nameof(Normal), "Normal");
    public static readonly RequestPriority High = new(3, nameof(High), "High");
    public static readonly RequestPriority Urgent = new(4, nameof(Urgent), "Urgent");

    public string DisplayName { get; private set; }

    private RequestPriority(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<RequestPriority> GetAll()
    {
        return new[] { Low, Normal, High, Urgent };
    }

    public static RequestPriority FromName(string name)
    {
        var priority = GetAll().FirstOrDefault(p => string.Equals(p.Name, name, StringComparison.OrdinalIgnoreCase));
        if (priority == null)
            throw new ArgumentException($"Unknown request priority: {name}");
        return priority;
    }

    public static RequestPriority FromId(int id)
    {
        var priority = GetAll().FirstOrDefault(p => p.Id == id);
        if (priority == null)
            throw new ArgumentException($"Unknown request priority ID: {id}");
        return priority;
    }

    public bool IsHighPriority => this == High || this == Urgent;
}

/// <summary>
/// Enumeration representing approval status
/// </summary>
public class ApprovalStatus : Enumeration
{
    public static readonly ApprovalStatus Pending = new(1, nameof(Pending), "Pending");
    public static readonly ApprovalStatus Approved = new(2, nameof(Approved), "Approved");
    public static readonly ApprovalStatus Rejected = new(3, nameof(Rejected), "Rejected");

    public string DisplayName { get; private set; }

    private ApprovalStatus(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<ApprovalStatus> GetAll()
    {
        return new[] { Pending, Approved, Rejected };
    }

    public static ApprovalStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown approval status: {name}");
        return status;
    }

    public static ApprovalStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown approval status ID: {id}");
        return status;
    }

    public bool IsCompleted => this == Approved || this == Rejected;
}
