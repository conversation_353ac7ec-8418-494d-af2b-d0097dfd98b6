using Kantoku.SharedKernel;
using Kantoku.Domain.ProjectManagement.ValueObjects;

namespace Kantoku.Domain.ProjectManagement;

/// <summary>
/// Entity representing a daily report for a project
/// </summary>
public class ProjectDailyReport : Entity<Guid>
{
    public Guid ProjectId { get; private set; }
    public Guid OrgId { get; private set; }
    public DateOnly ReportDate { get; private set; }
    public string? Description { get; private set; }
    public WorkloadSummary? EmployeeWorkload { get; private set; }
    public WorkloadSummary? OutSourceWorkload { get; private set; }
    public string? WeatherConditions { get; private set; }
    public string? SafetyNotes { get; private set; }
    public string? ProgressNotes { get; private set; }
    public bool IsApproved { get; private set; } = false;
    public DateTime? ApprovedTime { get; private set; }
    public Guid? ApproverUid { get; private set; }
    public string? ApprovalComments { get; private set; }
    public bool IsDeleted { get; private set; } = false;

    // Private constructor for EF Core
    private ProjectDailyReport() : base() { }

    /// <summary>
    /// Creates a new project daily report
    /// </summary>
    public ProjectDailyReport(
        Guid id,
        Guid projectId,
        Guid orgId,
        DateOnly reportDate,
        string? description = null) : base(id)
    {
        ProjectId = projectId;
        OrgId = orgId;
        ReportDate = reportDate;
        Description = description;
    }

    /// <summary>
    /// Updates the report description
    /// </summary>
    public void UpdateDescription(string? description)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update approved report");

        Description = description;
    }

    /// <summary>
    /// Updates employee workload information
    /// </summary>
    public void UpdateEmployeeWorkload(WorkloadSummary employeeWorkload)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update approved report");

        EmployeeWorkload = employeeWorkload;
    }

    /// <summary>
    /// Updates outsource workload information
    /// </summary>
    public void UpdateOutSourceWorkload(WorkloadSummary outSourceWorkload)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update approved report");

        OutSourceWorkload = outSourceWorkload;
    }

    /// <summary>
    /// Updates weather conditions
    /// </summary>
    public void UpdateWeatherConditions(string? weatherConditions)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update approved report");

        WeatherConditions = weatherConditions;
    }

    /// <summary>
    /// Updates safety notes
    /// </summary>
    public void UpdateSafetyNotes(string? safetyNotes)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update approved report");

        SafetyNotes = safetyNotes;
    }

    /// <summary>
    /// Updates progress notes
    /// </summary>
    public void UpdateProgressNotes(string? progressNotes)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update approved report");

        ProgressNotes = progressNotes;
    }

    /// <summary>
    /// Updates all report content at once
    /// </summary>
    public void UpdateContent(
        string? description = null,
        WorkloadSummary? employeeWorkload = null,
        WorkloadSummary? outSourceWorkload = null,
        string? weatherConditions = null,
        string? safetyNotes = null,
        string? progressNotes = null)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update approved report");

        Description = description ?? Description;
        EmployeeWorkload = employeeWorkload ?? EmployeeWorkload;
        OutSourceWorkload = outSourceWorkload ?? OutSourceWorkload;
        WeatherConditions = weatherConditions ?? WeatherConditions;
        SafetyNotes = safetyNotes ?? SafetyNotes;
        ProgressNotes = progressNotes ?? ProgressNotes;
    }

    /// <summary>
    /// Approves the daily report
    /// </summary>
    public void Approve(Guid approverUid, string? comments = null)
    {
        if (IsApproved)
            throw new InvalidOperationException("Report is already approved");

        IsApproved = true;
        ApproverUid = approverUid;
        ApprovedTime = DateTime.UtcNow;
        ApprovalComments = comments;
    }

    /// <summary>
    /// Revokes approval of the daily report
    /// </summary>
    public void RevokeApproval()
    {
        if (!IsApproved)
            throw new InvalidOperationException("Report is not approved");

        IsApproved = false;
        ApproverUid = null;
        ApprovedTime = null;
        ApprovalComments = null;
    }

    /// <summary>
    /// Soft deletes the report
    /// </summary>
    public void Delete()
    {
        IsDeleted = true;
    }

    /// <summary>
    /// Restores the report
    /// </summary>
    public void Restore()
    {
        IsDeleted = false;
    }

    /// <summary>
    /// Gets the total employee hours
    /// </summary>
    public decimal GetTotalEmployeeHours()
    {
        return EmployeeWorkload?.TotalHours ?? 0;
    }

    /// <summary>
    /// Gets the total outsource hours
    /// </summary>
    public decimal GetTotalOutSourceHours()
    {
        return OutSourceWorkload?.TotalHours ?? 0;
    }

    /// <summary>
    /// Gets the total hours for the day
    /// </summary>
    public decimal GetTotalHours()
    {
        return GetTotalEmployeeHours() + GetTotalOutSourceHours();
    }

    /// <summary>
    /// Checks if the report is for today
    /// </summary>
    public bool IsToday => ReportDate == DateOnly.FromDateTime(DateTime.Today);

    /// <summary>
    /// Checks if the report is overdue (more than 3 days old and not approved)
    /// </summary>
    public bool IsOverdue => !IsApproved && ReportDate < DateOnly.FromDateTime(DateTime.Today.AddDays(-3));
}
