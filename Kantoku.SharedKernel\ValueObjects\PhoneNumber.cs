using System.Text.RegularExpressions;
using Kantoku.SharedKernel.BuildingBlocks;
using Kantoku.SharedKernel.Guards;

namespace Kantoku.SharedKernel.ValueObjects;

/// <summary>
/// Value object representing a phone number with validation
/// </summary>
public sealed class PhoneNumber : ValueObject
{
    private static readonly Regex PhoneRegex = new(
        @"^\+?[1-9]\d{1,14}$",
        RegexOptions.Compiled);

    private static readonly Regex DigitsOnlyRegex = new(
        @"\D",
        RegexOptions.Compiled);

    /// <summary>
    /// Gets the phone number value
    /// </summary>
    public string Value { get; private set; }

    /// <summary>
    /// Gets the country code (if present)
    /// </summary>
    public string? CountryCode { get; private set; }

    /// <summary>
    /// Gets the national number (without country code)
    /// </summary>
    public string NationalNumber { get; private set; } = null!;

    /// <summary>
    /// Initializes a new instance of the PhoneNumber class
    /// </summary>
    /// <param name="value">The phone number</param>
    /// <exception cref="ArgumentException">Thrown when the phone number is invalid</exception>
    private PhoneNumber(string value)
    {
        var cleanedValue = Guard.NotNullOrWhiteSpace(value).Trim();

        // Remove all non-digit characters except + at the beginning
        var normalized = NormalizePhoneNumber(cleanedValue);

        if (!IsValid(normalized))
            throw new ArgumentException($"Invalid phone number: {value}", nameof(value));

        Value = normalized;
        ParseComponents();
    }

    /// <summary>
    /// Creates a new PhoneNumber instance
    /// </summary>
    /// <param name="value">The phone number</param>
    /// <returns>A new PhoneNumber instance</returns>
    /// <exception cref="ArgumentException">Thrown when the phone number is invalid</exception>
    public static PhoneNumber Create(string value) => new(value);

    /// <summary>
    /// Tries to create a new PhoneNumber instance
    /// </summary>
    /// <param name="value">The phone number</param>
    /// <param name="phoneNumber">The created PhoneNumber instance if successful</param>
    /// <returns>True if the phone number was created successfully; otherwise, false</returns>
    public static bool TryCreate(string? value, out PhoneNumber? phoneNumber)
    {
        phoneNumber = null;

        if (string.IsNullOrWhiteSpace(value))
            return false;

        try
        {
            phoneNumber = new PhoneNumber(value);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validates a phone number
    /// </summary>
    /// <param name="phoneNumber">The phone number to validate</param>
    /// <returns>True if the phone number is valid; otherwise, false</returns>
    public static bool IsValid(string? phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        var normalized = NormalizePhoneNumber(phoneNumber);

        // Check basic format
        if (!PhoneRegex.IsMatch(normalized))
            return false;

        // Additional length checks
        var digitsOnly = DigitsOnlyRegex.Replace(normalized, "");

        // International format: 7-15 digits (ITU-T E.164)
        // National format: 7-15 digits
        return digitsOnly.Length >= 7 && digitsOnly.Length <= 15;
    }

    /// <summary>
    /// Formats the phone number for display
    /// </summary>
    /// <param name="format">The format type</param>
    /// <returns>The formatted phone number</returns>
    public string Format(PhoneNumberFormat format = PhoneNumberFormat.International)
    {
        return format switch
        {
            PhoneNumberFormat.International => Value.StartsWith('+') ? Value : $"+{Value}",
            PhoneNumberFormat.National => NationalNumber,
            PhoneNumberFormat.E164 => Value.StartsWith('+') ? Value : $"+{Value}",
            PhoneNumberFormat.Raw => Value,
            _ => Value
        };
    }

    /// <summary>
    /// Returns the string representation of the phone number
    /// </summary>
    /// <returns>The phone number as a string</returns>
    public override string ToString() => Value;

    /// <summary>
    /// Gets the equality components for value object comparison
    /// </summary>
    /// <returns>The equality components</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Value;
    }

    /// <summary>
    /// Implicitly converts a PhoneNumber to a string
    /// </summary>
    /// <param name="phoneNumber">The PhoneNumber instance</param>
    public static implicit operator string(PhoneNumber phoneNumber) => phoneNumber.Value;

    /// <summary>
    /// Explicitly converts a string to a PhoneNumber
    /// </summary>
    /// <param name="value">The phone number string</param>
    public static explicit operator PhoneNumber(string value) => Create(value);

    /// <summary>
    /// Normalizes a phone number by removing formatting characters
    /// </summary>
    /// <param name="phoneNumber">The phone number to normalize</param>
    /// <returns>The normalized phone number</returns>
    private static string NormalizePhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return string.Empty;

        // Remove all characters except digits and + at the beginning
        var result = phoneNumber.Trim();

        // Handle international prefix
        var hasInternationalPrefix = result.StartsWith('+');

        // Remove all non-digit characters
        var digitsOnly = DigitsOnlyRegex.Replace(result, "");

        // Add back the + if it was there originally
        return hasInternationalPrefix ? $"+{digitsOnly}" : digitsOnly;
    }

    /// <summary>
    /// Parses the phone number components
    /// </summary>
    private void ParseComponents()
    {
        if (Value.StartsWith('+'))
        {
            // International format
            var digitsOnly = Value[1..]; // Remove the +

            // Simple country code detection (this could be more sophisticated)
            if (digitsOnly.Length >= 10)
            {
                // Assume 1-3 digit country code
                for (int i = 1; i <= 3 && i < digitsOnly.Length; i++)
                {
                    var potentialCountryCode = digitsOnly[..i];
                    var potentialNationalNumber = digitsOnly[i..];

                    if (potentialNationalNumber.Length >= 7)
                    {
                        CountryCode = potentialCountryCode;
                        NationalNumber = potentialNationalNumber;
                        break;
                    }
                }
            }

            // Fallback
            CountryCode ??= digitsOnly.Length > 10 ? digitsOnly[..1] : null;
            NationalNumber = CountryCode != null ? digitsOnly[CountryCode.Length..] : digitsOnly;
        }
        else
        {
            // National format
            CountryCode = null;
            NationalNumber = Value;
        }
    }
}

/// <summary>
/// Enumeration of phone number format types
/// </summary>
public enum PhoneNumberFormat
{
    /// <summary>
    /// International format with country code (e.g., +1234567890)
    /// </summary>
    International,

    /// <summary>
    /// National format without country code (e.g., 1234567890)
    /// </summary>
    National,

    /// <summary>
    /// E.164 format (e.g., +1234567890)
    /// </summary>
    E164,

    /// <summary>
    /// Raw format as stored
    /// </summary>
    Raw
}
