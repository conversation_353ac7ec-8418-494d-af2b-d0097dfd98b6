using System;

namespace Kantoku.Application.Exceptions
{
    /// <summary>
    /// Exception that is thrown when a duplicate entity is found
    /// </summary>
    public class DuplicateException : Exception
    {
        public DuplicateException()
            : base()
        {
        }

        public DuplicateException(string message)
            : base(message)
        {
        }

        public DuplicateException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public DuplicateException(string name, object key)
            : base($"Entity '{name}' with key '{key}' already exists.")
        {
        }
    }
} 