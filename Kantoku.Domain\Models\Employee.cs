﻿namespace Kantoku.Domain.Models;

public class Employee : AuditableEntity
{
    public Guid EmployeeUid { get; set; }
    public Guid AccountUid { get; set; }
    public Guid OrgUid { get; set; }


    public Guid? StructureUid { get; set; }


    public Guid? PositionUid { get; set; }


    public Guid? RankingUid { get; set; }


    public IEnumerable<EmployeeMail>? EmployeeMails { get; set; }


    public IEnumerable<EmployeePhone>? EmployeePhones { get; set; }


    public string? EmployeeAddress { get; set; }


    public string WorkingStatus { get; set; } = null!;

    public string EmployeeName { get; set; } = null!;

    public string EmployeeCode { get; set; } = null!;


    public bool EmployeeType { get; set; }


    public int SalaryInMonth { get; set; }


    public float StandardWorkingHours { get; set; }


    public DateTime? WorkingFromDate { get; set; }


    public DateTime? WorkingToDate { get; set; }


    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;

    public bool IsOrgOwner { get; set; } = false;

    public bool IsOrgAdmin { get; set; } = false;

    public bool HasApprovalAuthority { get; set; } = false;

    public bool IsHidden { get; set; } = false;

    public virtual Org Org { get; set; } = null!;

    public virtual Account Account { get; set; } = null!;

    public virtual Structure? Structure { get; set; }

    public virtual Position? Position { get; set; }

    public virtual Ranking? Ranking { get; set; }

    public virtual Status Status { get; set; } = null!;

    public virtual ICollection<Request> RequestApprover1s { get; set; } = [];

    public virtual ICollection<Request> RequestApprover2s { get; set; } = [];

    public virtual ICollection<Request> RequestAuthors { get; set; } = [];

    public virtual ICollection<EmpContract> EmpContracts { get; set; } = [];

    public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = [];

    public virtual ICollection<EmployeeShift> EmployeeShiftApprovers { get; set; } = [];

    public virtual ICollection<MonthlyReport> MonthlyReportsAuthor { get; set; } = [];

    public virtual ICollection<MonthlyReport> MonthlyReportsApprover { get; set; } = [];

    public virtual ICollection<EmployeeLeave> EmployeeLeaves { get; set; } = [];

    public virtual ICollection<EmployeeRole> EmployeeRoles { get; set; } = [];

    public virtual ICollection<ProjectManager> ProjectManagers { get; set; } = [];

    public virtual ICollection<ProjectDailyReport> ProjectDailyReports { get; set; } = [];

    public virtual ICollection<EmployeeNotification> EmployeeNotifications { get; set; } = [];

    public virtual ICollection<DeviceToken> DeviceTokens { get; set; } = [];

    public virtual ICollection<EmployeeCost> EmployeeCosts { get; set; } = [];

    public override bool Equals(object? obj)
    {
        return obj is Employee employee && EmployeeUid == employee.EmployeeUid;
    }

    public override int GetHashCode()
    {
        return EmployeeUid.GetHashCode();
    }
}

public class EmployeeMail
{
    public string? Email { get; set; }

    public bool IsPrimary { get; set; }
}

public class EmployeePhone
{
    public string? Phone { get; set; }

    public bool IsPrimary { get; set; }
}
