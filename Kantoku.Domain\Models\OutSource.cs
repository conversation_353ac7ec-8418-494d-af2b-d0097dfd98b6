namespace Kantoku.Domain.Models;

public class OutSource : AuditableEntity
{
    public Guid OutSourceUid { get; set; }


    public string OutSourceCode { get; set; } = null!;


    public string OutSourceName { get; set; } = null!;


    public string? Description { get; set; }


    public string? Address { get; set; }


    public string? PhoneNumber { get; set; }


    public string? Email { get; set; }


    public string? CorporateNumber { get; set; }


    public ContactPerson? ContactPerson { get; set; }


    public ICollection<string>? Expertise { get; set; }

    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }

    public string? LogoUrl { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<OutSourceShift> OutSourceShifts { get; set; } = [];

    public virtual ICollection<OutSourcePrice> OutSourcePrices { get; set; } = [];
}
