using System.Reflection;
using FluentValidation;
using Kantoku.Application.Behaviours;
using MediatR;
using Microsoft.Extensions.DependencyInjection;

namespace Kantoku.Application
{
    /// <summary>
    /// Contains extension methods for configuring Application layer services
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// Adds application layer services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            var assembly = Assembly.GetExecutingAssembly();
            
            services.AddMediatR(cfg => 
            {
                cfg.RegisterServicesFromAssembly(assembly);
                
                // Add behaviors in the order they should be executed
                cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(LoggingBehaviour<,>));
                cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(PerformanceBehaviour<,>));
                cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehaviour<,>));
                cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(TransactionBehaviour<,>));
            });

            // Add validators
            services.AddValidatorsFromAssembly(assembly);

            return services;
        }
    }
} 