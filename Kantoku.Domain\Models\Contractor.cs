﻿namespace Kantoku.Domain.Models;

public class Contractor : AuditableEntity
{
    public Guid ContractorUid { get; set; }


    public string ContractorCode { get; set; } = null!;


    public string ContractorName { get; set; } = null!;


    public string? ContractorSubName { get; set; }


    public string? CorporateNumber { get; set; }


    public string? Address { get; set; }


    public string? PhoneNumber { get; set; }


    public string? Email { get; set; }


    public ContactPerson? ContactPerson { get; set; }


    public string? Description { get; set; }

    public bool IsDeleted { get; set; }

    public Guid OrgUid { get; set; }

    public string? LogoUrl { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<ConstructionPaymentRequest> ConstructionPaymentRequests { get; set; } = [];

    public virtual ICollection<Project> Projects { get; set; } = [];
}