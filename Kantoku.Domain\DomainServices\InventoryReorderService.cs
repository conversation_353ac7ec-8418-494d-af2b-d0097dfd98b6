using Kantoku.Domain.InventoryManagement;
using Kantoku.Domain.InventoryManagement.Enums;

namespace Kantoku.Domain.DomainServices;

/// <summary>
/// Domain service for inventory reorder management
/// </summary>
public class InventoryReorderService
{
    /// <summary>
    /// Analyzes inventory and generates reorder recommendations
    /// </summary>
    public IEnumerable<ReorderRecommendation> AnalyzeInventoryForReorder(
        IEnumerable<Item> items,
        IEnumerable<ItemStock> recentMovements,
        int analysisWindowDays = 30)
    {
        var recommendations = new List<ReorderRecommendation>();
        var cutoffDate = DateTime.Today.AddDays(-analysisWindowDays);

        foreach (var item in items.Where(i => i.Status == ItemStatus.Active))
        {
            var recommendation = AnalyzeItemForReorder(item, recentMovements, cutoffDate);
            if (recommendation != null)
            {
                recommendations.Add(recommendation);
            }
        }

        return recommendations.OrderByDescending(r => r.Priority);
    }

    /// <summary>
    /// Calculates optimal reorder quantity based on usage patterns
    /// </summary>
    public decimal CalculateOptimalReorderQuantity(
        Item item,
        IEnumerable<ItemStock> movements,
        int forecastDays = 60)
    {
        if (item.ReorderQuantity > 0)
            return item.ReorderQuantity;

        // Calculate average daily usage
        var outboundMovements = movements
            .Where(m => m.ItemId == item.Id && 
                       (m.MovementType == StockMovementType.Out || 
                        m.MovementType == StockMovementType.AdjustmentOut))
            .OrderByDescending(m => m.MovementDate)
            .Take(30); // Last 30 movements

        if (!outboundMovements.Any())
            return item.MinimumStock * 2; // Default to 2x minimum stock

        var totalUsage = outboundMovements.Sum(m => m.Quantity);
        var daysCovered = outboundMovements.Count();
        var averageDailyUsage = totalUsage / daysCovered;

        // Economic Order Quantity (simplified)
        // EOQ = sqrt(2 * demand * ordering_cost / holding_cost)
        // Using simplified assumptions for ordering and holding costs
        var annualDemand = averageDailyUsage * 365;
        var orderingCost = 5000; // 5000 yen per order (assumption)
        var holdingCostRate = 0.20m; // 20% of item value per year
        var itemValue = GetAverageItemValue(item);
        var holdingCost = itemValue * holdingCostRate;

        if (holdingCost > 0)
        {
            var eoq = Math.Sqrt((double)(2 * annualDemand * orderingCost / holdingCost));
            return Math.Max((decimal)eoq, item.MinimumStock);
        }

        // Fallback: forecast usage for specified days plus safety stock
        return (averageDailyUsage * forecastDays) + item.MinimumStock;
    }

    /// <summary>
    /// Determines reorder priority based on criticality and stock levels
    /// </summary>
    public ReorderPriority DetermineReorderPriority(Item item, decimal daysOfStockRemaining)
    {
        // Critical items (safety stock, essential materials)
        if (item.ItemName.Contains("Safety") || item.ItemName.Contains("Essential"))
        {
            if (daysOfStockRemaining <= 3)
                return ReorderPriority.Critical;
            if (daysOfStockRemaining <= 7)
                return ReorderPriority.High;
        }

        // Regular items
        if (daysOfStockRemaining <= 1)
            return ReorderPriority.Critical;
        if (daysOfStockRemaining <= 3)
            return ReorderPriority.High;
        if (daysOfStockRemaining <= 7)
            return ReorderPriority.Medium;

        return ReorderPriority.Low;
    }

    /// <summary>
    /// Calculates lead time requirements for reordering
    /// </summary>
    public int CalculateLeadTimeRequirement(Item item, IEnumerable<ItemStock> movements)
    {
        // Default lead time if not specified
        var defaultLeadTime = 7; // 7 days

        // Calculate average daily usage
        var outboundMovements = movements
            .Where(m => m.ItemId == item.Id && 
                       (m.MovementType == StockMovementType.Out || 
                        m.MovementType == StockMovementType.AdjustmentOut))
            .OrderByDescending(m => m.MovementDate)
            .Take(30);

        if (!outboundMovements.Any())
            return defaultLeadTime;

        var averageDailyUsage = outboundMovements.Average(m => m.Quantity);
        var daysOfStockRemaining = item.CurrentStock / Math.Max(averageDailyUsage, 0.1m);

        // Add buffer time based on usage variability
        var usageVariability = CalculateUsageVariability(outboundMovements);
        var bufferDays = usageVariability > 0.5m ? 3 : 1; // High variability needs more buffer

        return defaultLeadTime + bufferDays;
    }

    /// <summary>
    /// Generates automatic reorder suggestions for critical items
    /// </summary>
    public IEnumerable<AutoReorderSuggestion> GenerateAutoReorderSuggestions(
        IEnumerable<Item> items,
        IEnumerable<ItemStock> movements)
    {
        var suggestions = new List<AutoReorderSuggestion>();

        foreach (var item in items.Where(i => i.Status == ItemStatus.Active && i.AutoReorder))
        {
            var recommendation = AnalyzeItemForReorder(item, movements, DateTime.Today.AddDays(-30));
            if (recommendation?.Priority >= ReorderPriority.High)
            {
                suggestions.Add(new AutoReorderSuggestion
                {
                    ItemId = item.Id,
                    ItemName = item.ItemName,
                    CurrentStock = item.CurrentStock,
                    RecommendedQuantity = recommendation.RecommendedQuantity,
                    EstimatedCost = recommendation.RecommendedQuantity * GetAverageItemValue(item),
                    Priority = recommendation.Priority,
                    Reason = recommendation.Reason
                });
            }
        }

        return suggestions.OrderByDescending(s => s.Priority);
    }

    // Private helper methods
    private ReorderRecommendation? AnalyzeItemForReorder(
        Item item, 
        IEnumerable<ItemStock> movements, 
        DateTime cutoffDate)
    {
        // Skip if item doesn't need reordering
        if (item.CurrentStock > item.ReorderPoint && item.ReorderPoint > 0)
            return null;

        var itemMovements = movements.Where(m => m.ItemId == item.Id && m.MovementDate >= cutoffDate);
        var outboundMovements = itemMovements
            .Where(m => m.MovementType == StockMovementType.Out || 
                       m.MovementType == StockMovementType.AdjustmentOut);

        if (!outboundMovements.Any())
        {
            // No recent usage, but stock is low
            if (item.CurrentStock <= item.MinimumStock)
            {
                return new ReorderRecommendation
                {
                    ItemId = item.Id,
                    ItemName = item.ItemName,
                    CurrentStock = item.CurrentStock,
                    ReorderPoint = item.ReorderPoint,
                    RecommendedQuantity = Math.Max(item.ReorderQuantity, item.MinimumStock * 2),
                    Priority = ReorderPriority.Medium,
                    Reason = "Stock below minimum level with no recent usage"
                };
            }
            return null;
        }

        var averageDailyUsage = outboundMovements.Average(m => m.Quantity);
        var daysOfStockRemaining = averageDailyUsage > 0 ? item.CurrentStock / averageDailyUsage : 999;

        var priority = DetermineReorderPriority(item, daysOfStockRemaining);
        var recommendedQuantity = CalculateOptimalReorderQuantity(item, movements);

        return new ReorderRecommendation
        {
            ItemId = item.Id,
            ItemName = item.ItemName,
            CurrentStock = item.CurrentStock,
            ReorderPoint = item.ReorderPoint,
            RecommendedQuantity = recommendedQuantity,
            Priority = priority,
            DaysOfStockRemaining = (int)Math.Ceiling(daysOfStockRemaining),
            AverageDailyUsage = averageDailyUsage,
            Reason = $"Stock will run out in {Math.Ceiling(daysOfStockRemaining)} days at current usage rate"
        };
    }

    private decimal GetAverageItemValue(Item item)
    {
        // This would typically come from recent price history
        // For now, return a default value or the last known price
        return 1000; // Default 1000 yen per unit
    }

    private decimal CalculateUsageVariability(IEnumerable<ItemStock> movements)
    {
        var quantities = movements.Select(m => m.Quantity).ToList();
        if (quantities.Count < 2)
            return 0;

        var average = quantities.Average();
        var variance = quantities.Sum(q => Math.Pow((double)(q - average), 2)) / quantities.Count;
        var standardDeviation = Math.Sqrt(variance);

        return average > 0 ? (decimal)(standardDeviation / (double)average) : 0;
    }
}

/// <summary>
/// Reorder recommendation for an item
/// </summary>
public class ReorderRecommendation
{
    public Guid ItemId { get; set; }
    public string ItemName { get; set; } = null!;
    public decimal CurrentStock { get; set; }
    public decimal ReorderPoint { get; set; }
    public decimal RecommendedQuantity { get; set; }
    public ReorderPriority Priority { get; set; }
    public int DaysOfStockRemaining { get; set; }
    public decimal AverageDailyUsage { get; set; }
    public string Reason { get; set; } = null!;
}

/// <summary>
/// Auto reorder suggestion
/// </summary>
public class AutoReorderSuggestion
{
    public Guid ItemId { get; set; }
    public string ItemName { get; set; } = null!;
    public decimal CurrentStock { get; set; }
    public decimal RecommendedQuantity { get; set; }
    public decimal EstimatedCost { get; set; }
    public ReorderPriority Priority { get; set; }
    public string Reason { get; set; } = null!;
}

/// <summary>
/// Reorder priority levels
/// </summary>
public enum ReorderPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}
