namespace Kantoku.Domain.Common.BuildingBlocks;


public abstract class Entity : HasDomainEventsBase
{
    public Guid Id { get; protected set; }
}

public abstract class Entity<TId> : HasDomainEventsBase where TId : class, IEquatable<TId>
{
    public TId Id { get; protected set; } = null!;

    // Parameterless constructor for EF Core
    protected Entity() { }

    protected Entity(TId id)
    {
        Id = id ?? throw new ArgumentNullException(nameof(id));
    }

    public bool IsTransient() => Id == null;

    public override bool Equals(object? obj)
    {
        if (obj == null || obj is not Entity<TId>)
            return false;

        if (ReferenceEquals(this, obj))
            return true;

        if (GetType() != obj.GetType())
            return false;

        var item = (Entity<TId>)obj;

        return item.Id.Equals(Id);
    }

    public override int GetHashCode()
    {
        return Id?.GetHashCode() ?? 0;
    }

    public static bool operator ==(Entity<TId>? a, Entity<TId>? b)
    {
        if (a is null && b is null)
            return true;

        if (a is null || b is null)
            return false;

        return a.Equals(b);
    }

    public static bool operator !=(Entity<TId>? a, Entity<TId>? b)
    {
        return !(a == b);
    }
}
