namespace Kantoku.Domain.Common.BuildingBlocks;


public abstract class Entity : HasDomainEventsBase
{
    public Guid Id { get; protected set; }
}

public abstract class Entity<TId> : HasDomainEventsBase where TId : IEquatable<TId>
{
    public TId Id { get; protected set; }

    protected Entity(TId id)
    {
        if (object.Equals(id, default(TId)))
        {
            throw new ArgumentException("The ID cannot be the default value.", nameof(id));
        }
        Id = id;
    }

    public bool IsTransient() => EqualityComparer<TId>.Default.Equals(Id, default);

    public override bool Equals(object? obj)
    {
        if (obj == null || obj is not Entity<TId>)
            return false;

        if (ReferenceEquals(this, obj))
            return true;

        if (GetType() != obj.GetType())
            return false;

        var item = (Entity<TId>)obj;

        return EqualityComparer<TId>.Default.Equals(item.Id, Id);
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    public static bool operator ==(Entity<TId>? a, Entity<TId>? b)
    {
        if (a is null && b is null)
            return true;

        if (a is null || b is null)
            return false;

        return a.Equals(b);
    }

    public static bool operator !=(Entity<TId>? a, Entity<TId>? b)
    {
        return !(a == b);
    }
}
