using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class NotificationQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedNotificationTargets { get; set; } = false;
}

public interface INotificationQueryable
{
    IQueryable<Notification> GetNotificationQuery(
        NotificationQueryableOptions options
    );

    IQueryable<Notification> GetNotificationQueryIncluded(
        NotificationQueryableOptions options,
        IQueryable<Notification>? query = null
    );

    IQueryable<Notification> GetNotificationQueryFiltered(
        OrgNotificationFilter filter,
        NotificationQueryableOptions options
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class NotificationQueryable(PostgreDbContext context) :
    BaseQueryable<Notification>(context), INotificationQueryable
{
    public IQueryable<Notification> GetNotificationQuery(
        NotificationQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Notification> GetNotificationQueryIncluded(
        NotificationQueryableOptions options,
        IQueryable<Notification>? query = null
    )
    {
        query ??= GetNotificationQuery(options);
        if (options.IncludedNotificationTargets)
        {
            query = query.Include(p => p.NotificationTargets);
        }
        return query;
    }

    public IQueryable<Notification> GetNotificationQueryFiltered(
        OrgNotificationFilter filter,
        NotificationQueryableOptions options
    )
    {
        var query = GetNotificationQueryIncluded(options);

        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (!string.IsNullOrEmpty(filter.TitleKeyword))
        {
            query = query.Where(p => p.Title.Contains(filter.TitleKeyword));
        }
        if (!string.IsNullOrEmpty(filter.BodyKeyword))
        {
            query = query.Where(p => p.Body.Contains(filter.BodyKeyword));
        }
        if (filter.Type != null)
        {
            query = query.Where(p => p.NotificationType == filter.Type);
        }
        if (filter.TargetType != null)
        {
            query = query.Where(p => p.NotificationType == filter.TargetType);
        }

        return query;
    }
}
