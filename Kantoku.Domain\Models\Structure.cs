﻿namespace Kantoku.Domain.Models;

public class Structure : AuditableEntity
{
    public Guid StructureUid { get; set; }
    public Guid? StructureParentUid { get; set; }


    public string StructureCode { get; set; } = null!;


    public string StructureName { get; set; } = null!;


    public string? Description { get; set; }

    public bool IsHidden { get; set; }
    public bool IsDefault { get; set; }
    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Structure Parent { get; set; } = null!;

    public virtual ICollection<Structure> Children { get; set; } = [];

    public virtual ICollection<Employee> Employees { get; set; } = [];

    public virtual ICollection<Role> Roles { get; set; } = [];
}
