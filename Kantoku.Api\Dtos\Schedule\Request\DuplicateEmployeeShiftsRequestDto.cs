using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class DuplicateScheduledEmployeeShiftsRequestDto
{
    /// <summary>
    /// Target project id (*)   
    /// </summary>
    [Required]
    [JsonPropertyName("projectId")]
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// Working date from (copy to this range of date, start from this date) (*)
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [DateTimeValidator(typeof(DateOnly))]
    [JsonPropertyName("workingDateFrom")]
    public required DateOnly WorkingDateFrom { get; set; }

    /// <summary>
    /// Working date to (copy to this range of date, end at this date) (*)
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [DateTimeValidator(typeof(DateOnly))]
    [JsonPropertyName("workingDateTo")]
    public required DateOnly WorkingDateTo { get; set; }
}