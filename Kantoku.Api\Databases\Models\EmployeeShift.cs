﻿using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class EmployeeShift : AuditableEntity
{
    public Guid EmployeeShiftUid { get; set; }

    public Guid? ProjectScheduleUid { get; set; }

    public Guid? MonthlyReportUid { get; set; }

    public Guid EmployeeUid { get; set; }

    public Guid ProjectUid { get; set; }

    [AuditProperty]
    public string? WorkingLocation { get; set; }

    [AuditProperty]
    public DateTime? ScheduledStartTime { get; set; }

    [AuditProperty]
    public DateTime? CheckInTime { get; set; }

    public int? CheckInTimeLastModifier { get; set; } // 0: author, 1: manager, 2: system 3: job

    [AuditProperty]
    public string? CheckInLocation { get; set; }

    [AuditProperty]
    public DateTime? ScheduledEndTime { get; set; }

    [AuditProperty]
    public DateTime? CheckOutTime { get; set; }

    public int? CheckOutTimeLastModifier { get; set; } // 0: author, 1: manager, 2: system 3: job

    public DateTime? AutoCheckOutTime { get; set; }

    [AuditProperty]
    public string? CheckOutLocation { get; set; }

    [AuditProperty]
    public ICollection<EmployeeShiftBreakTime>? EmployeeShiftBreakTimes { get; set; } = [];

    public int? BreakTimeLastModifier { get; set; } // 0: author, 1: manager, 2: system 3: job

    public float TotalScheduledWorkTime { get; set; }

    public float TotalWorkTime { get; set; }

    public float TotalBreakTime { get; set; }

    public float TotalOverTime { get; set; }

    public bool IsRequested { get; set; }

    public bool? IsApproved { get; set; }

    public Guid? ApprovedBy { get; set; }

    public DateTime? ApprovedTime { get; set; }

    public bool IsDeleted { get; set; }

    public string? Description { get; set; }

    public string? AssignedRole { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Employee? Approver { get; set; }

    public virtual Project Project { get; set; } = null!;

    public virtual ProjectSchedule? ProjectSchedule { get; set; }

    public virtual MonthlyReport? MonthlyReport { get; set; }
}

public class EmployeeShiftBreakTime
{
    public DateTime? BreakInTime { get; set; }

    public DateTime? BreakOutTime { get; set; }
}

public enum LastModifier
{
    Job = -1,
    System = 0,
    Author = 1,
    Manager = 2,
}
