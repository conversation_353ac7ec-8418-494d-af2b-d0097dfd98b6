using Kantoku.Domain.ContractorManagement.Enums;

namespace Kantoku.Domain.ContractorManagement;

/// <summary>
/// Repository interface for Contractor aggregate
/// </summary>
public interface IContractorRepository
{
    /// <summary>
    /// Gets a contractor by ID
    /// </summary>
    Task<Contractor?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a contractor by code
    /// </summary>
    Task<Contractor?> GetByCodeAsync(string contractorCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets contractors by organization ID
    /// </summary>
    Task<IEnumerable<Contractor>> GetByOrganizationIdAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets contractors by status
    /// </summary>
    Task<IEnumerable<Contractor>> GetByStatusAsync(ContractorStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets preferred contractors for an organization
    /// </summary>
    Task<IEnumerable<Contractor>> GetPreferredContractorsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets qualified contractors for an organization
    /// </summary>
    Task<IEnumerable<Contractor>> GetQualifiedContractorsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets contractors with expiring insurance
    /// </summary>
    Task<IEnumerable<Contractor>> GetContractorsWithExpiringInsuranceAsync(
        Guid orgId, 
        int daysUntilExpiry = 30, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets contractors with expiring licenses
    /// </summary>
    Task<IEnumerable<Contractor>> GetContractorsWithExpiringLicensesAsync(
        Guid orgId, 
        int daysUntilExpiry = 30, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets contractors by specialization
    /// </summary>
    Task<IEnumerable<Contractor>> GetBySpecializationAsync(
        Guid orgId, 
        string specialization, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets contractors by geographic area
    /// </summary>
    Task<IEnumerable<Contractor>> GetByGeographicAreaAsync(
        Guid orgId, 
        string area, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets contractors that can handle a specific project value
    /// </summary>
    Task<IEnumerable<Contractor>> GetByProjectValueCapacityAsync(
        Guid orgId, 
        decimal projectValue, 
        string currency = "JPY", 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches contractors by name or code
    /// </summary>
    Task<IEnumerable<Contractor>> SearchAsync(
        Guid orgId, 
        string searchTerm, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets contractors with pagination
    /// </summary>
    Task<(IEnumerable<Contractor> Contractors, int TotalCount)> GetPagedAsync(
        Guid orgId,
        int pageNumber = 1,
        int pageSize = 20,
        ContractorStatus? status = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a contractor code exists
    /// </summary>
    Task<bool> ExistsAsync(string contractorCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a contractor code exists for a different contractor
    /// </summary>
    Task<bool> ExistsAsync(string contractorCode, Guid excludeContractorId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new contractor
    /// </summary>
    Task AddAsync(Contractor contractor, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing contractor
    /// </summary>
    Task UpdateAsync(Contractor contractor, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes a contractor
    /// </summary>
    Task RemoveAsync(Contractor contractor, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets contractor statistics for an organization
    /// </summary>
    Task<ContractorStatistics> GetStatisticsAsync(Guid orgId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistics about contractors for an organization
/// </summary>
public class ContractorStatistics
{
    public int TotalContractors { get; set; }
    public int ActiveContractors { get; set; }
    public int QualifiedContractors { get; set; }
    public int PreferredContractors { get; set; }
    public int BlacklistedContractors { get; set; }
    public int ContractorsWithExpiringInsurance { get; set; }
    public int ContractorsWithExpiringLicenses { get; set; }
    public Dictionary<string, int> ContractorsBySpecialization { get; set; } = new();
    public Dictionary<string, int> ContractorsByGeographicArea { get; set; } = new();
}
