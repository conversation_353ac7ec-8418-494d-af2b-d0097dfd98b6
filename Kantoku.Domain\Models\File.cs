﻿namespace Kantoku.Domain.Models;

public partial class File : AuditableEntity
{
    public Guid FileUid { get; set; }

    public Guid OrgUid { get; set; }

    public string FileName { get; set; } = null!;

    public string FileType { get; set; } = null!;

    public string FileUrl { get; set; } = null!;

    public long FileSize { get; set; }

    public Dictionary<string, string?>? FileMetadata { get; set; }

    public bool IsDeleted { get; set; } = false;
}
