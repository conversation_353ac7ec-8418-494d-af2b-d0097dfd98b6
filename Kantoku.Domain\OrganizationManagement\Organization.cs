using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.OrganizationManagement.ValueObjects;
using Kantoku.Domain.OrganizationManagement.Events;
using Kantoku.SharedKernel;
using Kantoku.SharedKernel.ValueObjects;

namespace Kantoku.Domain.OrganizationManagement;

/// <summary>
/// Organization aggregate root representing a company or organization in the system
/// </summary>
public class Organization : FullAuditedEntity<OrganizationId>
{
    private readonly List<Structure> _structures = new();
    private readonly List<Position> _positions = new();
    private readonly List<Ranking> _rankings = new();
    private readonly List<WorkShift> _workShifts = new();

    public string OrgCode { get; private set; } = null!;
    public string OrgName { get; private set; } = null!;
    public string? OrgSubName { get; private set; }
    public string? Description { get; private set; }
    public string? LogoUrl { get; private set; }

    public OrganizationAddress? Address { get; private set; }
    public ContactInformation? ContactInfo { get; private set; }
    public LegalInformation? LegalInfo { get; private set; }

    public string? TimeZone { get; private set; }
    public bool EnableAutoCheckOut { get; private set; } = false;
    public string EmployeeRankingDefinitionType { get; private set; } = "DIRECT"; // DIRECT, INDIRECT

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<Structure> Structures => _structures.AsReadOnly();
    public IReadOnlyCollection<Position> Positions => _positions.AsReadOnly();
    public IReadOnlyCollection<Ranking> Rankings => _rankings.AsReadOnly();
    public IReadOnlyCollection<WorkShift> WorkShifts => _workShifts.AsReadOnly();

    // Private constructor for EF Core
    private Organization() : base() { }

    /// <summary>
    /// Creates a new organization
    /// </summary>
    public Organization(
        OrganizationId id,
        string orgCode,
        string orgName,
        string? orgSubName = null,
        string? description = null) : base(id)
    {
        SetOrgCode(orgCode);
        SetOrgName(orgName);
        OrgSubName = orgSubName;
        Description = description;

        AddDomainEvent(new OrganizationCreatedEvent(this));
    }

    /// <summary>
    /// Updates basic organization information
    /// </summary>
    public void UpdateBasicInfo(
        string orgName,
        string? orgSubName = null,
        string? description = null,
        string? logoUrl = null)
    {
        var oldName = OrgName;
        SetOrgName(orgName);
        OrgSubName = orgSubName;
        Description = description;
        LogoUrl = logoUrl;

        if (oldName != orgName)
        {
            AddDomainEvent(new OrganizationNameChangedEvent(this, oldName, orgName));
        }
    }

    /// <summary>
    /// Updates organization address
    /// </summary>
    public void UpdateAddress(OrganizationAddress address)
    {
        Address = address ?? throw new ArgumentNullException(nameof(address));
        AddDomainEvent(new OrganizationAddressUpdatedEvent(this));
    }

    /// <summary>
    /// Updates contact information
    /// </summary>
    public void UpdateContactInfo(ContactInformation contactInfo)
    {
        ContactInfo = contactInfo ?? throw new ArgumentNullException(nameof(contactInfo));
        AddDomainEvent(new OrganizationContactInfoUpdatedEvent(this));
    }

    /// <summary>
    /// Updates legal information
    /// </summary>
    public void UpdateLegalInfo(LegalInformation legalInfo)
    {
        LegalInfo = legalInfo ?? throw new ArgumentNullException(nameof(legalInfo));
    }

    /// <summary>
    /// Updates organization settings
    /// </summary>
    public void UpdateSettings(
        string? timeZone = null,
        bool? enableAutoCheckOut = null,
        string? employeeRankingDefinitionType = null)
    {
        TimeZone = timeZone ?? TimeZone;
        EnableAutoCheckOut = enableAutoCheckOut ?? EnableAutoCheckOut;

        if (employeeRankingDefinitionType != null)
        {
            SetEmployeeRankingDefinitionType(employeeRankingDefinitionType);
        }
    }

    /// <summary>
    /// Adds a new structure to the organization
    /// </summary>
    public void AddStructure(Structure structure)
    {
        if (structure == null)
            throw new ArgumentNullException(nameof(structure));

        if (_structures.Any(s => s.StructureCode == structure.StructureCode))
            throw new InvalidOperationException($"Structure with code '{structure.StructureCode}' already exists");

        _structures.Add(structure);
        AddDomainEvent(new StructureAddedEvent(this, structure));
    }

    /// <summary>
    /// Adds a new position to the organization
    /// </summary>
    public void AddPosition(Position position)
    {
        if (position == null)
            throw new ArgumentNullException(nameof(position));

        if (_positions.Any(p => p.PositionCode == position.PositionCode))
            throw new InvalidOperationException($"Position with code '{position.PositionCode}' already exists");

        _positions.Add(position);
        AddDomainEvent(new PositionAddedEvent(this, position));
    }

    /// <summary>
    /// Adds a new ranking to the organization
    /// </summary>
    public void AddRanking(Ranking ranking)
    {
        if (ranking == null)
            throw new ArgumentNullException(nameof(ranking));

        if (_rankings.Any(r => r.RankingCode == ranking.RankingCode))
            throw new InvalidOperationException($"Ranking with code '{ranking.RankingCode}' already exists");

        _rankings.Add(ranking);
        AddDomainEvent(new RankingAddedEvent(this, ranking));
    }

    /// <summary>
    /// Adds a new work shift to the organization
    /// </summary>
    public void AddWorkShift(WorkShift workShift)
    {
        if (workShift == null)
            throw new ArgumentNullException(nameof(workShift));

        if (_workShifts.Any(w => w.WorkShiftCode == workShift.WorkShiftCode))
            throw new InvalidOperationException($"Work shift with code '{workShift.WorkShiftCode}' already exists");

        _workShifts.Add(workShift);
        AddDomainEvent(new WorkShiftAddedEvent(this, workShift));
    }

    /// <summary>
    /// Deactivates the organization (soft delete)
    /// </summary>
    public void Deactivate()
    {
        if (IsDeleted)
            throw new InvalidOperationException("Organization is already deactivated");

        IsDeleted = true;
        DeletedAtUtc = DateTime.UtcNow;
        AddDomainEvent(new OrganizationDeactivatedEvent(this));
    }

    /// <summary>
    /// Reactivates the organization
    /// </summary>
    public void Reactivate()
    {
        if (!IsDeleted)
            throw new InvalidOperationException("Organization is already active");

        IsDeleted = false;
        DeletedAtUtc = null;
        DeletedBy = null;
        AddDomainEvent(new OrganizationReactivatedEvent(this));
    }

    // Private helper methods
    private void SetOrgCode(string orgCode)
    {
        if (string.IsNullOrWhiteSpace(orgCode))
            throw new ArgumentException("Organization code cannot be null or empty", nameof(orgCode));

        if (orgCode.Length > 50)
            throw new ArgumentException("Organization code cannot exceed 50 characters", nameof(orgCode));

        OrgCode = orgCode.Trim();
    }

    private void SetOrgName(string orgName)
    {
        if (string.IsNullOrWhiteSpace(orgName))
            throw new ArgumentException("Organization name cannot be null or empty", nameof(orgName));

        if (orgName.Length > 200)
            throw new ArgumentException("Organization name cannot exceed 200 characters", nameof(orgName));

        OrgName = orgName.Trim();
    }

    private void SetEmployeeRankingDefinitionType(string type)
    {
        if (type != "DIRECT" && type != "INDIRECT")
            throw new ArgumentException("Employee ranking definition type must be either 'DIRECT' or 'INDIRECT'", nameof(type));

        EmployeeRankingDefinitionType = type;
    }
}
