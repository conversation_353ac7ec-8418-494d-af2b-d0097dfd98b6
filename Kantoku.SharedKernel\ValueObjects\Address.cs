using Kantoku.SharedKernel.BuildingBlocks;
using Kantoku.SharedKernel.Guards;

namespace Kantoku.SharedKernel.ValueObjects;

/// <summary>
/// Value object representing a physical address
/// </summary>
public sealed class Address : ValueObject
{
    /// <summary>
    /// Gets the street address (line 1)
    /// </summary>
    public string Street { get; private set; }

    /// <summary>
    /// Gets the additional address information (line 2)
    /// </summary>
    public string? Street2 { get; private set; }

    /// <summary>
    /// Gets the city
    /// </summary>
    public string City { get; private set; }

    /// <summary>
    /// Gets the state or province
    /// </summary>
    public string? State { get; private set; }

    /// <summary>
    /// Gets the postal code or ZIP code
    /// </summary>
    public string? PostalCode { get; private set; }

    /// <summary>
    /// Gets the country
    /// </summary>
    public string Country { get; private set; }

    /// <summary>
    /// Initializes a new instance of the Address class
    /// </summary>
    /// <param name="street">The street address</param>
    /// <param name="city">The city</param>
    /// <param name="country">The country</param>
    /// <param name="street2">The additional address information</param>
    /// <param name="state">The state or province</param>
    /// <param name="postalCode">The postal code</param>
    private Address(string street, string city, string country, string? street2 = null, string? state = null, string? postalCode = null)
    {
        Street = Guard.NotNullOrWhiteSpace(street).Trim();
        Street2 = street2?.Trim();
        City = Guard.NotNullOrWhiteSpace(city).Trim();
        State = state?.Trim();
        PostalCode = postalCode?.Trim();
        Country = Guard.NotNullOrWhiteSpace(country).Trim();
    }

    /// <summary>
    /// Creates a new Address instance
    /// </summary>
    /// <param name="street">The street address</param>
    /// <param name="city">The city</param>
    /// <param name="country">The country</param>
    /// <param name="street2">The additional address information</param>
    /// <param name="state">The state or province</param>
    /// <param name="postalCode">The postal code</param>
    /// <returns>A new Address instance</returns>
    public static Address Create(string street, string city, string country, string? street2 = null, string? state = null, string? postalCode = null)
    {
        return new Address(street, city, country, street2, state, postalCode);
    }

    /// <summary>
    /// Tries to create a new Address instance
    /// </summary>
    /// <param name="street">The street address</param>
    /// <param name="city">The city</param>
    /// <param name="country">The country</param>
    /// <param name="address">The created Address instance if successful</param>
    /// <param name="street2">The additional address information</param>
    /// <param name="state">The state or province</param>
    /// <param name="postalCode">The postal code</param>
    /// <returns>True if the address was created successfully; otherwise, false</returns>
    public static bool TryCreate(string? street, string? city, string? country, out Address? address, string? street2 = null, string? state = null, string? postalCode = null)
    {
        address = null;

        if (string.IsNullOrWhiteSpace(street) || string.IsNullOrWhiteSpace(city) || string.IsNullOrWhiteSpace(country))
            return false;

        try
        {
            address = new Address(street, city, country, street2, state, postalCode);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Gets the full address as a single line
    /// </summary>
    /// <param name="separator">The separator to use between address components</param>
    /// <returns>The full address as a single line</returns>
    public string ToSingleLine(string separator = ", ")
    {
        var components = new List<string> { Street };

        if (!string.IsNullOrWhiteSpace(Street2))
            components.Add(Street2);

        components.Add(City);

        if (!string.IsNullOrWhiteSpace(State))
            components.Add(State);

        if (!string.IsNullOrWhiteSpace(PostalCode))
            components.Add(PostalCode);

        components.Add(Country);

        return string.Join(separator, components);
    }

    /// <summary>
    /// Gets the full address as multiple lines
    /// </summary>
    /// <returns>The full address as multiple lines</returns>
    public string[] ToMultipleLines()
    {
        var lines = new List<string> { Street };

        if (!string.IsNullOrWhiteSpace(Street2))
            lines.Add(Street2);

        var cityStatePostal = City;
        if (!string.IsNullOrWhiteSpace(State))
            cityStatePostal += $", {State}";
        if (!string.IsNullOrWhiteSpace(PostalCode))
            cityStatePostal += $" {PostalCode}";

        lines.Add(cityStatePostal);
        lines.Add(Country);

        return lines.ToArray();
    }

    /// <summary>
    /// Determines whether this address is in the specified country
    /// </summary>
    /// <param name="country">The country to check</param>
    /// <returns>True if the address is in the specified country; otherwise, false</returns>
    public bool IsInCountry(string country)
    {
        return string.Equals(Country, country, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Determines whether this address is in the specified state or province
    /// </summary>
    /// <param name="state">The state or province to check</param>
    /// <returns>True if the address is in the specified state; otherwise, false</returns>
    public bool IsInState(string state)
    {
        return string.Equals(State, state, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Determines whether this address is in the specified city
    /// </summary>
    /// <param name="city">The city to check</param>
    /// <returns>True if the address is in the specified city; otherwise, false</returns>
    public bool IsInCity(string city)
    {
        return string.Equals(City, city, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Creates a new address with updated street information
    /// </summary>
    /// <param name="newStreet">The new street address</param>
    /// <param name="newStreet2">The new additional address information</param>
    /// <returns>A new Address instance with updated street information</returns>
    public Address WithStreet(string newStreet, string? newStreet2 = null)
    {
        return new Address(newStreet, City, Country, newStreet2, State, PostalCode);
    }

    /// <summary>
    /// Creates a new address with updated city
    /// </summary>
    /// <param name="newCity">The new city</param>
    /// <returns>A new Address instance with updated city</returns>
    public Address WithCity(string newCity)
    {
        return new Address(Street, newCity, Country, Street2, State, PostalCode);
    }

    /// <summary>
    /// Creates a new address with updated state
    /// </summary>
    /// <param name="newState">The new state or province</param>
    /// <returns>A new Address instance with updated state</returns>
    public Address WithState(string? newState)
    {
        return new Address(Street, City, Country, Street2, newState, PostalCode);
    }

    /// <summary>
    /// Creates a new address with updated postal code
    /// </summary>
    /// <param name="newPostalCode">The new postal code</param>
    /// <returns>A new Address instance with updated postal code</returns>
    public Address WithPostalCode(string? newPostalCode)
    {
        return new Address(Street, City, Country, Street2, State, newPostalCode);
    }

    /// <summary>
    /// Creates a new address with updated country
    /// </summary>
    /// <param name="newCountry">The new country</param>
    /// <returns>A new Address instance with updated country</returns>
    public Address WithCountry(string newCountry)
    {
        return new Address(Street, City, newCountry, Street2, State, PostalCode);
    }

    /// <summary>
    /// Returns the string representation of the address
    /// </summary>
    /// <returns>The address as a string</returns>
    public override string ToString() => ToSingleLine();

    /// <summary>
    /// Gets the equality components for value object comparison
    /// </summary>
    /// <returns>The equality components</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Street.ToLowerInvariant();
        yield return Street2?.ToLowerInvariant();
        yield return City.ToLowerInvariant();
        yield return State?.ToLowerInvariant();
        yield return PostalCode?.ToLowerInvariant();
        yield return Country.ToLowerInvariant();
    }
}
