namespace Kantoku.Domain.Models;

public class Vendor : AuditableEntity
{
    public Guid VendorUid { get; set; }


    public string VendorCode { get; set; } = null!;


    public string VendorName { get; set; } = null!;


    public string? VendorSubName { get; set; }


    public string? Address { get; set; }


    public string? CorporateNumber { get; set; }


    public string? PhoneNumber { get; set; }


    public string? Email { get; set; }


    public ContactPerson? ContactPerson { get; set; }
    

    public string? Description { get; set; }

    public bool IsDeleted { get; set; }
    public Guid OrgUid { get; set; }
    public string? LogoUrl { get; set; }

    public virtual Org? Org { get; set; } = null!;

    public virtual ICollection<ItemPrice> ItemPrices { get; set; } = [];

    public virtual ICollection<InputCost> InputCosts { get; set; } = [];

    public virtual ICollection<InputCostItem> InputCostItems { get; set; } = [];
}