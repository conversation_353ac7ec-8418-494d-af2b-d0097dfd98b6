using Kantoku.Domain.NotificationManagement.Enums;

namespace Kantoku.Domain.NotificationManagement;

/// <summary>
/// Repository interface for Notification aggregate
/// </summary>
public interface INotificationRepository
{
    /// <summary>
    /// Gets a notification by ID
    /// </summary>
    Task<Notification?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications by organization
    /// </summary>
    Task<IEnumerable<Notification>> GetByOrganizationAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications by status
    /// </summary>
    Task<IEnumerable<Notification>> GetByStatusAsync(
        Guid orgId, 
        NotificationStatus status, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications by type
    /// </summary>
    Task<IEnumerable<Notification>> GetByTypeAsync(
        Guid orgId, 
        NotificationType type, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications by priority
    /// </summary>
    Task<IEnumerable<Notification>> GetByPriorityAsync(
        Guid orgId, 
        NotificationPriority priority, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications for a specific target
    /// </summary>
    Task<IEnumerable<Notification>> GetForTargetAsync(
        Guid targetId, 
        TargetType targetType, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets unread notifications for a target
    /// </summary>
    Task<IEnumerable<Notification>> GetUnreadForTargetAsync(
        Guid targetId, 
        TargetType targetType, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications scheduled for sending
    /// </summary>
    Task<IEnumerable<Notification>> GetScheduledForSendingAsync(
        DateTime? upToDate = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets expired notifications
    /// </summary>
    Task<IEnumerable<Notification>> GetExpiredAsync(
        DateTime? asOfDate = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications pending approval
    /// </summary>
    Task<IEnumerable<Notification>> GetPendingApprovalAsync(
        Guid orgId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications created by a user
    /// </summary>
    Task<IEnumerable<Notification>> GetCreatedByAsync(
        Guid createdBy, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications with failed deliveries
    /// </summary>
    Task<IEnumerable<Notification>> GetWithFailedDeliveriesAsync(
        Guid orgId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches notifications by title or content
    /// </summary>
    Task<IEnumerable<Notification>> SearchAsync(
        Guid orgId, 
        string searchTerm, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notifications with pagination
    /// </summary>
    Task<(IEnumerable<Notification> Notifications, int TotalCount)> GetPagedAsync(
        Guid orgId,
        int pageNumber = 1,
        int pageSize = 20,
        NotificationStatus? status = null,
        NotificationType? type = null,
        NotificationPriority? priority = null,
        string? searchTerm = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notification statistics for an organization
    /// </summary>
    Task<NotificationStatistics> GetStatisticsAsync(
        Guid orgId, 
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets delivery statistics for a notification
    /// </summary>
    Task<DeliveryStatistics> GetDeliveryStatisticsAsync(
        Guid notificationId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new notification
    /// </summary>
    Task AddAsync(Notification notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing notification
    /// </summary>
    Task UpdateAsync(Notification notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes a notification
    /// </summary>
    Task RemoveAsync(Notification notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk updates notification status
    /// </summary>
    Task BulkUpdateStatusAsync(
        IEnumerable<Guid> notificationIds, 
        NotificationStatus status, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistics about notifications for an organization
/// </summary>
public class NotificationStatistics
{
    public int TotalNotifications { get; set; }
    public int SentNotifications { get; set; }
    public int PendingNotifications { get; set; }
    public int FailedNotifications { get; set; }
    public int DraftNotifications { get; set; }
    public int PendingApprovalNotifications { get; set; }
    public decimal AverageDeliveryRate { get; set; }
    public Dictionary<string, int> NotificationsByType { get; set; } = new();
    public Dictionary<string, int> NotificationsByPriority { get; set; } = new();
    public Dictionary<string, int> NotificationsByChannel { get; set; } = new();
    public Dictionary<string, int> NotificationsByDay { get; set; } = new();
}

/// <summary>
/// Delivery statistics for a notification
/// </summary>
public class DeliveryStatistics
{
    public Guid NotificationId { get; set; }
    public int TotalTargets { get; set; }
    public int SuccessfulDeliveries { get; set; }
    public int FailedDeliveries { get; set; }
    public int PendingDeliveries { get; set; }
    public int ReadCount { get; set; }
    public int DismissedCount { get; set; }
    public decimal DeliveryRate { get; set; }
    public decimal ReadRate { get; set; }
    public decimal EngagementRate { get; set; }
    public Dictionary<string, int> DeliveriesByChannel { get; set; } = new();
    public Dictionary<string, int> FailuresByReason { get; set; } = new();
    public DateTime? FirstDeliveryDate { get; set; }
    public DateTime? LastDeliveryDate { get; set; }
}
