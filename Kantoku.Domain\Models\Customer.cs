﻿namespace Kantoku.Domain.Models;

public class Customer : AuditableEntity
{
    public Guid CustomerUid { get; set; }


    public string CustomerCode { get; set; } = null!;


    public string CustomerName { get; set; } = null!;


    public string? CustomerSubName { get; set; }


    public string? CustomerTypeCode { get; set; }


    public ContactPerson? ContactPerson { get; set; }


    public string? Description { get; set; }


    public string? CorporateNumber { get; set; }


    public string? Address { get; set; }


    public string? PhoneNumber { get; set; }


    public string? Email { get; set; }

    public bool IsDeleted { get; set; }

    public Guid OrgUid { get; set; }

    public string? LogoUrl { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual CustomerType CustomerType { get; set; } = null!;

    public virtual ICollection<Project> Projects { get; set; } = [];
}