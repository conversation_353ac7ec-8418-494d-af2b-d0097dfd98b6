using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class DuplicateProjectSchedulesRequestDto
{
    /// <summary>
    /// Source schedule ids (what to be duplicated) (*)
    /// </summary>
    [Required]
    [JsonPropertyName("projectScheduleIds")]
    public required IEnumerable<Guid> ProjectScheduleIds { get; set; }

    /// <summary>
    /// Target date from (the schedule will be duplicated to, start from this date) (*)  
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [DateTimeValidator(typeof(DateOnly))]
    [JsonPropertyName("targetDateFrom")]
    public required DateOnly TargetDateFrom { get; set; }

    /// <summary>
    /// Target date to (the schedule will be duplicated to, end at this date) (*)
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [DateTimeValidator(typeof(DateOnly))]
    [JsonPropertyName("targetDateTo")]
    public required DateOnly TargetDateTo { get; set; }
}