# Project Transformation: Monolith to Clean Architecture with DDD & Event-Driven Approach

## 1. Goals & Motivations

*   **Improved Maintainability:** Isolate business logic from infrastructure concerns, making code easier to understand, modify, and extend.
*   **Enhanced Testability:** Decouple components to allow for focused unit and integration testing.
*   **Increased Scalability:** Design components that can be scaled independently (even if initially deployed as a monolith).
*   **Business Agility:** Create a system that can more easily adapt to changing business requirements by modeling the domain effectively.
*   **Clearer Separation of Concerns:** Enforce strict boundaries between different parts of the application.

## 2. Core Architectural Principles

*   **Clean Architecture:**
    *   **Dependency Rule:** Source code dependencies can only point inwards. Nothing in an inner circle can know anything at all about something in an outer circle.
    *   **Layers:**
        *   **Domain Layer:** Contains enterprise-wide business logic. Includes Entities, Value Objects, Aggregates, Domain Events, Repository Interfaces, and Domain Services. It has no dependencies on other layers.
        *   **Application Layer:** Contains application-specific business logic (use cases). Orchestrates domain objects. Includes Application Services (Commands/Queries handlers), DTOs, and interfaces for infrastructure concerns (e.g., `IUnitOfWork`, `IEventBusPublisher`). Depends only on the Domain Layer.
        *   **Infrastructure Layer:** Contains implementations for interfaces defined in the Application and Domain layers (e.g., EF Core Repositories, Database Context, Event Bus implementation, external service clients, file system access). Depends on Application and Domain layers (for interfaces) and external libraries.
        *   **Presentation/API Layer:** Handles UI/API concerns (e.g., ASP.NET Core Controllers, API models/view models). Depends on the Application Layer.
*   **Domain-Driven Design (DDD):**
    *   **Ubiquitous Language:** Develop a common, rigorous language between developers and domain experts.
    *   **Bounded Contexts:** Explicitly define boundaries around different parts of the domain, each with its own model and ubiquitous language.
    *   **Aggregates:** Clusters of entities and value objects treated as a single unit for data changes, ensuring consistency. Each aggregate has a root entity.
    *   **Entities:** Objects with a distinct identity that persists over time.
    *   **Value Objects:** Objects defined by their attributes, immutable, and without a conceptual identity.
    *   **Repositories:** Abstractions for data access, providing an illusion of an in-memory collection of aggregates.
    *   **Domain Services:** For domain logic that doesn't naturally fit within an entity or value object.
    *   **Domain Events:** Capture significant occurrences within the domain.
*   **Event-Driven Architecture (EDA):**
    *   Decouple components by communicating through events.
    *   Enable asynchronous processing and improved resilience.
    *   Facilitate integration between different bounded contexts or services.

## 3. Proposed Target Project Structure (Illustrative)

```
KantokuService/
├── Kantoku.Domain/                   // Domain Layer: Entities, Aggregates, Value Objects, Domain Events, Repository Interfaces
├── Kantoku.Application/              // Application Layer: Use Cases (Commands/Queries), DTOs, Application Service Interfaces, Infrastructure Interfaces
├── Kantoku.Infrastructure/           // Infrastructure Layer: EF Core DbContext, Repositories Impl, EventBus Impl, External Services
├── Kantoku.Api/                      // Presentation Layer: ASP.NET Core Controllers, API DTOs, Startup, Middlewares (refactored)
├── Kantoku.Processor/                // Potentially a separate Bounded Context or specific infrastructure for background tasks
│   ├── Kantoku.Processor.Domain/       // If it becomes its own bounded context
│   ├── Kantoku.Processor.Application/
│   └── Kantoku.Processor.Infrastructure/
└── Kantoku.SharedKernel/ (Optional)  // Common building blocks, base classes, cross-cutting concerns (e.g., BaseEntity, Result types)
```

## 4. Phased Refactoring Approach

This is a gradual process. Avoid a "big bang" rewrite.

### Phase 0: Preparation & Foundation (Weeks 1-4)

*   **1. Deep Dive & Analysis:**
    *   Thoroughly review the existing `Kantoku.Api` and `Kantoku.Processor` codebase.
    *   Identify core business domains and subdomains (e.g., User Management, Project Management, Costing, Scheduling, Reporting).
    *   Map out existing data flows and critical business logic.
    *   Identify existing pain points and areas that would benefit most from refactoring.
*   **2. Team Upskilling & Alignment:**
    *   Ensure the team understands Clean Architecture, DDD, and EDA principles. Conduct workshops if necessary.
    *   Agree on coding standards, naming conventions (refer to `asp-dotnet` rules), and tools.
*   **3. Define Initial Bounded Contexts (High-Level):**
    *   Based on the analysis, make a first pass at identifying potential Bounded Contexts. `Kantoku.Processor` might be a natural candidate. Other candidates could emerge from distinct functional areas in `Kantoku.Api`.
    *   Document the responsibilities and interactions of these initial contexts.
*   **4. Set Up New Project Structure:**
    *   Create the new solution structure with projects for `Domain`, `Application`, `Infrastructure`.
    *   Consider a `SharedKernel` project for truly common, domain-agnostic code (e.g., base classes like `Entity<TId>`, `ValueObject`, custom `Result` types, common interfaces).
*   **5. Introduce Core Abstractions:**
    *   Define base classes/interfaces in `Kantoku.Domain` (or `SharedKernel`): `Entity`, `AggregateRoot`, `ValueObject`, `IDomainEvent`, `IRepository<T>`.
    *   Define core interfaces in `Kantoku.Application`: `ICommand`, `IQuery`, `ICommandHandler<TCommand>`, `IQueryHandler<TQuery, TResult>`, `IUnitOfWork`, `IEventBusPublisher`, `IEventBusSubscriber`.
*   **6. Basic Event Bus Setup:**
    *   Implement or choose a simple in-process event bus for domain events initially (e.g., using MediatR for dispatching domain events within an aggregate's transaction).
    *   Plan for a more robust, potentially out-of-process event bus (RabbitMQ, Kafka, Azure Service Bus) for integration events between bounded contexts later.
*   **7. Logging & Error Handling:**
    *   Standardize logging (Serilog is good as per `asp-dotnet` rules).
    *   Implement global exception handling middleware in `Kantoku.Api` that translates exceptions into consistent API error responses.

### Phase 1: First Vertical Slice - The "Strangler Fig" Seed (Weeks 5-12)

The goal is to refactor a single, relatively isolated feature or a small part of a bounded context into the new architecture.

*   **1. Select a Candidate Slice:**
    *   Choose a feature that is:
        *   Well-understood.
        *   Relatively low-risk or non-critical if issues arise.
        *   Complex enough to exercise the new architecture but not overwhelmingly so.
    *   Example: A simple CRUD operation for a less critical entity, or a specific business process within `Kantoku.Processor`.
*   **2. Domain Modeling for the Slice:**
    *   Identify aggregates, entities, and value objects relevant to this slice. Define them in `Kantoku.Domain`.
    *   Define repository interfaces for the aggregates in `Kantoku.Domain`.
    *   Implement domain logic within entities/aggregates. Use domain services if necessary.
    *   Define domain events that occur within this slice.
*   **3. Application Layer for the Slice:**
    *   Create Commands (for state changes) and Queries (for data retrieval) in `Kantoku.Application`.
    *   Implement corresponding Command Handlers and Query Handlers. These handlers will use repository interfaces to interact with the domain and orchestrate the use case.
    *   Define DTOs for input and output of these application services.
*   **4. Infrastructure Implementation for the Slice:**
    *   Implement the repository interfaces in `Kantoku.Infrastructure` using EF Core.
    *   Update/configure the `DbContext` to include the new domain entities.
    *   Implement the `IUnitOfWork` pattern (often tied to `DbContext.SaveChanges`).
*   **5. Presentation/API Layer Integration:**
    *   Modify or create new API controllers in `Kantoku.Api` to use the new application services (command/query handlers) instead of directly accessing old services or DbContext.
    *   Ensure requests and responses use the new DTOs.
*   **6. Event Handling (In-Process):**
    *   Implement handlers for domain events raised by the slice. These handlers might update other aggregates (within the same transaction initially) or perform other side effects. Use MediatR for this.
*   **7. Testing:**
    *   Write unit tests for domain logic, application services (handlers), and event handlers.
    *   Write integration tests to verify the slice from the API endpoint down to the database, including event publishing/handling.
*   **8. "Strangling" the Old Code:**
    *   Once the new slice is working and tested, traffic for this specific feature is routed to the new implementation. The old code for this feature is now "strangled" and can eventually be removed.

### Phase 2: Iterative Refactoring & Expansion (Months 4-12+)

*   **1. Repeat Phase 1 for Other Slices/Features:**
    *   Incrementally identify and refactor more features or parts of bounded contexts.
    *   Prioritize based on business value, risk, and complexity.
*   **2. Refine Bounded Contexts:**
    *   As more of the system is refactored, the boundaries and responsibilities of bounded contexts will become clearer. Adjust them as needed.
    *   Consider how `Kantoku.Processor` fits. Is it one bounded context? Does it host logic for multiple?
*   **3. Introduce Integration Events:**
    *   When communication is needed *between* clearly defined Bounded Contexts (or if you anticipate splitting into microservices later), introduce Integration Events.
    *   These are different from Domain Events (which are about things happening *within* a bounded context).
    *   Choose and implement an out-of-process event bus (e.g., RabbitMQ, Kafka, Azure Service Bus).
    *   Publish integration events from one context (e.g., after a domain event has been successfully handled and persisted).
    *   Subscribe to integration events in other contexts. This often involves creating "anti-corruption layers" if the models between contexts differ.
*   **4. Database Decoupling (Gradual):**
    *   Initially, new and old code might share the same database.
    *   As bounded contexts solidify, consider if they need their own databases or schemas to ensure true autonomy. This is a complex step and requires careful data migration strategies.
*   **5. Address Cross-Cutting Concerns:**
    *   Ensure authentication, authorization, logging, validation, and configuration are handled consistently in the new architecture. Leverage ASP.NET Core middleware and filters.
*   **6. Continuous Testing and CI/CD:**
    *   Maintain a high level of test coverage.
    *   Ensure your CI/CD pipeline supports the evolving architecture.

### Phase 3: Processor Refactoring & Potential Service Extraction (Concurrent or Post-Phase 2)

*   **1. Deep Dive into `Kantoku.Processor`:**
    *   Understand its current responsibilities. Does it handle background jobs, scheduled tasks, event processing?
    *   Apply the same DDD and Clean Architecture principles to refactor its logic.
    *   `Kantoku.Processor` might become a dedicated "host" for background workers that consume events or process jobs defined in various application services.
*   **2. Service Extraction (If applicable):**
    *   If a bounded context becomes sufficiently independent and complex, and if scalability or deployment requirements warrant it, consider extracting it into a separate microservice.
    *   This makes the robust integration event strategy from Phase 2 critical.
    *   This step adds operational complexity (deployment, monitoring, distributed transactions/sagas) and should be carefully considered.

### Phase 4: Optimization & Refinement (Ongoing)

*   **Performance Tuning:** Monitor and optimize critical paths, database queries, and event processing.
*   **Architectural Consistency:** Regularly review the codebase to ensure adherence to the architectural principles.
*   **Documentation:** Keep architectural diagrams, bounded context maps, and API documentation up-to-date.
*   **Monitoring & Observability:** Implement comprehensive monitoring for event flows, service health, and performance, especially with an event-driven system (e.g., distributed tracing).

## 5. Key DDD Concepts in Practice

*   **Ubiquitous Language:** Start by creating glossaries for each emerging bounded context.
*   **Identifying Aggregates:** Look for clusters of objects that change together and have clear consistency boundaries. What is the "root" entity that controls the lifecycle of others in the cluster?
*   **Repositories:** Implement `IRepository<T>` in Infrastructure for each Aggregate Root. Ensure they only return Aggregates.
*   **Domain Events:**
    *   Name them in the past tense (e.g., `OrderCreated`, `UserEmailUpdated`).
    *   Dispatch them from within your aggregate roots after a state change.
    *   Handlers can be in the Application layer (for application-level side effects) or Domain layer (if they orchestrate other domain logic).
*   **Entities vs. Value Objects:** Entities have an ID and a lifecycle. Value Objects are defined by their attributes and are typically immutable (e.g., `Address`, `Money`).

## 6. Event-Driven Strategy Details

*   **Domain Events:**
    *   Purpose: Signal state changes within an aggregate/bounded context.
    *   Scope: Local to a bounded context.
    *   Processing: Typically synchronous within the same transaction initially (e.g., using MediatR). Can become asynchronous if needed (e.g., to update a read model).
*   **Integration Events:**
    *   Purpose: Communicate significant occurrences between bounded contexts or external systems.
    *   Scope: Global, cross-context.
    *   Processing: Always asynchronous, via a message broker.
    *   Idempotency: Consumers must be idempotent, as events might be delivered more than once.
    *   Outbox Pattern: To ensure events are published reliably after a transaction commits (prevents publishing an event if the DB transaction fails). The `Kantoku.Processor` could play a role here in polling an "outbox" table and publishing to the message bus.

## 7. Tooling & Technology (Based on your stack and `asp-dotnet` rules)

*   **.NET 8 / ASP.NET Core:** Foundation.
*   **Entity Framework Core:** For data access.
*   **MediatR:** Excellent for in-process messaging (Commands, Queries, Domain Events).
*   **Message Broker (for Integration Events):** RabbitMQ, Kafka, Azure Service Bus, or AWS SQS/SNS. Choose based on your infrastructure and needs.
*   **FluentValidation:** For robust request DTO validation in the Application layer.
*   **xUnit & NSubstitute/Moq:** For testing.
*   **Serilog:** For structured logging.
*   **Swagger/OpenAPI (Swashbuckle):** For API documentation.
*   **Docker:** For containerization and consistent environments.

## 8. Testing Strategy

*   **Domain Layer:** Unit test entities, value objects, domain services. Focus on business logic.
*   **Application Layer:** Unit test command/query handlers. Mock repositories and other infrastructure dependencies. Verify orchestration logic and calls to domain objects.
*   **Infrastructure Layer:** Integration test repository implementations against a test database. Test event bus implementations.
*   **API/Presentation Layer:** Integration test controllers. Can be done in-memory (`WebApplicationFactory`) or against a deployed instance.
*   **End-to-End Tests:** Test critical user flows through the entire system, especially for event-driven sagas or processes.
*   **Contract Testing:** For integration events, ensure publisher and consumer contracts are compatible.

## 9. Potential Challenges & Mitigation

*   **Steep Learning Curve (DDD/EDA):** Invest in training, pair programming, and iterative learning. Start simple.
*   **Identifying Bounded Contexts:** This is often an iterative process. Don't aim for perfection upfront. Use Event Storming workshops.
*   **Database Refactoring & Data Migration:** Plan carefully. Use techniques like schema versioning, data sync scripts. Sometimes, views or materialized views can bridge old and new schemas temporarily.
*   **Managing Eventual Consistency:** Educate the team and stakeholders. Design the UI/UX to handle it.
*   **Debugging Distributed Systems:** Requires good logging, tracing, and monitoring tools.
*   **Over-Engineering:** Don't apply every DDD pattern blindly. Start with the most impactful ones.
*   **Keeping the Monolith Running:** The Strangler Fig pattern helps, but ensure the old parts remain stable during the transition.

## 10. Next Steps (Immediate Actions)

1.  **Form a Core Refactoring Team:** Identify key people who will drive this.
2.  **Schedule Workshops:** For DDD, Event Storming, and architectural alignment.
3.  **Set Up New Project Structure (Phase 0, Step 4):** Get the basic solution structure in place.
4.  **Select the First Vertical Slice (Phase 1, Step 1):** Choose a good candidate to start with.
5.  **Begin Domain Modeling for the Slice (Phase 1, Step 2):** Start applying DDD concepts. 