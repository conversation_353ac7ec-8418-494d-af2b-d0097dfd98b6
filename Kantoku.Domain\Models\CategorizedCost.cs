namespace Kantoku.Domain.Models;

public class CategorizedCost : AuditableEntity
{
    public Guid CategorizedCostUid { get; set; }

    public Guid ConstructionCostUid { get; set; }

    public Guid CategoryUid { get; set; }


    public float? Quantity { get; set; }


    public long TotalAmount { get; set; }


    public long AvgAmount { get; set; }


    public long FixedAmount { get; set; }

    public virtual ConstructionCost ConstructionCost { get; set; } = null!;

    public virtual Category Category { get; set; } = null!;
}

