using Kantoku.Domain.Common.BuildingBlocks;

namespace Kantoku.Domain.EmployeeAggregate.EmployeeShifts;

public class LastModifierEnum : Enumeration<LastModifierEnum, int>
{
    public static readonly LastModifierEnum JOB = new(-1, nameof(JOB));
    public static readonly LastModifierEnum SYSTEM = new(0, nameof(SYSTEM));
    public static readonly LastModifierEnum AUTHOR = new(1, nameof(AUTHOR));
    public static readonly LastModifierEnum MANAGER = new(2, nameof(MANAGER));

    private LastModifierEnum(int value, string name) : base(value, name)
    {
    }
}