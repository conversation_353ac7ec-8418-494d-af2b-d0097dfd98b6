using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class EmployeeNotificationConfiguration(string schema) : IEntityTypeConfiguration<EmployeeNotification>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EmployeeNotification> builder)
    {
        builder.ToTable("employee_notification", Schema);

        builder.HasKey(e => e.EmployeeNotificationUid).HasName("employee_notification_pkey");

        builder.Property(e => e.EmployeeNotificationUid)
            .HasColumnName("employee_notification_uid");
        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_uid");
        builder.Property(e => e.NotificationUid)
            .HasColumnName("notification_uid");
        builder.Property(e => e.IsRead)
            .HasColumnName("is_read");
        builder.Property(e => e.ReadAt)
            .HasColumnType("timestamp without time zone")
            .HasColumnName("read_at");
        builder.Property(e => e.IsDeleted)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.Employee)
            .WithMany(p => p.EmployeeNotifications)
            .HasForeignKey(d => d.EmployeeUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("employee_notification_employee_id_fkey");

        builder.HasOne(d => d.Notification)
            .WithMany(p => p.EmployeeNotifications)
            .HasForeignKey(d => d.NotificationUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("employee_notification_notification_id_fkey");
    }
} 