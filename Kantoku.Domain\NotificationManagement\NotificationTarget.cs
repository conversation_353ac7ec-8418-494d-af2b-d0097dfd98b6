using Kantoku.Domain.Common.BuildingBlocks;
using Kantoku.Domain.NotificationManagement.Enums;

namespace Kantoku.Domain.NotificationManagement;

/// <summary>
/// Entity representing a target for a notification
/// </summary>
public class NotificationTarget : Entity<Guid>
{
    public Guid NotificationId { get; private set; }
    public TargetType TargetType { get; private set; } = TargetType.Employee;
    public Guid TargetId { get; private set; }
    public string? TargetName { get; private set; }
    public string? TargetEmail { get; private set; }
    public string? TargetPhone { get; private set; }
    public bool IsActive { get; private set; } = true;
    public DateTime? ReadDate { get; private set; }
    public DateTime? DismissedDate { get; private set; }
    public Dictionary<string, object> Preferences { get; private set; } = new();

    // Private constructor for EF Core
    private NotificationTarget() : base() { }

    /// <summary>
    /// Creates a new notification target
    /// </summary>
    public NotificationTarget(
        Guid id,
        Guid notificationId,
        TargetType targetType,
        Guid targetId,
        string? targetName = null,
        string? targetEmail = null,
        string? targetPhone = null) : base(id)
    {
        NotificationId = notificationId;
        TargetType = targetType ?? throw new ArgumentNullException(nameof(targetType));
        TargetId = targetId;
        TargetName = targetName?.Trim();
        TargetEmail = targetEmail?.Trim();
        TargetPhone = targetPhone?.Trim();
    }

    /// <summary>
    /// Marks the notification as read
    /// </summary>
    public void MarkAsRead()
    {
        if (!ReadDate.HasValue)
        {
            ReadDate = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Marks the notification as dismissed
    /// </summary>
    public void Dismiss()
    {
        if (!DismissedDate.HasValue)
        {
            DismissedDate = DateTime.UtcNow;
            MarkAsRead(); // Auto-mark as read when dismissed
        }
    }

    /// <summary>
    /// Updates target preferences
    /// </summary>
    public void UpdatePreferences(Dictionary<string, object> preferences)
    {
        Preferences = preferences ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Deactivates the target
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
    }

    /// <summary>
    /// Reactivates the target
    /// </summary>
    public void Reactivate()
    {
        IsActive = true;
    }

    /// <summary>
    /// Checks if the notification has been read
    /// </summary>
    public bool IsRead => ReadDate.HasValue;

    /// <summary>
    /// Checks if the notification has been dismissed
    /// </summary>
    public bool IsDismissed => DismissedDate.HasValue;
}

/// <summary>
/// Entity representing a delivery attempt for a notification
/// </summary>
public class NotificationDelivery : Entity<Guid>
{
    public Guid NotificationId { get; private set; }
    public Guid TargetId { get; private set; }
    public DeliveryChannel Channel { get; private set; } = DeliveryChannel.InApp;
    public DateTime AttemptDate { get; private set; }
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ExternalId { get; private set; } // ID from external service (email provider, SMS, etc.)
    public Dictionary<string, object> DeliveryData { get; private set; } = new();

    // Private constructor for EF Core
    private NotificationDelivery() : base() { }

    /// <summary>
    /// Creates a new notification delivery record
    /// </summary>
    public NotificationDelivery(
        Guid id,
        Guid notificationId,
        Guid targetId,
        DeliveryChannel channel,
        bool isSuccessful,
        string? errorMessage = null,
        string? externalId = null,
        Dictionary<string, object>? deliveryData = null) : base(id)
    {
        NotificationId = notificationId;
        TargetId = targetId;
        Channel = channel ?? throw new ArgumentNullException(nameof(channel));
        AttemptDate = DateTime.UtcNow;
        IsSuccessful = isSuccessful;
        ErrorMessage = errorMessage?.Trim();
        ExternalId = externalId?.Trim();
        DeliveryData = deliveryData ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates a successful delivery record
    /// </summary>
    public static NotificationDelivery CreateSuccessful(
        Guid id,
        Guid notificationId,
        Guid targetId,
        DeliveryChannel channel,
        string? externalId = null,
        Dictionary<string, object>? deliveryData = null)
    {
        return new NotificationDelivery(id, notificationId, targetId, channel, true, null, externalId, deliveryData);
    }

    /// <summary>
    /// Creates a failed delivery record
    /// </summary>
    public static NotificationDelivery CreateFailed(
        Guid id,
        Guid notificationId,
        Guid targetId,
        DeliveryChannel channel,
        string errorMessage,
        Dictionary<string, object>? deliveryData = null)
    {
        return new NotificationDelivery(id, notificationId, targetId, channel, false, errorMessage, null, deliveryData);
    }
}
