using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.CustomerManagement.ValueObjects;
using Kantoku.Domain.CustomerManagement.Enums;
using Kantoku.Domain.ContractorManagement.Events;
using Kantoku.Domain.ContractorManagement.ValueObjects;
using Kantoku.Domain.ContractorManagement.Enums;

namespace Kantoku.Domain.ContractorManagement;

/// <summary>
/// Contractor aggregate root representing a contractor in the system
/// </summary>
public class Contractor : FullAuditedEntity<Guid>
{
    private readonly List<ContractorContact> _contacts = new();
    private readonly List<ContractorCertification> _certifications = new();

    public Guid OrgId { get; private set; } 
    public string ContractorCode { get; private set; } = null!;
    public string ContractorName { get; private set; } = null!;
    public string? ContractorSubName { get; private set; }
    public string? Description { get; private set; }

    public Guid? ContractorTypeId { get; private set; }
    public ContractorStatus Status { get; private set; } = ContractorStatus.Active;

    public CustomerAddress? Address { get; private set; }
    public CustomerContactInfo? ContactInfo { get; private set; }
    public CustomerBusinessInfo? BusinessInfo { get; private set; }
    public ContractorCapabilities? Capabilities { get; private set; }

    public string? LogoUrl { get; private set; }
    public string? Website { get; private set; }
    public string Currency { get; private set; } = "JPY";
    public string PaymentTerms { get; private set; } = "NET30";

    public bool IsPreferred { get; private set; } = false;
    public bool IsBlacklisted { get; private set; } = false;
    public bool RequiresInsurance { get; private set; } = true;
    public DateTime? InsuranceExpiryDate { get; private set; }
    public DateTime? LicenseExpiryDate { get; private set; }

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<ContractorContact> Contacts => _contacts.AsReadOnly();
    public IReadOnlyCollection<ContractorCertification> Certifications => _certifications.AsReadOnly();

    // Private constructor for EF Core
    private Contractor() : base() { }

    /// <summary>
    /// Creates a new contractor
    /// </summary>
    public Contractor(
        Guid id,
        Guid orgId,
        string contractorCode,
        string contractorName,
        string? contractorSubName = null,
        string? description = null,
        Guid? contractorTypeId = null) : base(id)
    {
        OrgId = orgId;
        SetContractorCode(contractorCode);
        SetContractorName(contractorName);
        ContractorSubName = contractorSubName;
        Description = description;
        ContractorTypeId = contractorTypeId;

        AddDomainEvent(new ContractorCreatedEvent(this));
    }

    /// <summary>
    /// Updates basic contractor information
    /// </summary>
    public void UpdateBasicInfo(
        string contractorName,
        string? contractorSubName = null,
        string? description = null,
        Guid? contractorTypeId = null)
    {
        SetContractorName(contractorName);
        ContractorSubName = contractorSubName;
        Description = description;
        ContractorTypeId = contractorTypeId;

        AddDomainEvent(new ContractorUpdatedEvent(this));
    }

    /// <summary>
    /// Updates contractor capabilities
    /// </summary>
    public void UpdateCapabilities(ContractorCapabilities capabilities)
    {
        Capabilities = capabilities;
        AddDomainEvent(new ContractorCapabilitiesUpdatedEvent(this));
    }

    /// <summary>
    /// Updates contractor status
    /// </summary>
    public void UpdateStatus(ContractorStatus status)
    {
        var oldStatus = Status;
        Status = status;

        if (oldStatus != status)
        {
            AddDomainEvent(new ContractorStatusChangedEvent(this, oldStatus, status));
        }
    }

    /// <summary>
    /// Updates insurance information
    /// </summary>
    public void UpdateInsuranceInfo(bool requiresInsurance, DateTime? insuranceExpiryDate = null)
    {
        RequiresInsurance = requiresInsurance;
        InsuranceExpiryDate = insuranceExpiryDate;

        if (requiresInsurance && !insuranceExpiryDate.HasValue)
            throw new ArgumentException("Insurance expiry date is required when insurance is required");

        AddDomainEvent(new ContractorInsuranceUpdatedEvent(this));
    }

    /// <summary>
    /// Updates license information
    /// </summary>
    public void UpdateLicenseInfo(DateTime? licenseExpiryDate)
    {
        LicenseExpiryDate = licenseExpiryDate;
        AddDomainEvent(new ContractorLicenseUpdatedEvent(this));
    }

    /// <summary>
    /// Sets contractor as preferred
    /// </summary>
    public void SetAsPreferred()
    {
        if (!IsPreferred)
        {
            IsPreferred = true;
            AddDomainEvent(new ContractorPreferredStatusChangedEvent(this, true));
        }
    }

    /// <summary>
    /// Removes preferred status
    /// </summary>
    public void RemovePreferredStatus()
    {
        if (IsPreferred)
        {
            IsPreferred = false;
            AddDomainEvent(new ContractorPreferredStatusChangedEvent(this, false));
        }
    }

    /// <summary>
    /// Blacklists the contractor
    /// </summary>
    public void Blacklist(string reason)
    {
        if (!IsBlacklisted)
        {
            IsBlacklisted = true;
            Status = ContractorStatus.Blacklisted;
            IsPreferred = false;
            AddDomainEvent(new ContractorBlacklistedEvent(this, reason));
        }
    }

    /// <summary>
    /// Removes contractor from blacklist
    /// </summary>
    public void RemoveFromBlacklist()
    {
        if (IsBlacklisted)
        {
            IsBlacklisted = false;
            Status = ContractorStatus.Active;
            AddDomainEvent(new ContractorRemovedFromBlacklistEvent(this));
        }
    }

    /// <summary>
    /// Qualifies the contractor
    /// </summary>
    public void Qualify()
    {
        if (Status != ContractorStatus.Qualified)
        {
            Status = ContractorStatus.Qualified;
            AddDomainEvent(new ContractorQualifiedEvent(this));
        }
    }

    /// <summary>
    /// Disqualifies the contractor
    /// </summary>
    public void Disqualify(string reason)
    {
        if (Status != ContractorStatus.Unqualified)
        {
            Status = ContractorStatus.Unqualified;
            IsPreferred = false;
            AddDomainEvent(new ContractorDisqualifiedEvent(this, reason));
        }
    }

    /// <summary>
    /// Adds a certification to the contractor
    /// </summary>
    public void AddCertification(ContractorCertification certification)
    {
        if (certification == null)
            throw new ArgumentNullException(nameof(certification));

        if (_certifications.Any(c => c.CertificationName == certification.CertificationName))
            throw new InvalidOperationException("Certification already exists");

        _certifications.Add(certification);
        AddDomainEvent(new ContractorCertificationAddedEvent(this, certification));
    }

    /// <summary>
    /// Removes a certification from the contractor
    /// </summary>
    public void RemoveCertification(Guid certificationId)
    {
        var certification = _certifications.FirstOrDefault(c => c.Id == certificationId);
        if (certification != null)
        {
            _certifications.Remove(certification);
            AddDomainEvent(new ContractorCertificationRemovedEvent(this, certification));
        }
    }

    /// <summary>
    /// Adds a contact to the contractor
    /// </summary>
    public void AddContact(ContractorContact contact)
    {
        if (contact == null)
            throw new ArgumentNullException(nameof(contact));

        // If this is set as primary, remove primary status from other contacts
        if (contact.IsPrimary)
        {
            foreach (var existingContact in _contacts.Where(c => c.IsPrimary))
            {
                existingContact.RemovePrimaryStatus();
            }
        }

        _contacts.Add(contact);
        AddDomainEvent(new ContractorContactAddedEvent(this, contact));
    }

    /// <summary>
    /// Removes a contact from the contractor
    /// </summary>
    public void RemoveContact(Guid contactId)
    {
        var contact = _contacts.FirstOrDefault(c => c.Id == contactId);
        if (contact != null)
        {
            _contacts.Remove(contact);
            AddDomainEvent(new ContractorContactRemovedEvent(this, contact));
        }
    }

    /// <summary>
    /// Sets a contact as primary
    /// </summary>
    public void SetPrimaryContact(Guid contactId)
    {
        var contact = _contacts.FirstOrDefault(c => c.Id == contactId);
        if (contact == null)
            throw new ArgumentException("Contact not found", nameof(contactId));

        // Remove primary status from other contacts
        foreach (var existingContact in _contacts.Where(c => c.IsPrimary && c.Id != contactId))
        {
            existingContact.RemovePrimaryStatus();
        }

        contact.SetAsPrimary();
    }

    /// <summary>
    /// Checks if the contractor can work
    /// </summary>
    public bool CanWork => Status.CanWork && !IsBlacklisted && IsInsuranceValid && IsLicenseValid;

    /// <summary>
    /// Checks if insurance is valid
    /// </summary>
    public bool IsInsuranceValid
    {
        get
        {
            if (!RequiresInsurance)
                return true;

            return InsuranceExpiryDate.HasValue && InsuranceExpiryDate.Value > DateTime.Today;
        }
    }

    /// <summary>
    /// Checks if license is valid
    /// </summary>
    public bool IsLicenseValid => !LicenseExpiryDate.HasValue || LicenseExpiryDate.Value > DateTime.Today;

    /// <summary>
    /// Checks if insurance is expiring soon (within 30 days)
    /// </summary>
    public bool IsInsuranceExpiringSoon
    {
        get
        {
            if (!RequiresInsurance || !InsuranceExpiryDate.HasValue)
                return false;

            return InsuranceExpiryDate.Value <= DateTime.Today.AddDays(30);
        }
    }

    /// <summary>
    /// Checks if license is expiring soon (within 30 days)
    /// </summary>
    public bool IsLicenseExpiringSoon
    {
        get
        {
            if (!LicenseExpiryDate.HasValue)
                return false;

            return LicenseExpiryDate.Value <= DateTime.Today.AddDays(30);
        }
    }

    // Private helper methods
    private void SetContractorCode(string contractorCode)
    {
        if (string.IsNullOrWhiteSpace(contractorCode))
            throw new ArgumentException("Contractor code cannot be null or empty", nameof(contractorCode));

        if (contractorCode.Length > 50)
            throw new ArgumentException("Contractor code cannot exceed 50 characters", nameof(contractorCode));

        ContractorCode = contractorCode.Trim();
    }

    private void SetContractorName(string contractorName)
    {
        if (string.IsNullOrWhiteSpace(contractorName))
            throw new ArgumentException("Contractor name cannot be null or empty", nameof(contractorName));

        if (contractorName.Length > 200)
            throw new ArgumentException("Contractor name cannot exceed 200 characters", nameof(contractorName));

        ContractorName = contractorName.Trim();
    }
}
