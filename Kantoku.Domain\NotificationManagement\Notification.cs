using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.NotificationManagement.Events;
using Kantoku.Domain.NotificationManagement.ValueObjects;
using Kantoku.Domain.NotificationManagement.Enums;

namespace Kantoku.Domain.NotificationManagement;

/// <summary>
/// Notification aggregate root representing a notification in the system
/// </summary>
public class Notification : FullAuditedEntity<Guid>
{
    private readonly List<NotificationTarget> _targets = new();
    private readonly List<NotificationDelivery> _deliveries = new();

    public Guid OrgId { get; private set; }
    public string Title { get; private set; } = null!;
    public string Content { get; private set; } = null!;
    public NotificationType Type { get; private set; } = NotificationType.Info;
    public NotificationPriority Priority { get; private set; } = NotificationPriority.Normal;
    public NotificationStatus Status { get; private set; } = NotificationStatus.Draft;
    
    public NotificationContent? RichContent { get; private set; }
    public string? ActionUrl { get; private set; }
    public string? ActionText { get; private set; }
    public string? ImageUrl { get; private set; }
    public string? IconUrl { get; private set; }
    
    public DateTime? ScheduledDate { get; private set; }
    public DateTime? SentDate { get; private set; }
    public DateTime? ExpiryDate { get; private set; }
    
    public Guid? CreatedBy { get; private set; }
    public Guid? ApprovedBy { get; private set; }
    public DateTime? ApprovedDate { get; private set; }
    
    public bool RequiresApproval { get; private set; } = false;
    public bool IsRecurring { get; private set; } = false;
    public string? RecurrencePattern { get; private set; }
    
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<NotificationTarget> Targets => _targets.AsReadOnly();
    public IReadOnlyCollection<NotificationDelivery> Deliveries => _deliveries.AsReadOnly();

    // Private constructor for EF Core
    private Notification() : base() { }

    /// <summary>
    /// Creates a new notification
    /// </summary>
    public Notification(
        Guid id,
        Guid orgId,
        string title,
        string content,
        NotificationType type = null,
        NotificationPriority priority = null,
        Guid? createdBy = null,
        DateTime? scheduledDate = null,
        DateTime? expiryDate = null,
        bool requiresApproval = false) : base(id)
    {
        OrgId = orgId;
        SetTitle(title);
        SetContent(content);
        Type = type ?? NotificationType.Info;
        Priority = priority ?? NotificationPriority.Normal;
        CreatedBy = createdBy;
        ScheduledDate = scheduledDate;
        ExpiryDate = expiryDate;
        RequiresApproval = requiresApproval;

        if (requiresApproval)
            Status = NotificationStatus.PendingApproval;

        AddDomainEvent(new NotificationCreatedEvent(this));
    }

    /// <summary>
    /// Updates notification content
    /// </summary>
    public void UpdateContent(
        string title,
        string content,
        NotificationContent? richContent = null,
        string? actionUrl = null,
        string? actionText = null,
        string? imageUrl = null,
        string? iconUrl = null)
    {
        if (Status == NotificationStatus.Sent)
            throw new InvalidOperationException("Cannot update content of sent notification");

        SetTitle(title);
        SetContent(content);
        RichContent = richContent;
        ActionUrl = actionUrl?.Trim();
        ActionText = actionText?.Trim();
        ImageUrl = imageUrl?.Trim();
        IconUrl = iconUrl?.Trim();

        AddDomainEvent(new NotificationUpdatedEvent(this));
    }

    /// <summary>
    /// Updates notification scheduling
    /// </summary>
    public void UpdateScheduling(
        DateTime? scheduledDate = null,
        DateTime? expiryDate = null,
        bool isRecurring = false,
        string? recurrencePattern = null)
    {
        if (Status == NotificationStatus.Sent)
            throw new InvalidOperationException("Cannot update scheduling of sent notification");

        ScheduledDate = scheduledDate;
        ExpiryDate = expiryDate;
        IsRecurring = isRecurring;
        RecurrencePattern = isRecurring ? recurrencePattern : null;

        if (scheduledDate.HasValue && scheduledDate.Value <= DateTime.UtcNow)
            throw new ArgumentException("Scheduled date must be in the future");

        if (expiryDate.HasValue && expiryDate.Value <= DateTime.UtcNow)
            throw new ArgumentException("Expiry date must be in the future");

        AddDomainEvent(new NotificationSchedulingUpdatedEvent(this));
    }

    /// <summary>
    /// Adds a target to the notification
    /// </summary>
    public void AddTarget(NotificationTarget target)
    {
        if (target == null)
            throw new ArgumentNullException(nameof(target));

        if (Status == NotificationStatus.Sent)
            throw new InvalidOperationException("Cannot add targets to sent notification");

        if (_targets.Any(t => t.TargetType == target.TargetType && t.TargetId == target.TargetId))
            throw new InvalidOperationException("Target already exists");

        _targets.Add(target);
        AddDomainEvent(new NotificationTargetAddedEvent(this, target));
    }

    /// <summary>
    /// Removes a target from the notification
    /// </summary>
    public void RemoveTarget(Guid targetId)
    {
        if (Status == NotificationStatus.Sent)
            throw new InvalidOperationException("Cannot remove targets from sent notification");

        var target = _targets.FirstOrDefault(t => t.Id == targetId);
        if (target != null)
        {
            _targets.Remove(target);
            AddDomainEvent(new NotificationTargetRemovedEvent(this, target));
        }
    }

    /// <summary>
    /// Approves the notification
    /// </summary>
    public void Approve(Guid approvedBy)
    {
        if (Status != NotificationStatus.PendingApproval)
            throw new InvalidOperationException("Notification is not pending approval");

        Status = NotificationStatus.Approved;
        ApprovedBy = approvedBy;
        ApprovedDate = DateTime.UtcNow;

        AddDomainEvent(new NotificationApprovedEvent(this));
    }

    /// <summary>
    /// Rejects the notification
    /// </summary>
    public void Reject(Guid rejectedBy, string reason)
    {
        if (Status != NotificationStatus.PendingApproval)
            throw new InvalidOperationException("Notification is not pending approval");

        Status = NotificationStatus.Rejected;
        Metadata["RejectedBy"] = rejectedBy.ToString();
        Metadata["RejectedDate"] = DateTime.UtcNow.ToString("O");
        Metadata["RejectionReason"] = reason;

        AddDomainEvent(new NotificationRejectedEvent(this, reason));
    }

    /// <summary>
    /// Sends the notification
    /// </summary>
    public void Send()
    {
        if (Status == NotificationStatus.Sent)
            throw new InvalidOperationException("Notification already sent");

        if (RequiresApproval && Status != NotificationStatus.Approved)
            throw new InvalidOperationException("Notification requires approval before sending");

        if (!_targets.Any())
            throw new InvalidOperationException("Cannot send notification without targets");

        if (ScheduledDate.HasValue && ScheduledDate.Value > DateTime.UtcNow)
            throw new InvalidOperationException("Cannot send notification before scheduled date");

        Status = NotificationStatus.Sent;
        SentDate = DateTime.UtcNow;

        AddDomainEvent(new NotificationSentEvent(this));
    }

    /// <summary>
    /// Cancels the notification
    /// </summary>
    public void Cancel(string reason)
    {
        if (Status == NotificationStatus.Sent)
            throw new InvalidOperationException("Cannot cancel sent notification");

        Status = NotificationStatus.Cancelled;
        Metadata["CancelledDate"] = DateTime.UtcNow.ToString("O");
        Metadata["CancellationReason"] = reason;

        AddDomainEvent(new NotificationCancelledEvent(this, reason));
    }

    /// <summary>
    /// Records a delivery attempt
    /// </summary>
    public void RecordDelivery(NotificationDelivery delivery)
    {
        if (delivery == null)
            throw new ArgumentNullException(nameof(delivery));

        _deliveries.Add(delivery);
        AddDomainEvent(new NotificationDeliveryRecordedEvent(this, delivery));
    }

    /// <summary>
    /// Updates metadata
    /// </summary>
    public void UpdateMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    /// <summary>
    /// Checks if the notification is ready to send
    /// </summary>
    public bool IsReadyToSend
    {
        get
        {
            if (Status == NotificationStatus.Sent || Status == NotificationStatus.Cancelled)
                return false;

            if (RequiresApproval && Status != NotificationStatus.Approved)
                return false;

            if (!_targets.Any())
                return false;

            if (ScheduledDate.HasValue && ScheduledDate.Value > DateTime.UtcNow)
                return false;

            if (ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.UtcNow)
                return false;

            return true;
        }
    }

    /// <summary>
    /// Checks if the notification is expired
    /// </summary>
    public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.UtcNow;

    /// <summary>
    /// Gets the delivery success rate
    /// </summary>
    public decimal DeliverySuccessRate
    {
        get
        {
            if (!_deliveries.Any())
                return 0;

            var successfulDeliveries = _deliveries.Count(d => d.IsSuccessful);
            return (decimal)successfulDeliveries / _deliveries.Count * 100;
        }
    }

    // Private helper methods
    private void SetTitle(string title)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be null or empty", nameof(title));

        if (title.Length > 200)
            throw new ArgumentException("Title cannot exceed 200 characters", nameof(title));

        Title = title.Trim();
    }

    private void SetContent(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be null or empty", nameof(content));

        if (content.Length > 5000)
            throw new ArgumentException("Content cannot exceed 5000 characters", nameof(content));

        Content = content.Trim();
    }
}
