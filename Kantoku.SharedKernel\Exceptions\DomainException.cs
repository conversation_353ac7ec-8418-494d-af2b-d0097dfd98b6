namespace Kantoku.SharedKernel.Exceptions;

/// <summary>
/// Base class for domain-specific exceptions.
/// Domain exceptions represent business rule violations or invalid domain operations.
/// </summary>
public abstract class DomainException : Exception
{
    /// <summary>
    /// Gets the error code associated with this exception
    /// </summary>
    public string ErrorCode { get; protected set; }

    /// <summary>
    /// Gets additional details about the exception
    /// </summary>
    public IDictionary<string, object> Details { get; protected set; }

    /// <summary>
    /// Initializes a new instance of the DomainException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errorCode">The error code</param>
    protected DomainException(string message, string? errorCode = null)
        : base(message)
    {
        ErrorCode = errorCode ?? GetType().Name;
        Details = new Dictionary<string, object>();
    }

    /// <summary>
    /// Initializes a new instance of the DomainException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="innerException">The inner exception</param>
    /// <param name="errorCode">The error code</param>
    protected DomainException(string message, Exception innerException, string? errorCode = null)
        : base(message, innerException)
    {
        ErrorCode = errorCode ?? GetType().Name;
        Details = new Dictionary<string, object>();
    }

    /// <summary>
    /// Adds a detail to the exception
    /// </summary>
    /// <param name="key">The detail key</param>
    /// <param name="value">The detail value</param>
    /// <returns>The current exception instance for method chaining</returns>
    public DomainException WithDetail(string key, object value)
    {
        Details[key] = value;
        return this;
    }

    /// <summary>
    /// Adds multiple details to the exception
    /// </summary>
    /// <param name="details">The details to add</param>
    /// <returns>The current exception instance for method chaining</returns>
    public DomainException WithDetails(IDictionary<string, object> details)
    {
        foreach (var detail in details)
        {
            Details[detail.Key] = detail.Value;
        }
        return this;
    }

    /// <summary>
    /// Gets a formatted error message including details
    /// </summary>
    /// <returns>The formatted error message</returns>
    public virtual string GetFormattedMessage()
    {
        if (!Details.Any())
            return Message;

        var detailsString = string.Join(", ", Details.Select(d => $"{d.Key}: {d.Value}"));
        return $"{Message} (Details: {detailsString})";
    }

    /// <summary>
    /// Returns the string representation of the exception
    /// </summary>
    /// <returns>The exception as a string</returns>
    public override string ToString()
    {
        return $"[{ErrorCode}] {GetFormattedMessage()}{Environment.NewLine}{base.ToString()}";
    }
}

/// <summary>
/// Exception thrown when a business rule is violated
/// </summary>
public class BusinessRuleViolationException : DomainException
{
    /// <summary>
    /// Gets the name of the business rule that was violated
    /// </summary>
    public string RuleName { get; private set; }

    /// <summary>
    /// Initializes a new instance of the BusinessRuleViolationException class
    /// </summary>
    /// <param name="ruleName">The name of the business rule that was violated</param>
    /// <param name="message">The error message</param>
    public BusinessRuleViolationException(string ruleName, string message)
        : base(message, "BUSINESS_RULE_VIOLATION")
    {
        RuleName = ruleName;
        WithDetail("RuleName", ruleName);
    }

    /// <summary>
    /// Initializes a new instance of the BusinessRuleViolationException class
    /// </summary>
    /// <param name="ruleName">The name of the business rule that was violated</param>
    /// <param name="message">The error message</param>
    /// <param name="innerException">The inner exception</param>
    public BusinessRuleViolationException(string ruleName, string message, Exception innerException)
        : base(message, innerException, "BUSINESS_RULE_VIOLATION")
    {
        RuleName = ruleName;
        WithDetail("RuleName", ruleName);
    }
}

/// <summary>
/// Exception thrown when an entity is not found
/// </summary>
public class EntityNotFoundException : DomainException
{
    /// <summary>
    /// Gets the type of the entity that was not found
    /// </summary>
    public Type EntityType { get; private set; }

    /// <summary>
    /// Gets the identifier of the entity that was not found
    /// </summary>
    public object EntityId { get; private set; }

    /// <summary>
    /// Initializes a new instance of the EntityNotFoundException class
    /// </summary>
    /// <param name="entityType">The type of the entity that was not found</param>
    /// <param name="entityId">The identifier of the entity that was not found</param>
    public EntityNotFoundException(Type entityType, object entityId)
        : base($"{entityType.Name} with ID '{entityId}' was not found.", "ENTITY_NOT_FOUND")
    {
        EntityType = entityType;
        EntityId = entityId;
        WithDetail("EntityType", entityType.Name);
        WithDetail("EntityId", entityId);
    }

    /// <summary>
    /// Initializes a new instance of the EntityNotFoundException class
    /// </summary>
    /// <param name="entityType">The type of the entity that was not found</param>
    /// <param name="entityId">The identifier of the entity that was not found</param>
    /// <param name="innerException">The inner exception</param>
    public EntityNotFoundException(Type entityType, object entityId, Exception innerException)
        : base($"{entityType.Name} with ID '{entityId}' was not found.", innerException, "ENTITY_NOT_FOUND")
    {
        EntityType = entityType;
        EntityId = entityId;
        WithDetail("EntityType", entityType.Name);
        WithDetail("EntityId", entityId);
    }
}

/// <summary>
/// Exception thrown when an entity already exists
/// </summary>
public class EntityAlreadyExistsException : DomainException
{
    /// <summary>
    /// Gets the type of the entity that already exists
    /// </summary>
    public Type EntityType { get; private set; }

    /// <summary>
    /// Gets the identifier of the entity that already exists
    /// </summary>
    public object EntityId { get; private set; }

    /// <summary>
    /// Initializes a new instance of the EntityAlreadyExistsException class
    /// </summary>
    /// <param name="entityType">The type of the entity that already exists</param>
    /// <param name="entityId">The identifier of the entity that already exists</param>
    public EntityAlreadyExistsException(Type entityType, object entityId)
        : base($"{entityType.Name} with ID '{entityId}' already exists.", "ENTITY_ALREADY_EXISTS")
    {
        EntityType = entityType;
        EntityId = entityId;
        WithDetail("EntityType", entityType.Name);
        WithDetail("EntityId", entityId);
    }

    /// <summary>
    /// Initializes a new instance of the EntityAlreadyExistsException class
    /// </summary>
    /// <param name="entityType">The type of the entity that already exists</param>
    /// <param name="entityId">The identifier of the entity that already exists</param>
    /// <param name="innerException">The inner exception</param>
    public EntityAlreadyExistsException(Type entityType, object entityId, Exception innerException)
        : base($"{entityType.Name} with ID '{entityId}' already exists.", innerException, "ENTITY_ALREADY_EXISTS")
    {
        EntityType = entityType;
        EntityId = entityId;
        WithDetail("EntityType", entityType.Name);
        WithDetail("EntityId", entityId);
    }
}

/// <summary>
/// Exception thrown when a domain operation is invalid
/// </summary>
public class InvalidDomainOperationException : DomainException
{
    /// <summary>
    /// Gets the name of the operation that is invalid
    /// </summary>
    public string OperationName { get; private set; }

    /// <summary>
    /// Initializes a new instance of the InvalidDomainOperationException class
    /// </summary>
    /// <param name="operationName">The name of the operation that is invalid</param>
    /// <param name="message">The error message</param>
    public InvalidDomainOperationException(string operationName, string message)
        : base(message, "INVALID_DOMAIN_OPERATION")
    {
        OperationName = operationName;
        WithDetail("OperationName", operationName);
    }

    /// <summary>
    /// Initializes a new instance of the InvalidDomainOperationException class
    /// </summary>
    /// <param name="operationName">The name of the operation that is invalid</param>
    /// <param name="message">The error message</param>
    /// <param name="innerException">The inner exception</param>
    public InvalidDomainOperationException(string operationName, string message, Exception innerException)
        : base(message, innerException, "INVALID_DOMAIN_OPERATION")
    {
        OperationName = operationName;
        WithDetail("OperationName", operationName);
    }
}
