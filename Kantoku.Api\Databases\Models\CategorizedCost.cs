using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class CategorizedCost : AuditableEntity
{
    public Guid CategorizedCostUid { get; set; }

    public Guid ConstructionCostUid { get; set; }

    public Guid CategoryUid { get; set; }

    [AuditProperty]
    public float? Quantity { get; set; }

    [AuditProperty]
    public long TotalAmount { get; set; }

    [AuditProperty]
    public long AvgAmount { get; set; }

    [AuditProperty]
    public long FixedAmount { get; set; }

    public virtual ConstructionCost ConstructionCost { get; set; } = null!;

    public virtual Category Category { get; set; } = null!;
}

