using Kantoku.SharedKernel.Guards;

namespace Kantoku.SharedKernel.Pagination;

/// <summary>
/// Represents a request for paged data with sorting options
/// </summary>
public class PagedRequest
{
    private int _pageNumber = 1;
    private int _pageSize = 10;

    /// <summary>
    /// Gets or sets the page number (1-based)
    /// </summary>
    public int PageNumber
    {
        get => _pageNumber;
        set => _pageNumber = Guard.Positive(value);
    }

    /// <summary>
    /// Gets or sets the page size (number of items per page)
    /// </summary>
    public int PageSize
    {
        get => _pageSize;
        set => _pageSize = Guard.InRange(value, 1, 1000);
    }

    /// <summary>
    /// Gets or sets the sorting criteria
    /// </summary>
    public IList<SortCriteria> SortBy { get; set; } = [];

    /// <summary>
    /// Gets the number of items to skip
    /// </summary>
    public int Skip => (PageNumber - 1) * PageSize;

    /// <summary>
    /// Gets the number of items to take
    /// </summary>
    public int Take => PageSize;

    /// <summary>
    /// Initializes a new instance of the PagedRequest class
    /// </summary>
    public PagedRequest()
    {
    }

    /// <summary>
    /// Initializes a new instance of the PagedRequest class
    /// </summary>
    /// <param name="pageNumber">The page number (1-based)</param>
    /// <param name="pageSize">The page size</param>
    public PagedRequest(int pageNumber, int pageSize)
    {
        PageNumber = pageNumber;
        PageSize = pageSize;
    }

    /// <summary>
    /// Initializes a new instance of the PagedRequest class
    /// </summary>
    /// <param name="pageNumber">The page number (1-based)</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="sortBy">The sorting criteria</param>
    public PagedRequest(int pageNumber, int pageSize, params SortCriteria[] sortBy)
        : this(pageNumber, pageSize)
    {
        SortBy = sortBy?.ToList() ?? [];
    }

    /// <summary>
    /// Adds a sort criteria to the request
    /// </summary>
    /// <param name="propertyName">The property name to sort by</param>
    /// <param name="direction">The sort direction</param>
    /// <returns>The current PagedRequest instance for method chaining</returns>
    public PagedRequest AddSort(string propertyName, SortDirection direction = SortDirection.Ascending)
    {
        SortBy.Add(new SortCriteria(propertyName, direction));
        return this;
    }

    /// <summary>
    /// Adds ascending sort criteria to the request
    /// </summary>
    /// <param name="propertyName">The property name to sort by</param>
    /// <returns>The current PagedRequest instance for method chaining</returns>
    public PagedRequest OrderBy(string propertyName)
    {
        return AddSort(propertyName, SortDirection.Ascending);
    }

    /// <summary>
    /// Adds descending sort criteria to the request
    /// </summary>
    /// <param name="propertyName">The property name to sort by</param>
    /// <returns>The current PagedRequest instance for method chaining</returns>
    public PagedRequest OrderByDescending(string propertyName)
    {
        return AddSort(propertyName, SortDirection.Descending);
    }

    /// <summary>
    /// Clears all sorting criteria
    /// </summary>
    /// <returns>The current PagedRequest instance for method chaining</returns>
    public PagedRequest ClearSort()
    {
        SortBy.Clear();
        return this;
    }

    /// <summary>
    /// Creates a default paged request
    /// </summary>
    /// <param name="pageSize">The page size (default: 10)</param>
    /// <returns>A default paged request</returns>
    public static PagedRequest Default(int pageSize = 10)
    {
        return new PagedRequest(1, pageSize);
    }

    /// <summary>
    /// Creates a paged request for the first page
    /// </summary>
    /// <param name="pageSize">The page size</param>
    /// <returns>A paged request for the first page</returns>
    public static PagedRequest FirstPage(int pageSize)
    {
        return new PagedRequest(1, pageSize);
    }

    /// <summary>
    /// Creates a copy of the current request with a different page number
    /// </summary>
    /// <param name="pageNumber">The new page number</param>
    /// <returns>A new PagedRequest instance</returns>
    public PagedRequest WithPageNumber(int pageNumber)
    {
        return new PagedRequest(pageNumber, PageSize, SortBy.ToArray());
    }

    /// <summary>
    /// Creates a copy of the current request with a different page size
    /// </summary>
    /// <param name="pageSize">The new page size</param>
    /// <returns>A new PagedRequest instance</returns>
    public PagedRequest WithPageSize(int pageSize)
    {
        return new PagedRequest(PageNumber, pageSize, SortBy.ToArray());
    }

    /// <summary>
    /// Validates the paged request
    /// </summary>
    /// <returns>True if the request is valid; otherwise, false</returns>
    public bool IsValid()
    {
        return PageNumber > 0 && PageSize > 0 && PageSize <= 1000;
    }

    /// <summary>
    /// Gets validation errors for the paged request
    /// </summary>
    /// <returns>A collection of validation errors</returns>
    public IEnumerable<string> GetValidationErrors()
    {
        var errors = new List<string>();

        if (PageNumber <= 0)
            errors.Add("Page number must be greater than 0.");

        if (PageSize <= 0)
            errors.Add("Page size must be greater than 0.");

        if (PageSize > 1000)
            errors.Add("Page size cannot exceed 1000.");

        foreach (var sort in SortBy)
        {
            if (string.IsNullOrWhiteSpace(sort.PropertyName))
                errors.Add("Sort property name cannot be empty.");
        }

        return errors;
    }
}
