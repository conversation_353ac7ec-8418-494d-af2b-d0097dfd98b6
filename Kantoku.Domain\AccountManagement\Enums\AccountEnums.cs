using Kantoku.Domain.Common.BuildingBlocks;

namespace Kantoku.Domain.AccountManagement.Enums;

/// <summary>
/// Enumeration representing account status
/// </summary>
public class AccountStatus : Enumeration
{
    public static readonly AccountStatus Active = new(1, nameof(Active), "Active", true);
    public static readonly AccountStatus Inactive = new(2, nameof(Inactive), "Inactive", false);
    public static readonly AccountStatus Locked = new(3, nameof(Locked), "Locked", false);
    public static readonly AccountStatus Suspended = new(4, nameof(Suspended), "Suspended", false);
    public static readonly AccountStatus PendingVerification = new(5, nameof(PendingVerification), "Pending Verification", false);

    public string DisplayName { get; private set; }
    public bool CanLogin { get; private set; }

    private AccountStatus(int id, string name, string displayName, bool canLogin) : base(id, name)
    {
        DisplayName = displayName;
        CanLogin = canLogin;
    }

    public static IEnumerable<AccountStatus> GetAll()
    {
        return new[] { Active, Inactive, Locked, Suspended, PendingVerification };
    }

    public static AccountStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown account status: {name}");
        return status;
    }

    public static AccountStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown account status ID: {id}");
        return status;
    }

    public static AccountStatus FromDisplayName(string displayName)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.DisplayName, displayName, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown account status display name: {displayName}");
        return status;
    }

    /// <summary>
    /// Checks if the status allows password changes
    /// </summary>
    public bool CanChangePassword => this == Active || this == PendingVerification;

    /// <summary>
    /// Checks if the status allows profile updates
    /// </summary>
    public bool CanUpdateProfile => this == Active || this == PendingVerification;

    /// <summary>
    /// Checks if the status requires administrative action
    /// </summary>
    public bool RequiresAdminAction => this == Locked || this == Suspended;
}

/// <summary>
/// Enumeration representing session types
/// </summary>
public class SessionType : Enumeration
{
    public static readonly SessionType Web = new(1, nameof(Web), "Web Browser");
    public static readonly SessionType Mobile = new(2, nameof(Mobile), "Mobile App");
    public static readonly SessionType Api = new(3, nameof(Api), "API Access");
    public static readonly SessionType Desktop = new(4, nameof(Desktop), "Desktop Application");

    public string DisplayName { get; private set; }

    private SessionType(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<SessionType> GetAll()
    {
        return new[] { Web, Mobile, Api, Desktop };
    }

    public static SessionType FromName(string name)
    {
        var type = GetAll().FirstOrDefault(t => string.Equals(t.Name, name, StringComparison.OrdinalIgnoreCase));
        if (type == null)
            throw new ArgumentException($"Unknown session type: {name}");
        return type;
    }

    public static SessionType FromId(int id)
    {
        var type = GetAll().FirstOrDefault(t => t.Id == id);
        if (type == null)
            throw new ArgumentException($"Unknown session type ID: {id}");
        return type;
    }
}

/// <summary>
/// Enumeration representing authentication methods
/// </summary>
public class AuthenticationMethod : Enumeration
{
    public static readonly AuthenticationMethod Password = new(1, nameof(Password), "Password");
    public static readonly AuthenticationMethod TwoFactor = new(2, nameof(TwoFactor), "Two-Factor Authentication");
    public static readonly AuthenticationMethod Biometric = new(3, nameof(Biometric), "Biometric");
    public static readonly AuthenticationMethod SocialLogin = new(4, nameof(SocialLogin), "Social Login");
    public static readonly AuthenticationMethod ApiKey = new(5, nameof(ApiKey), "API Key");

    public string DisplayName { get; private set; }

    private AuthenticationMethod(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<AuthenticationMethod> GetAll()
    {
        return new[] { Password, TwoFactor, Biometric, SocialLogin, ApiKey };
    }

    public static AuthenticationMethod FromName(string name)
    {
        var method = GetAll().FirstOrDefault(m => string.Equals(m.Name, name, StringComparison.OrdinalIgnoreCase));
        if (method == null)
            throw new ArgumentException($"Unknown authentication method: {name}");
        return method;
    }

    public static AuthenticationMethod FromId(int id)
    {
        var method = GetAll().FirstOrDefault(m => m.Id == id);
        if (method == null)
            throw new ArgumentException($"Unknown authentication method ID: {id}");
        return method;
    }

    /// <summary>
    /// Checks if the method requires additional verification
    /// </summary>
    public bool RequiresAdditionalVerification => this == TwoFactor || this == Biometric;

    /// <summary>
    /// Checks if the method is considered secure
    /// </summary>
    public bool IsSecure => this != Password || this == TwoFactor || this == Biometric;
}
