using Kantoku.SharedKernel;

namespace Kantoku.Domain.ProjectManagement;

/// <summary>
/// Entity representing a project schedule entry
/// </summary>
public class ProjectSchedule : Entity<Guid>
{
    public Guid ProjectId { get; private set; }
    public string TaskName { get; private set; } = null!;
    public string? Description { get; private set; }
    public DateOnly StartDate { get; private set; }
    public DateOnly EndDate { get; private set; }
    public int EstimatedHours { get; private set; }
    public int ActualHours { get; private set; } = 0;
    public string Status { get; private set; } = "PLANNED";
    public int Priority { get; private set; } = 1;
    public Guid? AssignedEmployeeId { get; private set; }
    public bool IsDeleted { get; private set; } = false;

    // Private constructor for EF Core
    private ProjectSchedule() : base() { }

    /// <summary>
    /// Creates a new project schedule entry
    /// </summary>
    public ProjectSchedule(
        Guid id,
        Guid projectId,
        string taskName,
        DateOnly startDate,
        DateOnly endDate,
        int estimatedHours,
        string? description = null,
        int priority = 1) : base(id)
    {
        ProjectId = projectId;
        SetTaskName(taskName);
        SetDates(startDate, endDate);
        SetEstimatedHours(estimatedHours);
        Description = description;
        SetPriority(priority);
    }

    /// <summary>
    /// Updates task information
    /// </summary>
    public void UpdateTask(
        string taskName,
        string? description = null,
        int? priority = null)
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot update completed task");

        SetTaskName(taskName);
        Description = description;
        
        if (priority.HasValue)
            SetPriority(priority.Value);
    }

    /// <summary>
    /// Updates schedule dates
    /// </summary>
    public void UpdateDates(DateOnly startDate, DateOnly endDate)
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot update dates for completed task");

        SetDates(startDate, endDate);
    }

    /// <summary>
    /// Updates estimated hours
    /// </summary>
    public void UpdateEstimatedHours(int estimatedHours)
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot update estimated hours for completed task");

        SetEstimatedHours(estimatedHours);
    }

    /// <summary>
    /// Assigns an employee to the task
    /// </summary>
    public void AssignEmployee(Guid employeeId)
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot assign employee to completed task");

        AssignedEmployeeId = employeeId;
    }

    /// <summary>
    /// Removes employee assignment
    /// </summary>
    public void RemoveEmployeeAssignment()
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot remove assignment from completed task");

        AssignedEmployeeId = null;
    }

    /// <summary>
    /// Starts the task
    /// </summary>
    public void Start()
    {
        if (Status != "PLANNED")
            throw new InvalidOperationException("Task can only be started from PLANNED status");

        Status = "IN_PROGRESS";
    }

    /// <summary>
    /// Completes the task
    /// </summary>
    public void Complete(int actualHours)
    {
        if (Status != "IN_PROGRESS")
            throw new InvalidOperationException("Task can only be completed from IN_PROGRESS status");

        if (actualHours < 0)
            throw new ArgumentException("Actual hours cannot be negative");

        Status = "COMPLETED";
        ActualHours = actualHours;
    }

    /// <summary>
    /// Cancels the task
    /// </summary>
    public void Cancel()
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot cancel completed task");

        Status = "CANCELLED";
    }

    /// <summary>
    /// Resets the task to planned status
    /// </summary>
    public void Reset()
    {
        if (Status == "COMPLETED")
            throw new InvalidOperationException("Cannot reset completed task");

        Status = "PLANNED";
        ActualHours = 0;
    }

    /// <summary>
    /// Soft deletes the schedule entry
    /// </summary>
    public void Delete()
    {
        IsDeleted = true;
    }

    /// <summary>
    /// Restores the schedule entry
    /// </summary>
    public void Restore()
    {
        IsDeleted = false;
    }

    /// <summary>
    /// Checks if this schedule overlaps with another schedule
    /// </summary>
    public bool OverlapsWith(ProjectSchedule other)
    {
        if (other == null)
            return false;

        return StartDate <= other.EndDate && EndDate >= other.StartDate;
    }

    /// <summary>
    /// Gets the duration in days
    /// </summary>
    public int DurationDays => EndDate.DayNumber - StartDate.DayNumber + 1;

    /// <summary>
    /// Gets the variance in hours (actual vs estimated)
    /// </summary>
    public int HoursVariance => ActualHours - EstimatedHours;

    /// <summary>
    /// Checks if the task is overdue
    /// </summary>
    public bool IsOverdue => Status != "COMPLETED" && EndDate < DateOnly.FromDateTime(DateTime.Today);

    /// <summary>
    /// Checks if the task is completed
    /// </summary>
    public bool IsCompleted => Status == "COMPLETED";

    /// <summary>
    /// Checks if the task is in progress
    /// </summary>
    public bool IsInProgress => Status == "IN_PROGRESS";

    /// <summary>
    /// Checks if the task is planned
    /// </summary>
    public bool IsPlanned => Status == "PLANNED";

    // Private helper methods
    private void SetTaskName(string taskName)
    {
        if (string.IsNullOrWhiteSpace(taskName))
            throw new ArgumentException("Task name cannot be null or empty", nameof(taskName));

        if (taskName.Length > 200)
            throw new ArgumentException("Task name cannot exceed 200 characters", nameof(taskName));

        TaskName = taskName.Trim();
    }

    private void SetDates(DateOnly startDate, DateOnly endDate)
    {
        if (startDate > endDate)
            throw new ArgumentException("Start date cannot be after end date");

        StartDate = startDate;
        EndDate = endDate;
    }

    private void SetEstimatedHours(int estimatedHours)
    {
        if (estimatedHours < 0)
            throw new ArgumentException("Estimated hours cannot be negative", nameof(estimatedHours));

        if (estimatedHours > 10000)
            throw new ArgumentException("Estimated hours cannot exceed 10,000", nameof(estimatedHours));

        EstimatedHours = estimatedHours;
    }

    private void SetPriority(int priority)
    {
        if (priority < 1 || priority > 5)
            throw new ArgumentException("Priority must be between 1 and 5", nameof(priority));

        Priority = priority;
    }
}
