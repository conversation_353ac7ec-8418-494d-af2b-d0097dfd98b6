using Kantoku.Domain.EmployeeManagement;
using Kantoku.Domain.EmployeeManagement.Enums;
using Kantoku.Domain.EmployeeManagement.ValueObjects;
using Kantoku.Domain.OrganizationManagement;

namespace Kantoku.Domain.DomainServices;

/// <summary>
/// Domain service for complex employee business logic
/// </summary>
public class EmployeeDomainService
{
    /// <summary>
    /// Calculates effective salary based on employee ranking and organization settings
    /// </summary>
    public SalaryInfo CalculateEffectiveSalary(Employee employee, Ranking? ranking, Organization organization)
    {
        if (employee == null)
            throw new ArgumentNullException(nameof(employee));

        if (organization == null)
            throw new ArgumentNullException(nameof(organization));

        var baseSalary = employee.SalaryInfo;

        // If no ranking or organization doesn't use ranking-based salary, return base salary
        if (ranking == null || organization.EmployeeRankingDefinitionType != "INDIRECT")
            return baseSalary;

        // Calculate salary based on ranking
        var monthlySalary = ranking.BaseSalary > 0 ? ranking.BaseSalary : baseSalary.SalaryInMonth;
        var dailySalary = CalculateDailySalary(monthlySalary);
        var hourlySalary = CalculateHourlySalary(monthlySalary, employee.StandardWorkingHours);

        return new SalaryInfo(monthlySalary, dailySalary, hourlySalary, baseSalary.Currency);
    }

    /// <summary>
    /// Determines if an employee can approve requests based on their authority and the request type
    /// </summary>
    public bool CanApproveRequest(Employee employee, string requestType, decimal? requestAmount = null)
    {
        if (employee == null)
            throw new ArgumentNullException(nameof(employee));

        if (!employee.HasApprovalAuthority)
            return false;

        if (employee.WorkingStatus != WorkingStatus.Active)
            return false;

        // Additional business rules based on request type and amount
        return requestType switch
        {
            "LEAVE" => true, // All employees with approval authority can approve leave
            "OVERTIME" => true, // All employees with approval authority can approve overtime
            "EXPENSE" => CanApproveExpense(employee, requestAmount),
            "EQUIPMENT" => employee.IsOrgAdmin, // Only org admins can approve equipment requests
            "TRAVEL" => employee.IsOrgAdmin, // Only org admins can approve travel requests
            _ => employee.IsOrgAdmin // Default: only org admins for unknown types
        };
    }

    /// <summary>
    /// Calculates overtime pay for an employee
    /// </summary>
    public decimal CalculateOvertimePay(Employee employee, decimal overtimeHours, Ranking? ranking = null)
    {
        if (employee == null)
            throw new ArgumentNullException(nameof(employee));

        if (overtimeHours <= 0)
            return 0;

        var effectiveSalary = ranking != null 
            ? new SalaryInfo(ranking.BaseSalary, 0, 0, employee.SalaryInfo.Currency)
            : employee.SalaryInfo;

        var hourlyRate = effectiveSalary.SalaryInHour > 0 
            ? effectiveSalary.SalaryInHour 
            : CalculateHourlyRate(effectiveSalary.SalaryInMonth, employee.StandardWorkingHours);

        // Standard overtime rate is 1.25x for first 2 hours, 1.5x for additional hours
        var overtimePay = 0m;
        var remainingHours = overtimeHours;

        // First 2 hours at 1.25x
        var firstTierHours = Math.Min(remainingHours, 2);
        overtimePay += firstTierHours * hourlyRate * 1.25m;
        remainingHours -= firstTierHours;

        // Additional hours at 1.5x
        if (remainingHours > 0)
        {
            overtimePay += remainingHours * hourlyRate * 1.5m;
        }

        return Math.Round(overtimePay, 0); // Round to nearest yen
    }

    /// <summary>
    /// Validates if an employee can be assigned to a specific organizational structure
    /// </summary>
    public bool CanAssignToStructure(Employee employee, Structure structure, Position? position = null)
    {
        if (employee == null)
            throw new ArgumentNullException(nameof(employee));

        if (structure == null)
            throw new ArgumentNullException(nameof(structure));

        // Check if employee belongs to the same organization
        if (employee.OrgId != structure.OrgId)
            return false;

        // Check if structure is active
        if (!structure.IsActive)
            return false;

        // Check if position is compatible with structure (if provided)
        if (position != null && position.StructureId != structure.Id)
            return false;

        // Additional business rules can be added here
        return true;
    }

    /// <summary>
    /// Determines the next approver in the hierarchy for a request
    /// </summary>
    public async Task<Employee?> GetNextApproverAsync(
        Employee requestingEmployee, 
        string requestType, 
        decimal? requestAmount,
        IEmployeeRepository employeeRepository)
    {
        if (requestingEmployee == null)
            throw new ArgumentNullException(nameof(requestingEmployee));

        if (employeeRepository == null)
            throw new ArgumentNullException(nameof(employeeRepository));

        // Get employees in the same organization with approval authority
        var potentialApprovers = await employeeRepository.GetByOrganizationIdAsync(requestingEmployee.OrgId);
        var approvers = potentialApprovers
            .Where(e => e.HasApprovalAuthority && 
                       e.WorkingStatus == WorkingStatus.Active &&
                       e.Id != requestingEmployee.Id &&
                       CanApproveRequest(e, requestType, requestAmount))
            .ToList();

        if (!approvers.Any())
            return null;

        // Priority order: Direct supervisor -> Org Admin -> Any approver
        // For now, return the first org admin or any approver
        return approvers.FirstOrDefault(a => a.IsOrgAdmin) ?? approvers.First();
    }

    // Private helper methods
    private bool CanApproveExpense(Employee employee, decimal? amount)
    {
        if (!amount.HasValue)
            return true;

        // Business rule: Regular employees can approve up to 50,000 yen
        // Org admins can approve any amount
        return employee.IsOrgAdmin || amount.Value <= 50000;
    }

    private int CalculateDailySalary(int monthlySalary)
    {
        // Assuming 22 working days per month
        return (int)Math.Round(monthlySalary / 22.0, 0);
    }

    private int CalculateHourlySalary(int monthlySalary, float standardWorkingHours)
    {
        // Assuming 22 working days per month
        var totalMonthlyHours = 22 * standardWorkingHours;
        return (int)Math.Round(monthlySalary / totalMonthlyHours, 0);
    }

    private decimal CalculateHourlyRate(int monthlySalary, float standardWorkingHours)
    {
        // Assuming 22 working days per month
        var totalMonthlyHours = 22 * standardWorkingHours;
        return monthlySalary / (decimal)totalMonthlyHours;
    }
}
