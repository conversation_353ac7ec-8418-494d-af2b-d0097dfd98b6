﻿namespace Kantoku.Domain.Models;

public class EmpContract : AuditableEntity
{
    public Guid EmpContractUid { get; set; }

    public string EmpContractCode { get; set; } = null!;

    public string EmpContractName { get; set; } = null!;
    
    public Guid EmployeeUid { get; set; }

    public string? Description { get; set; }

    public DateTime? ExpireTime { get; set; }

    public bool IsDeleted { get; set; } = false;

    public bool? Status { get; set; }

    public virtual Employee Employee { get; set; } = null!;
}
