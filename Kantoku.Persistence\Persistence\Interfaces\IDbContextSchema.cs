namespace Kantoku.Persistence.Interfaces
{
    /// <summary>
    /// Interface to define the schema for a DbContext.
    /// This can be useful for multi-tenant applications or modular monoliths
    /// where different modules might have their own schemas.
    /// </summary>
    public interface IDbContextSchema
    {
        /// <summary>
        /// Gets the database schema name.
        /// </summary>
        string Schema { get; }
    }
} 