using Kantoku.Domain.AccountManagement.Enums;

namespace Kantoku.Domain.AccountManagement;

/// <summary>
/// Repository interface for Account aggregate
/// </summary>
public interface IAccountRepository
{
    /// <summary>
    /// Gets an account by ID
    /// </summary>
    Task<Account?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets an account by username
    /// </summary>
    Task<Account?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets an account by email
    /// </summary>
    Task<Account?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets accounts by status
    /// </summary>
    Task<IEnumerable<Account>> GetByStatusAsync(AccountStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets accounts with expiring passwords
    /// </summary>
    Task<IEnumerable<Account>> GetAccountsWithExpiringPasswordsAsync(
        int daysUntilExpiry = 30, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets accounts with failed login attempts
    /// </summary>
    Task<IEnumerable<Account>> GetAccountsWithFailedLoginsAsync(
        int minimumFailedAttempts = 3, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets locked accounts
    /// </summary>
    Task<IEnumerable<Account>> GetLockedAccountsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets accounts by role
    /// </summary>
    Task<IEnumerable<Account>> GetByRoleAsync(Guid roleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets accounts by organization and role
    /// </summary>
    Task<IEnumerable<Account>> GetByOrganizationAndRoleAsync(
        Guid orgId, 
        Guid roleId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches accounts by username or email
    /// </summary>
    Task<IEnumerable<Account>> SearchAsync(
        string searchTerm, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets accounts with pagination
    /// </summary>
    Task<(IEnumerable<Account> Accounts, int TotalCount)> GetPagedAsync(
        int pageNumber = 1,
        int pageSize = 20,
        AccountStatus? status = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a username exists
    /// </summary>
    Task<bool> UsernameExistsAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a username exists for a different account
    /// </summary>
    Task<bool> UsernameExistsAsync(string username, Guid excludeAccountId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an email exists
    /// </summary>
    Task<bool> EmailExistsAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an email exists for a different account
    /// </summary>
    Task<bool> EmailExistsAsync(string email, Guid excludeAccountId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new account
    /// </summary>
    Task AddAsync(Account account, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing account
    /// </summary>
    Task UpdateAsync(Account account, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes an account
    /// </summary>
    Task RemoveAsync(Account account, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets account statistics
    /// </summary>
    Task<AccountStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for Role aggregate
/// </summary>
public interface IRoleRepository
{
    /// <summary>
    /// Gets a role by ID
    /// </summary>
    Task<Role?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a role by name
    /// </summary>
    Task<Role?> GetByNameAsync(string roleName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets roles by organization
    /// </summary>
    Task<IEnumerable<Role>> GetByOrganizationAsync(Guid? orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets system roles
    /// </summary>
    Task<IEnumerable<Role>> GetSystemRolesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active roles
    /// </summary>
    Task<IEnumerable<Role>> GetActiveRolesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all roles
    /// </summary>
    Task<IEnumerable<Role>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a role name exists
    /// </summary>
    Task<bool> NameExistsAsync(string roleName, Guid? orgId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a role name exists for a different role
    /// </summary>
    Task<bool> NameExistsAsync(string roleName, Guid excludeRoleId, Guid? orgId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new role
    /// </summary>
    Task AddAsync(Role role, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing role
    /// </summary>
    Task UpdateAsync(Role role, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes a role
    /// </summary>
    Task RemoveAsync(Role role, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for Function aggregate
/// </summary>
public interface IFunctionRepository
{
    /// <summary>
    /// Gets a function by ID
    /// </summary>
    Task<Function?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a function by code
    /// </summary>
    Task<Function?> GetByCodeAsync(string functionCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets functions by category
    /// </summary>
    Task<IEnumerable<Function>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets system functions
    /// </summary>
    Task<IEnumerable<Function>> GetSystemFunctionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active functions
    /// </summary>
    Task<IEnumerable<Function>> GetActiveFunctionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all functions
    /// </summary>
    Task<IEnumerable<Function>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets functions by role
    /// </summary>
    Task<IEnumerable<Function>> GetByRoleAsync(Guid roleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a function code exists
    /// </summary>
    Task<bool> CodeExistsAsync(string functionCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a function code exists for a different function
    /// </summary>
    Task<bool> CodeExistsAsync(string functionCode, Guid excludeFunctionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new function
    /// </summary>
    Task AddAsync(Function function, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing function
    /// </summary>
    Task UpdateAsync(Function function, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes a function
    /// </summary>
    Task RemoveAsync(Function function, CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistics about accounts
/// </summary>
public class AccountStatistics
{
    public int TotalAccounts { get; set; }
    public int ActiveAccounts { get; set; }
    public int InactiveAccounts { get; set; }
    public int LockedAccounts { get; set; }
    public int SuspendedAccounts { get; set; }
    public int AccountsWithTwoFactor { get; set; }
    public int AccountsWithFailedLogins { get; set; }
    public Dictionary<string, int> AccountsByRole { get; set; } = new();
    public Dictionary<string, int> LoginsByDay { get; set; } = new();
}
