namespace Kantoku.SharedKernel.BuildingBlocks;

/// <summary>
/// Base class for entities with Guid identifiers.
/// Entities are objects that have a distinct identity that runs through time and different representations.
/// </summary>
public abstract class Entity : HasDomainEventsBase
{
    /// <summary>
    /// Gets the unique identifier for this entity
    /// </summary>
    public Guid Id { get; protected set; }

    /// <summary>
    /// Initializes a new instance of the Entity class with a new Guid
    /// </summary>
    protected Entity()
    {
        Id = Guid.NewGuid();
    }

    /// <summary>
    /// Initializes a new instance of the Entity class with the specified identifier
    /// </summary>
    /// <param name="id">The unique identifier for this entity</param>
    protected Entity(Guid id)
    {
        if (id == Guid.Empty)
        {
            throw new ArgumentException("The ID cannot be empty.", nameof(id));
        }
        Id = id;
    }

    /// <summary>
    /// Determines whether this entity is transient (not yet persisted)
    /// </summary>
    /// <returns>True if the entity is transient; otherwise, false</returns>
    public bool IsTransient() => Id == Guid.Empty;

    /// <summary>
    /// Determines whether this entity is equal to the specified object
    /// </summary>
    /// <param name="obj">The object to compare</param>
    /// <returns>True if the entities are equal; otherwise, false</returns>
    public override bool Equals(object? obj)
    {
        if (obj == null || obj is not Entity)
            return false;

        if (ReferenceEquals(this, obj))
            return true;

        if (GetType() != obj.GetType())
            return false;

        var item = (Entity)obj;

        if (item.IsTransient() || IsTransient())
            return false;

        return item.Id == Id;
    }

    /// <summary>
    /// Returns the hash code for this entity
    /// </summary>
    /// <returns>A hash code for this entity</returns>
    public override int GetHashCode()
    {
        if (IsTransient())
            return base.GetHashCode();

        return Id.GetHashCode();
    }

    /// <summary>
    /// Determines whether two entities are equal
    /// </summary>
    /// <param name="left">The first entity</param>
    /// <param name="right">The second entity</param>
    /// <returns>True if the entities are equal; otherwise, false</returns>
    public static bool operator ==(Entity? left, Entity? right)
    {
        if (Equals(left, null))
            return Equals(right, null);

        return left.Equals(right);
    }

    /// <summary>
    /// Determines whether two entities are not equal
    /// </summary>
    /// <param name="left">The first entity</param>
    /// <param name="right">The second entity</param>
    /// <returns>True if the entities are not equal; otherwise, false</returns>
    public static bool operator !=(Entity? left, Entity? right)
    {
        return !(left == right);
    }
}
