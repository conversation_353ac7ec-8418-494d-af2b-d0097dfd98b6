using Kantoku.SharedKernel;
using Kantoku.Domain.CustomerManagement.Enums;

namespace Kantoku.Domain.CustomerManagement;

/// <summary>
/// Entity representing a contact person for a customer
/// </summary>
public class CustomerContact : Entity<Guid>
{
    public Guid CustomerId { get; private set; }
    public string FirstName { get; private set; } = null!;
    public string LastName { get; private set; } = null!;
    public string? MiddleName { get; private set; }
    public string? Title { get; private set; }
    public string? Department { get; private set; }
    public ContactType ContactType { get; private set; } = ContactType.Other;
    public string Email { get; private set; } = null!;
    public string? PhoneNumber { get; private set; }
    public string? MobileNumber { get; private set; }
    public string? Fax { get; private set; }
    public bool IsPrimary { get; private set; } = false;
    public bool IsActive { get; private set; } = true;
    public string? Notes { get; private set; }

    // Private constructor for EF Core
    private CustomerContact() : base() { }

    /// <summary>
    /// Creates a new customer contact
    /// </summary>
    public CustomerContact(
        Guid id,
        Guid customerId,
        string firstName,
        string lastName,
        string email,
        ContactType contactType = null,
        string? middleName = null,
        string? title = null,
        string? department = null,
        string? phoneNumber = null,
        string? mobileNumber = null,
        string? fax = null,
        bool isPrimary = false,
        string? notes = null) : base(id)
    {
        CustomerId = customerId;
        SetFirstName(firstName);
        SetLastName(lastName);
        SetEmail(email);
        ContactType = contactType ?? ContactType.Other;
        MiddleName = middleName?.Trim();
        Title = title?.Trim();
        Department = department?.Trim();
        PhoneNumber = phoneNumber?.Trim();
        MobileNumber = mobileNumber?.Trim();
        Fax = fax?.Trim();
        IsPrimary = isPrimary;
        Notes = notes?.Trim();
    }

    /// <summary>
    /// Updates contact basic information
    /// </summary>
    public void UpdateBasicInfo(
        string firstName,
        string lastName,
        string? middleName = null,
        string? title = null,
        string? department = null)
    {
        SetFirstName(firstName);
        SetLastName(lastName);
        MiddleName = middleName?.Trim();
        Title = title?.Trim();
        Department = department?.Trim();
    }

    /// <summary>
    /// Updates contact information
    /// </summary>
    public void UpdateContactInfo(
        string email,
        string? phoneNumber = null,
        string? mobileNumber = null,
        string? fax = null)
    {
        SetEmail(email);
        PhoneNumber = phoneNumber?.Trim();
        MobileNumber = mobileNumber?.Trim();
        Fax = fax?.Trim();
    }

    /// <summary>
    /// Updates contact type
    /// </summary>
    public void UpdateContactType(ContactType contactType)
    {
        ContactType = contactType ?? throw new ArgumentNullException(nameof(contactType));
    }

    /// <summary>
    /// Sets as primary contact
    /// </summary>
    public void SetAsPrimary()
    {
        IsPrimary = true;
    }

    /// <summary>
    /// Removes primary status
    /// </summary>
    public void RemovePrimaryStatus()
    {
        IsPrimary = false;
    }

    /// <summary>
    /// Activates the contact
    /// </summary>
    public void Activate()
    {
        IsActive = true;
    }

    /// <summary>
    /// Deactivates the contact
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
        IsPrimary = false; // Cannot be primary if inactive
    }

    /// <summary>
    /// Updates notes
    /// </summary>
    public void UpdateNotes(string? notes)
    {
        Notes = notes?.Trim();
    }

    /// <summary>
    /// Gets the full name of the contact
    /// </summary>
    public string GetFullName()
    {
        var parts = new List<string> { FirstName };
        
        if (!string.IsNullOrEmpty(MiddleName))
            parts.Add(MiddleName);
            
        parts.Add(LastName);
        
        return string.Join(" ", parts);
    }

    /// <summary>
    /// Gets the display name with title
    /// </summary>
    public string GetDisplayName()
    {
        var name = GetFullName();
        
        if (!string.IsNullOrEmpty(Title))
            return $"{Title} {name}";
            
        return name;
    }

    /// <summary>
    /// Checks if the contact has phone contact information
    /// </summary>
    public bool HasPhoneContact => !string.IsNullOrEmpty(PhoneNumber) || !string.IsNullOrEmpty(MobileNumber);

    /// <summary>
    /// Gets the preferred phone number (mobile first, then phone)
    /// </summary>
    public string? GetPreferredPhone()
    {
        return !string.IsNullOrEmpty(MobileNumber) ? MobileNumber : PhoneNumber;
    }

    // Private helper methods
    private void SetFirstName(string firstName)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be null or empty", nameof(firstName));

        if (firstName.Length > 100)
            throw new ArgumentException("First name cannot exceed 100 characters", nameof(firstName));

        FirstName = firstName.Trim();
    }

    private void SetLastName(string lastName)
    {
        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be null or empty", nameof(lastName));

        if (lastName.Length > 100)
            throw new ArgumentException("Last name cannot exceed 100 characters", nameof(lastName));

        LastName = lastName.Trim();
    }

    private void SetEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be null or empty", nameof(email));

        if (email.Length > 100)
            throw new ArgumentException("Email cannot exceed 100 characters", nameof(email));

        if (!IsValidEmail(email))
            throw new ArgumentException("Invalid email format", nameof(email));

        Email = email.Trim().ToLowerInvariant();
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    public override string ToString()
    {
        return $"{GetDisplayName()} ({Email})";
    }
}
