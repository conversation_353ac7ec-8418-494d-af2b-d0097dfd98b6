using System;
// Assuming Account model is in Kantoku.Domain/Models or accessible
// using Kantoku.Domain.Models; 

namespace Kantoku.Domain.Models;

public class AuditLog
{
    public Guid AuditLogUid { get; set; }
    public string EntityType { get; set; } = null!; // Renamed from EntityName
    public string EntityId { get; set; } = null!; // Represents the primary key(s) as a string (e.g., JSON for composite keys)
    public string ActionType { get; set; } = null!; // e.g., "CREATE", "UPDATE", "SOFT_DELETE", "DELETE"
    public DateTime TimestampUtc { get; set; } // Renamed from Timestamp, ensure UTC
    
    // Stores a JSON serialized representation of changes.
    // Example for UPDATE: { "Old": { "Prop1": "val" }, "New": { "Prop1": "new_val" } }
    // Example for CREATE: { "New": { "Prop1": "val" } }
    // Example for DELETE/SOFT_DELETE: { "Old": { "Prop1": "val" } } (New might contain IsDeleted flags for soft delete)
    public string? Changes { get; set; } // Replaces OldValues and NewValues

    public Guid? UserId { get; set; } // Renamed from AccountUid, represents the user performing the action

    // Navigation property to the Account entity (assuming Account is the user representation)
    // Ensure the Account entity is defined and accessible from Kantoku.Domain.Models
    public virtual Account? User { get; set; } 
}
