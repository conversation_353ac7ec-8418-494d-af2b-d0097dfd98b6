using Kantoku.Domain.RequestManagement.Enums;
using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.RequestManagement;

/// <summary>
/// Entity representing an approval step for a request
/// </summary>
public class RequestApproval : Entity<Guid>
{
    public Guid RequestId { get; private set; }
    public Guid ApproverId { get; private set; }
    public int ApprovalOrder { get; private set; }
    public ApprovalStatus Status { get; private set; } = ApprovalStatus.Pending;
    public DateTime AssignedDate { get; private set; }
    public DateTime? ResponseDate { get; private set; }
    public string? Comments { get; private set; }
    public bool IsRequired { get; private set; } = true;

    // Private constructor for EF Core
    private RequestApproval() : base() { }

    /// <summary>
    /// Creates a new request approval
    /// </summary>
    public RequestApproval(
        Guid id,
        Guid requestId,
        Guid approverId,
        int approvalOrder,
        bool isRequired = true,
        DateTime? assignedDate = null) : base(id)
    {
        RequestId = requestId;
        ApproverId = approverId;
        SetApprovalOrder(approvalOrder);
        IsRequired = isRequired;
        AssignedDate = assignedDate ?? DateTime.UtcNow;
    }

    /// <summary>
    /// Approves the request
    /// </summary>
    public void Approve(string? comments = null)
    {
        if (Status != ApprovalStatus.Pending)
            throw new InvalidOperationException("Can only approve pending approvals");

        Status = ApprovalStatus.Approved;
        ResponseDate = DateTime.UtcNow;
        Comments = comments;
    }

    /// <summary>
    /// Rejects the request
    /// </summary>
    public void Reject(string? comments = null)
    {
        if (Status != ApprovalStatus.Pending)
            throw new InvalidOperationException("Can only reject pending approvals");

        Status = ApprovalStatus.Rejected;
        ResponseDate = DateTime.UtcNow;
        Comments = comments;
    }

    /// <summary>
    /// Resets the approval to pending status
    /// </summary>
    public void Reset()
    {
        if (Status == ApprovalStatus.Pending)
            throw new InvalidOperationException("Approval is already pending");

        Status = ApprovalStatus.Pending;
        ResponseDate = null;
        Comments = null;
    }

    /// <summary>
    /// Updates the approval comments
    /// </summary>
    public void UpdateComments(string? comments)
    {
        if (Status == ApprovalStatus.Pending)
            throw new InvalidOperationException("Cannot update comments for pending approval");

        Comments = comments;
    }

    /// <summary>
    /// Checks if the approval is overdue (more than 3 days without response)
    /// </summary>
    public bool IsOverdue => Status == ApprovalStatus.Pending && 
                            AssignedDate.AddDays(3) < DateTime.UtcNow;

    /// <summary>
    /// Gets the number of days since assignment
    /// </summary>
    public int DaysSinceAssignment => (DateTime.UtcNow.Date - AssignedDate.Date).Days;

    /// <summary>
    /// Gets the response time in hours (if responded)
    /// </summary>
    public double? ResponseTimeHours
    {
        get
        {
            if (!ResponseDate.HasValue)
                return null;
            
            return (ResponseDate.Value - AssignedDate).TotalHours;
        }
    }

    // Private helper methods
    private void SetApprovalOrder(int approvalOrder)
    {
        if (approvalOrder < 1)
            throw new ArgumentException("Approval order must be greater than 0", nameof(approvalOrder));

        if (approvalOrder > 10)
            throw new ArgumentException("Approval order cannot exceed 10", nameof(approvalOrder));

        ApprovalOrder = approvalOrder;
    }
}
