using Kantoku.SharedKernel;

namespace Kantoku.Domain.EmployeeManagement;

/// <summary>
/// Entity representing an employee's work shift
/// </summary>
public class EmployeeShift : Entity<Guid>
{
    public Guid EmployeeId { get; private set; }
    public Guid ProjectId { get; private set; }
    public DateOnly WorkDate { get; private set; }
    public TimeOnly? StartTime { get; private set; }
    public TimeOnly? EndTime { get; private set; }
    public TimeSpan? BreakDuration { get; private set; }
    public decimal WorkingHours { get; private set; }
    public string? Description { get; private set; }
    public string Status { get; private set; } = "PENDING";
    public Guid? ApprovedBy { get; private set; }
    public DateTime? ApprovedDate { get; private set; }

    // Private constructor for EF Core
    private EmployeeShift() : base() { }

    /// <summary>
    /// Creates a new employee shift
    /// </summary>
    public EmployeeShift(
        Guid id,
        Guid employeeId,
        Guid projectId,
        DateOnly workDate,
        TimeOnly? startTime = null,
        TimeOnly? endTime = null,
        TimeSpan? breakDuration = null,
        string? description = null) : base(id)
    {
        EmployeeId = employeeId;
        ProjectId = projectId;
        WorkDate = workDate;
        StartTime = startTime;
        EndTime = endTime;
        BreakDuration = breakDuration ?? TimeSpan.Zero;
        Description = description;
        
        CalculateWorkingHours();
    }

    /// <summary>
    /// Updates shift times
    /// </summary>
    public void UpdateTimes(TimeOnly? startTime, TimeOnly? endTime, TimeSpan? breakDuration = null)
    {
        if (Status == "APPROVED")
            throw new InvalidOperationException("Cannot update times for approved shift");

        StartTime = startTime;
        EndTime = endTime;
        BreakDuration = breakDuration ?? BreakDuration;
        
        CalculateWorkingHours();
    }

    /// <summary>
    /// Updates working hours directly (for manual entry)
    /// </summary>
    public void UpdateWorkingHours(decimal workingHours)
    {
        if (Status == "APPROVED")
            throw new InvalidOperationException("Cannot update working hours for approved shift");

        if (workingHours < 0)
            throw new ArgumentException("Working hours cannot be negative");

        if (workingHours > 24)
            throw new ArgumentException("Working hours cannot exceed 24 hours");

        WorkingHours = workingHours;
    }

    /// <summary>
    /// Approves the shift
    /// </summary>
    public void Approve(Guid approvedBy)
    {
        if (Status == "APPROVED")
            throw new InvalidOperationException("Shift is already approved");

        Status = "APPROVED";
        ApprovedBy = approvedBy;
        ApprovedDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Rejects the shift
    /// </summary>
    public void Reject(Guid rejectedBy)
    {
        if (Status == "APPROVED")
            throw new InvalidOperationException("Cannot reject approved shift");

        Status = "REJECTED";
        ApprovedBy = rejectedBy;
        ApprovedDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Checks if the shift is approved
    /// </summary>
    public bool IsApproved => Status == "APPROVED";

    /// <summary>
    /// Checks if the shift is pending
    /// </summary>
    public bool IsPending => Status == "PENDING";

    /// <summary>
    /// Checks if the shift is rejected
    /// </summary>
    public bool IsRejected => Status == "REJECTED";

    // Private helper methods
    private void CalculateWorkingHours()
    {
        if (StartTime.HasValue && EndTime.HasValue)
        {
            var duration = EndTime.Value.ToTimeSpan() - StartTime.Value.ToTimeSpan();
            
            // Handle overnight shifts
            if (duration < TimeSpan.Zero)
                duration += TimeSpan.FromDays(1);
            
            // Subtract break duration
            duration -= BreakDuration ?? TimeSpan.Zero;
            
            WorkingHours = Math.Max(0, (decimal)duration.TotalHours);
        }
    }
}
