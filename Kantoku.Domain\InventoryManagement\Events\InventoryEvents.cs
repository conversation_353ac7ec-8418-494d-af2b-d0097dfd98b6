using Kantoku.SharedKernel;
using Kantoku.Domain.InventoryManagement.Enums;

namespace Kantoku.Domain.InventoryManagement.Events;

/// <summary>
/// Domain event raised when an item is created
/// </summary>
public class ItemCreatedEvent : IDomainEvent
{
    public Item Item { get; }
    public DateTime OccurredOn { get; }

    public ItemCreatedEvent(Item item)
    {
        Item = item;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an item is updated
/// </summary>
public class ItemUpdatedEvent : IDomainEvent
{
    public Item Item { get; }
    public DateTime OccurredOn { get; }

    public ItemUpdatedEvent(Item item)
    {
        Item = item;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when item specifications are updated
/// </summary>
public class ItemSpecificationsUpdatedEvent : IDomainEvent
{
    public Item Item { get; }
    public DateTime OccurredOn { get; }

    public ItemSpecificationsUpdatedEvent(Item item)
    {
        Item = item;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when item stock settings are updated
/// </summary>
public class ItemStockSettingsUpdatedEvent : IDomainEvent
{
    public Item Item { get; }
    public DateTime OccurredOn { get; }

    public ItemStockSettingsUpdatedEvent(Item item)
    {
        Item = item;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when item storage information is updated
/// </summary>
public class ItemStorageInfoUpdatedEvent : IDomainEvent
{
    public Item Item { get; }
    public DateTime OccurredOn { get; }

    public ItemStorageInfoUpdatedEvent(Item item)
    {
        Item = item;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when item properties are updated
/// </summary>
public class ItemPropertiesUpdatedEvent : IDomainEvent
{
    public Item Item { get; }
    public DateTime OccurredOn { get; }

    public ItemPropertiesUpdatedEvent(Item item)
    {
        Item = item;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when item status changes
/// </summary>
public class ItemStatusChangedEvent : IDomainEvent
{
    public Item Item { get; }
    public ItemStatus OldStatus { get; }
    public ItemStatus NewStatus { get; }
    public DateTime OccurredOn { get; }

    public ItemStatusChangedEvent(Item item, ItemStatus oldStatus, ItemStatus newStatus)
    {
        Item = item;
        OldStatus = oldStatus;
        NewStatus = newStatus;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when stock is added to an item
/// </summary>
public class ItemStockAddedEvent : IDomainEvent
{
    public Item Item { get; }
    public ItemStock StockMovement { get; }
    public DateTime OccurredOn { get; }

    public ItemStockAddedEvent(Item item, ItemStock stockMovement)
    {
        Item = item;
        StockMovement = stockMovement;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when stock is removed from an item
/// </summary>
public class ItemStockRemovedEvent : IDomainEvent
{
    public Item Item { get; }
    public ItemStock StockMovement { get; }
    public DateTime OccurredOn { get; }

    public ItemStockRemovedEvent(Item item, ItemStock stockMovement)
    {
        Item = item;
        StockMovement = stockMovement;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when stock is adjusted for an item
/// </summary>
public class ItemStockAdjustedEvent : IDomainEvent
{
    public Item Item { get; }
    public ItemStock StockMovement { get; }
    public DateTime OccurredOn { get; }

    public ItemStockAdjustedEvent(Item item, ItemStock stockMovement)
    {
        Item = item;
        StockMovement = stockMovement;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an item reaches its reorder point
/// </summary>
public class ItemReorderPointReachedEvent : IDomainEvent
{
    public Item Item { get; }
    public decimal CurrentStock { get; }
    public decimal ReorderPoint { get; }
    public DateTime OccurredOn { get; }

    public ItemReorderPointReachedEvent(Item item)
    {
        Item = item;
        CurrentStock = item.CurrentStock;
        ReorderPoint = item.ReorderPoint;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a price is added to an item
/// </summary>
public class ItemPriceAddedEvent : IDomainEvent
{
    public Item Item { get; }
    public ItemPrice Price { get; }
    public DateTime OccurredOn { get; }

    public ItemPriceAddedEvent(Item item, ItemPrice price)
    {
        Item = item;
        Price = price;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a category is created
/// </summary>
public class CategoryCreatedEvent : IDomainEvent
{
    public Category Category { get; }
    public DateTime OccurredOn { get; }

    public CategoryCreatedEvent(Category category)
    {
        Category = category;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a category is updated
/// </summary>
public class CategoryUpdatedEvent : IDomainEvent
{
    public Category Category { get; }
    public DateTime OccurredOn { get; }

    public CategoryUpdatedEvent(Category category)
    {
        Category = category;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a category is activated
/// </summary>
public class CategoryActivatedEvent : IDomainEvent
{
    public Category Category { get; }
    public DateTime OccurredOn { get; }

    public CategoryActivatedEvent(Category category)
    {
        Category = category;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a category is deactivated
/// </summary>
public class CategoryDeactivatedEvent : IDomainEvent
{
    public Category Category { get; }
    public DateTime OccurredOn { get; }

    public CategoryDeactivatedEvent(Category category)
    {
        Category = category;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a category hierarchy changes
/// </summary>
public class CategoryHierarchyChangedEvent : IDomainEvent
{
    public Category Category { get; }
    public Guid? OldParentId { get; }
    public Guid? NewParentId { get; }
    public DateTime OccurredOn { get; }

    public CategoryHierarchyChangedEvent(Category category, Guid? oldParentId, Guid? newParentId)
    {
        Category = category;
        OldParentId = oldParentId;
        NewParentId = newParentId;
        OccurredOn = DateTime.UtcNow;
    }
}
