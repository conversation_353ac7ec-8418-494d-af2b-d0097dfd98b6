﻿namespace Kantoku.Domain.Models;

public class RequestType
{
    public string RequestTypeCode { get; set; } = null!;

    public IEnumerable<TranslatedRequestType> TranslatedRequestType { get; set; } = [];

    public bool RequiredLevel1Approval { get; set; }

    public bool RequiredLevel2Approval { get; set; }

    public virtual ICollection<Request> Requests { get; set; } = [];
}

public class TranslatedRequestType
{
    public string LanguageCode { get; set; } = null!;

    public string RequestTypeName { get; set; } = null!;
}

