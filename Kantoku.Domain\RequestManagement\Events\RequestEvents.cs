using Kantoku.SharedKernel;

namespace Kantoku.Domain.RequestManagement.Events;

/// <summary>
/// Domain event raised when a request is created
/// </summary>
public class RequestCreatedEvent : IDomainEvent
{
    public Request Request { get; }
    public DateTime OccurredOn { get; }

    public RequestCreatedEvent(Request request)
    {
        Request = request;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a request is updated
/// </summary>
public class RequestUpdatedEvent : IDomainEvent
{
    public Request Request { get; }
    public DateTime OccurredOn { get; }

    public RequestUpdatedEvent(Request request)
    {
        Request = request;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when request dates are updated
/// </summary>
public class RequestDatesUpdatedEvent : IDomainEvent
{
    public Request Request { get; }
    public DateTime OccurredOn { get; }

    public RequestDatesUpdatedEvent(Request request)
    {
        Request = request;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when request details are updated
/// </summary>
public class RequestDetailsUpdatedEvent : IDomainEvent
{
    public Request Request { get; }
    public DateTime OccurredOn { get; }

    public RequestDetailsUpdatedEvent(Request request)
    {
        Request = request;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a request is submitted
/// </summary>
public class RequestSubmittedEvent : IDomainEvent
{
    public Request Request { get; }
    public DateTime OccurredOn { get; }

    public RequestSubmittedEvent(Request request)
    {
        Request = request;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a request is withdrawn
/// </summary>
public class RequestWithdrawnEvent : IDomainEvent
{
    public Request Request { get; }
    public DateTime OccurredOn { get; }

    public RequestWithdrawnEvent(Request request)
    {
        Request = request;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an approval is added to a request
/// </summary>
public class RequestApprovalAddedEvent : IDomainEvent
{
    public Request Request { get; }
    public RequestApproval Approval { get; }
    public DateTime OccurredOn { get; }

    public RequestApprovalAddedEvent(Request request, RequestApproval approval)
    {
        Request = request;
        Approval = approval;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a request is approved by an approver
/// </summary>
public class RequestApprovedEvent : IDomainEvent
{
    public Request Request { get; }
    public RequestApproval Approval { get; }
    public DateTime OccurredOn { get; }

    public RequestApprovedEvent(Request request, RequestApproval approval)
    {
        Request = request;
        Approval = approval;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a request is rejected by an approver
/// </summary>
public class RequestRejectedEvent : IDomainEvent
{
    public Request Request { get; }
    public RequestApproval Approval { get; }
    public DateTime OccurredOn { get; }

    public RequestRejectedEvent(Request request, RequestApproval approval)
    {
        Request = request;
        Approval = approval;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a request receives all required approvals
/// </summary>
public class RequestFullyApprovedEvent : IDomainEvent
{
    public Request Request { get; }
    public DateTime OccurredOn { get; }

    public RequestFullyApprovedEvent(Request request)
    {
        Request = request;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a request is completed
/// </summary>
public class RequestCompletedEvent : IDomainEvent
{
    public Request Request { get; }
    public DateTime OccurredOn { get; }

    public RequestCompletedEvent(Request request)
    {
        Request = request;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a request is cancelled
/// </summary>
public class RequestCancelledEvent : IDomainEvent
{
    public Request Request { get; }
    public DateTime OccurredOn { get; }

    public RequestCancelledEvent(Request request)
    {
        Request = request;
        OccurredOn = DateTime.UtcNow;
    }
}
