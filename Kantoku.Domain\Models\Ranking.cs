﻿namespace Kantoku.Domain.Models;

public class Ranking : AuditableEntity
{
    public Guid RankingUid { get; set; }


    public string RankingName { get; set; } = null!;


    public int? MaxValue { get; set; }


    public int? MinValue { get; set; }


    public int? AverageValue { get; set; }


    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }

    public virtual Org Org { get; set; } = null!;
    public virtual ICollection<ProjectRankingCost> ProjectRankingCosts { get; set; } = [];
    public virtual ICollection<Employee> Employees { get; set; } = [];
}
