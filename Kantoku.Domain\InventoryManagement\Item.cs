using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.InventoryManagement.ValueObjects;
using Kantoku.Domain.InventoryManagement.Events;
using Kantoku.Domain.InventoryManagement.Enums;

namespace Kantoku.Domain.InventoryManagement;

/// <summary>
/// Item aggregate root representing an inventory item in the system
/// </summary>
public class Item : FullAuditedEntity<Guid>
{
    private readonly List<ItemPrice> _prices = new();
    private readonly List<ItemStock> _stockMovements = new();

    public Guid OrgId { get; private set; }
    public string ItemCode { get; private set; } = null!;
    public string ItemName { get; private set; } = null!;
    public string? Description { get; private set; }
    public Guid? CategoryId { get; private set; }
    public Guid? ManufacturerId { get; private set; }
    
    public ItemSpecifications? Specifications { get; private set; }
    public ItemUnit Unit { get; private set; } = ItemUnit.Piece;
    public ItemStatus Status { get; private set; } = ItemStatus.Active;
    
    public decimal CurrentStock { get; private set; } = 0;
    public decimal MinimumStock { get; private set; } = 0;
    public decimal MaximumStock { get; private set; } = 0;
    public decimal ReorderPoint { get; private set; } = 0;
    
    public string? StorageLocation { get; private set; }
    public string? Barcode { get; private set; }
    public string? SerialNumber { get; private set; }
    
    public bool IsConsumable { get; private set; } = true;
    public bool RequiresApproval { get; private set; } = false;
    public bool IsHazardous { get; private set; } = false;

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<ItemPrice> Prices => _prices.AsReadOnly();
    public IReadOnlyCollection<ItemStock> StockMovements => _stockMovements.AsReadOnly();

    // Private constructor for EF Core
    private Item() : base() { }

    /// <summary>
    /// Creates a new item
    /// </summary>
    public Item(
        Guid id,
        Guid orgId,
        string itemCode,
        string itemName,
        ItemUnit unit,
        string? description = null,
        Guid? categoryId = null) : base(id)
    {
        OrgId = orgId;
        SetItemCode(itemCode);
        SetItemName(itemName);
        Unit = unit ?? throw new ArgumentNullException(nameof(unit));
        Description = description;
        CategoryId = categoryId;

        AddDomainEvent(new ItemCreatedEvent(this));
    }

    /// <summary>
    /// Updates basic item information
    /// </summary>
    public void UpdateBasicInfo(
        string itemName,
        string? description = null,
        Guid? categoryId = null,
        Guid? manufacturerId = null)
    {
        SetItemName(itemName);
        Description = description;
        CategoryId = categoryId;
        ManufacturerId = manufacturerId;

        AddDomainEvent(new ItemUpdatedEvent(this));
    }

    /// <summary>
    /// Updates item specifications
    /// </summary>
    public void UpdateSpecifications(ItemSpecifications specifications)
    {
        Specifications = specifications;
        AddDomainEvent(new ItemSpecificationsUpdatedEvent(this));
    }

    /// <summary>
    /// Updates stock levels and settings
    /// </summary>
    public void UpdateStockSettings(
        decimal? minimumStock = null,
        decimal? maximumStock = null,
        decimal? reorderPoint = null)
    {
        if (minimumStock.HasValue)
            SetMinimumStock(minimumStock.Value);

        if (maximumStock.HasValue)
            SetMaximumStock(maximumStock.Value);

        if (reorderPoint.HasValue)
            SetReorderPoint(reorderPoint.Value);

        ValidateStockLevels();
        AddDomainEvent(new ItemStockSettingsUpdatedEvent(this));
    }

    /// <summary>
    /// Updates storage and identification information
    /// </summary>
    public void UpdateStorageInfo(
        string? storageLocation = null,
        string? barcode = null,
        string? serialNumber = null)
    {
        StorageLocation = storageLocation;
        Barcode = barcode;
        SerialNumber = serialNumber;

        AddDomainEvent(new ItemStorageInfoUpdatedEvent(this));
    }

    /// <summary>
    /// Updates item properties
    /// </summary>
    public void UpdateProperties(
        bool? isConsumable = null,
        bool? requiresApproval = null,
        bool? isHazardous = null)
    {
        IsConsumable = isConsumable ?? IsConsumable;
        RequiresApproval = requiresApproval ?? RequiresApproval;
        IsHazardous = isHazardous ?? IsHazardous;

        AddDomainEvent(new ItemPropertiesUpdatedEvent(this));
    }

    /// <summary>
    /// Updates item status
    /// </summary>
    public void UpdateStatus(ItemStatus status)
    {
        var oldStatus = Status;
        Status = status;

        if (oldStatus != status)
        {
            AddDomainEvent(new ItemStatusChangedEvent(this, oldStatus, status));
        }
    }

    /// <summary>
    /// Adds stock to the item
    /// </summary>
    public void AddStock(decimal quantity, string reason, decimal? unitCost = null, string? reference = null)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be greater than 0", nameof(quantity));

        var stockMovement = new ItemStock(
            Guid.NewGuid(),
            Id,
            StockMovementType.In,
            quantity,
            reason,
            unitCost,
            reference);

        _stockMovements.Add(stockMovement);
        CurrentStock += quantity;

        AddDomainEvent(new ItemStockAddedEvent(this, stockMovement));
    }

    /// <summary>
    /// Removes stock from the item
    /// </summary>
    public void RemoveStock(decimal quantity, string reason, string? reference = null)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be greater than 0", nameof(quantity));

        if (quantity > CurrentStock)
            throw new InvalidOperationException("Cannot remove more stock than available");

        var stockMovement = new ItemStock(
            Guid.NewGuid(),
            Id,
            StockMovementType.Out,
            quantity,
            reason,
            null,
            reference);

        _stockMovements.Add(stockMovement);
        CurrentStock -= quantity;

        AddDomainEvent(new ItemStockRemovedEvent(this, stockMovement));

        // Check if stock is below reorder point
        if (CurrentStock <= ReorderPoint && ReorderPoint > 0)
        {
            AddDomainEvent(new ItemReorderPointReachedEvent(this));
        }
    }

    /// <summary>
    /// Adjusts stock to a specific quantity
    /// </summary>
    public void AdjustStock(decimal newQuantity, string reason, string? reference = null)
    {
        if (newQuantity < 0)
            throw new ArgumentException("Stock quantity cannot be negative", nameof(newQuantity));

        var difference = newQuantity - CurrentStock;
        if (difference == 0)
            return;

        var movementType = difference > 0 ? StockMovementType.AdjustmentIn : StockMovementType.AdjustmentOut;
        var stockMovement = new ItemStock(
            Guid.NewGuid(),
            Id,
            movementType,
            Math.Abs(difference),
            reason,
            null,
            reference);

        _stockMovements.Add(stockMovement);
        CurrentStock = newQuantity;

        AddDomainEvent(new ItemStockAdjustedEvent(this, stockMovement));
    }

    /// <summary>
    /// Adds a price to the item
    /// </summary>
    public void AddPrice(ItemPrice price)
    {
        if (price == null)
            throw new ArgumentNullException(nameof(price));

        // Deactivate existing prices of the same type if this is the new active price
        if (price.IsActive)
        {
            foreach (var existingPrice in _prices.Where(p => p.PriceType == price.PriceType && p.IsActive))
            {
                existingPrice.Deactivate();
            }
        }

        _prices.Add(price);
        AddDomainEvent(new ItemPriceAddedEvent(this, price));
    }

    /// <summary>
    /// Gets the current active price for a specific type
    /// </summary>
    public ItemPrice? GetCurrentPrice(string priceType = "STANDARD")
    {
        return _prices.FirstOrDefault(p => p.PriceType == priceType && p.IsActive);
    }

    /// <summary>
    /// Checks if the item is low in stock
    /// </summary>
    public bool IsLowStock => CurrentStock <= ReorderPoint && ReorderPoint > 0;

    /// <summary>
    /// Checks if the item is out of stock
    /// </summary>
    public bool IsOutOfStock => CurrentStock <= 0;

    /// <summary>
    /// Checks if the item is overstocked
    /// </summary>
    public bool IsOverstocked => MaximumStock > 0 && CurrentStock > MaximumStock;

    /// <summary>
    /// Gets the stock status
    /// </summary>
    public string GetStockStatus()
    {
        if (IsOutOfStock) return "OUT_OF_STOCK";
        if (IsLowStock) return "LOW_STOCK";
        if (IsOverstocked) return "OVERSTOCKED";
        return "NORMAL";
    }

    // Private helper methods
    private void SetItemCode(string itemCode)
    {
        if (string.IsNullOrWhiteSpace(itemCode))
            throw new ArgumentException("Item code cannot be null or empty", nameof(itemCode));

        if (itemCode.Length > 50)
            throw new ArgumentException("Item code cannot exceed 50 characters", nameof(itemCode));

        ItemCode = itemCode.Trim();
    }

    private void SetItemName(string itemName)
    {
        if (string.IsNullOrWhiteSpace(itemName))
            throw new ArgumentException("Item name cannot be null or empty", nameof(itemName));

        if (itemName.Length > 200)
            throw new ArgumentException("Item name cannot exceed 200 characters", nameof(itemName));

        ItemName = itemName.Trim();
    }

    private void SetMinimumStock(decimal minimumStock)
    {
        if (minimumStock < 0)
            throw new ArgumentException("Minimum stock cannot be negative", nameof(minimumStock));

        MinimumStock = minimumStock;
    }

    private void SetMaximumStock(decimal maximumStock)
    {
        if (maximumStock < 0)
            throw new ArgumentException("Maximum stock cannot be negative", nameof(maximumStock));

        MaximumStock = maximumStock;
    }

    private void SetReorderPoint(decimal reorderPoint)
    {
        if (reorderPoint < 0)
            throw new ArgumentException("Reorder point cannot be negative", nameof(reorderPoint));

        ReorderPoint = reorderPoint;
    }

    private void ValidateStockLevels()
    {
        if (MinimumStock > MaximumStock && MaximumStock > 0)
            throw new InvalidOperationException("Minimum stock cannot be greater than maximum stock");

        if (ReorderPoint > MaximumStock && MaximumStock > 0)
            throw new InvalidOperationException("Reorder point cannot be greater than maximum stock");
    }
}
