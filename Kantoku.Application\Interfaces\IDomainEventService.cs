using Kantoku.Domain.Common.BuildingBlocks;

namespace Kantoku.Application.Interfaces
{
    /// <summary>
    /// Interface for triggering domain events
    /// </summary>
    public interface IDomainEventService
    {
        /// <summary>
        /// Publishes all domain events from the provided entity
        /// </summary>
        /// <typeparam name="TId">The ID type of the aggregate root</typeparam>
        /// <param name="entity">The entity containing domain events</param>
        /// <param name="cancellationToken">A CancellationToken to observe while waiting for the task to complete</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task PublishAsync<TId>(Entity<TId> entity, CancellationToken cancellationToken = default) 
            where TId : IEquatable<TId>;
    }
} 