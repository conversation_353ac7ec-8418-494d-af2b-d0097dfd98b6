using Kantoku.SharedKernel;

namespace Kantoku.Domain.AccountManagement.Events;

/// <summary>
/// Domain event raised when an account is created
/// </summary>
public class AccountCreatedEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountCreatedEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an account is updated
/// </summary>
public class AccountUpdatedEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountUpdatedEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an account password is changed
/// </summary>
public class AccountPasswordChangedEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountPasswordChangedEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a login attempt occurs
/// </summary>
public class AccountLoginEvent : IDomainEvent
{
    public Account Account { get; }
    public bool IsSuccessful { get; }
    public DateTime OccurredOn { get; }

    public AccountLoginEvent(Account account, bool isSuccessful)
    {
        Account = account;
        IsSuccessful = isSuccessful;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an account is locked
/// </summary>
public class AccountLockedEvent : IDomainEvent
{
    public Account Account { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public AccountLockedEvent(Account account, string reason)
    {
        Account = account;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an account is unlocked
/// </summary>
public class AccountUnlockedEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountUnlockedEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an account is suspended
/// </summary>
public class AccountSuspendedEvent : IDomainEvent
{
    public Account Account { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public AccountSuspendedEvent(Account account, string reason)
    {
        Account = account;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an account is activated
/// </summary>
public class AccountActivatedEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountActivatedEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an account is deactivated
/// </summary>
public class AccountDeactivatedEvent : IDomainEvent
{
    public Account Account { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public AccountDeactivatedEvent(Account account, string reason)
    {
        Account = account;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an account email is verified
/// </summary>
public class AccountEmailVerifiedEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountEmailVerifiedEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an account phone is verified
/// </summary>
public class AccountPhoneVerifiedEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountPhoneVerifiedEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when two-factor authentication is enabled
/// </summary>
public class AccountTwoFactorEnabledEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountTwoFactorEnabledEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when two-factor authentication is disabled
/// </summary>
public class AccountTwoFactorDisabledEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountTwoFactorDisabledEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when account preferences are updated
/// </summary>
public class AccountPreferencesUpdatedEvent : IDomainEvent
{
    public Account Account { get; }
    public DateTime OccurredOn { get; }

    public AccountPreferencesUpdatedEvent(Account account)
    {
        Account = account;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a role is added to an account
/// </summary>
public class AccountRoleAddedEvent : IDomainEvent
{
    public Account Account { get; }
    public UserRole UserRole { get; }
    public DateTime OccurredOn { get; }

    public AccountRoleAddedEvent(Account account, UserRole userRole)
    {
        Account = account;
        UserRole = userRole;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a role is removed from an account
/// </summary>
public class AccountRoleRemovedEvent : IDomainEvent
{
    public Account Account { get; }
    public UserRole UserRole { get; }
    public DateTime OccurredOn { get; }

    public AccountRoleRemovedEvent(Account account, UserRole userRole)
    {
        Account = account;
        UserRole = userRole;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a session is created
/// </summary>
public class AccountSessionCreatedEvent : IDomainEvent
{
    public Account Account { get; }
    public AccountSession Session { get; }
    public DateTime OccurredOn { get; }

    public AccountSessionCreatedEvent(Account account, AccountSession session)
    {
        Account = account;
        Session = session;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a session is revoked
/// </summary>
public class AccountSessionRevokedEvent : IDomainEvent
{
    public Account Account { get; }
    public AccountSession Session { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public AccountSessionRevokedEvent(Account account, AccountSession session, string reason)
    {
        Account = account;
        Session = session;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}
