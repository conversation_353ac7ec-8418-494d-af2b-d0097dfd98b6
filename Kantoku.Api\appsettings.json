{"ApplicationName": "KantokuService", "AppConfig": {"DefaultLanguage": "ja", "Environment": "Development", "GoogleApiKey": "", "ApplicationHost": "web-staging.kantok.biz", "AllowOrigins": "http://localhost:6678,https://web-dev.kantok.biz,https://web-test.kantok.biz,https://web-staging.kantok.biz"}, "DbConfig": {"DbHost": "**************", "DbPort": "14322", "DbName": "Ka<PERSON><PERSON>", "Schema": "Kantoku_dev", "UserName": "", "Password": "", "EnableCache": true, "CacheExpiry": 60}, "AuthConfig": {"Issuer": "erp.kantok.biz", "Audience": "erp.kantok.biz", "SecretKey": "", "AccessTokenExpires": 30000, "RefreshTokenExpires": 7}, "MailConfig": {"EmailFrom": "<EMAIL>", "SmtpHost": "s370.xrea.com", "SmtpPort": 587, "SmtpUser": "", "SmtpPass": "", "FromName": "Ka<PERSON><PERSON>", "Subject": "Ka<PERSON><PERSON>"}, "MinioConfig": {"Endpoint": "http://**************:9000", "AccessKey": "", "SecretKey": ""}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "ElasticSearch": {"Url": "http://**************:9200"}, "RedisConfig": {"ConnectionString": "http://**************:6379", "AbortOnConnectFail": true}, "PostgreDbConfig": {"DbHost": "**************", "DbPort": "54322", "DbName": "Ka<PERSON><PERSON>", "Schema": "kantoku_dev", "UserName": "kan_user", "Password": "Kan@123"}}