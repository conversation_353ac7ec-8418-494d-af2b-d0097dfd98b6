namespace Kantoku.Domain.Models;

public class ItemPrice : AuditableEntity
{
    public Guid ItemPriceUid { get; set; }
    public Guid ItemUid { get; set; }
    public Guid VendorUid { get; set; }
    public Guid OrgUid { get; set; }


    public string? Unit { get; set; }


    public DateOnly ValidFrom { get; set; }


    public DateOnly? ValidTo { get; set; }


    public int Price { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Item Item { get; set; } = null!;

    public virtual Vendor Vendor { get; set; } = null!;
}
