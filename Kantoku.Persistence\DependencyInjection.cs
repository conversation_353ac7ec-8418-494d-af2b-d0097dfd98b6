using Kantoku.Application.Interfaces;
using Kantoku.Persistence.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Kantoku.Persistence
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddPostgreSQLInfrastructure(
            this IServiceCollection services, 
            IConfiguration configuration)
        {
            // Configure DbContext
            services.AddDbContext<KantokuPostgresDbContext>(options =>
                options.UseNpgsql(configuration.GetConnectionString("KantokuPostgresConnection"),
                    b => b.MigrationsAssembly(typeof(KantokuPostgresDbContext).Assembly.FullName)));

            // Register IUnitOfWork. DbContext implements it directly in this setup.
            // If IUnitOfWork had more responsibilities or you wanted a separate class, you would register it here.
            services.AddScoped<IUnitOfWork>(provider => provider.GetRequiredService<KantokuPostgresDbContext>());
            
            // Register IDomainEventService if its implementation is in this project
            // services.AddScoped<IDomainEventService, DomainEventService>();

            // Register repositories
            // Example: services.AddScoped(typeof(IRepository<>), typeof(EfRepository<>));
            // You would typically have specific repository implementations here.

            return services;
        }
    }
} 