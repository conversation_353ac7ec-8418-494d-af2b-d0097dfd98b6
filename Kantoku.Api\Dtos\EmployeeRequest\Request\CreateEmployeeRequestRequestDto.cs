using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.EmployeeRequest.Request;

public class CreateEmployeeRequestRequestDto
{
    /// <summary>
    /// Request start date and time (*)
    /// The date and time that request starts
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    [Required]
    public string RequestFrom { get; set; } = null!;

    /// <summary>
    /// Request end date and time (*)
    /// The date and time that request ends
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    [Required]
    public string RequestTo { get; set; } = null!;

    /// <summary>
    /// Request type code (*)
    /// The code indicating type of request
    /// </summary>
    [Required]
    public string RequestTypeCode { get; set; } = null!;

    /// <summary>
    /// Project ID
    /// The ID of the project associated with the request
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// Description
    /// Additional details about the request
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Is user request
    /// Whether the request is a user request
    /// </summary>
    public bool? IsUserRequestedLeave { get; set; }
}

public class DurationCreateRequestDto
{
    /// <summary>
    /// Number of days
    /// The duration in days
    /// </summary>
    public float? Days { get; set; }

    /// <summary>
    /// Number of hours
    /// The duration in hours
    /// </summary>
    public float? Hours { get; set; }

    /// <summary>
    /// Number of minutes
    /// The duration in minutes
    /// </summary>
    public float? Minutes { get; set; }
}