using Kantoku.SharedKernel;
using Kantoku.Domain.ProjectManagement.Enums;

namespace Kantoku.Domain.ProjectManagement.Events;

/// <summary>
/// Domain event raised when a project is created
/// </summary>
public class ProjectCreatedEvent : IDomainEvent
{
    public Project Project { get; }
    public DateTime OccurredOn { get; }

    public ProjectCreatedEvent(Project project)
    {
        Project = project;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a project is updated
/// </summary>
public class ProjectUpdatedEvent : IDomainEvent
{
    public Project Project { get; }
    public DateTime OccurredOn { get; }

    public ProjectUpdatedEvent(Project project)
    {
        Project = project;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when project dates are updated
/// </summary>
public class ProjectDatesUpdatedEvent : IDomainEvent
{
    public Project Project { get; }
    public DateTime OccurredOn { get; }

    public ProjectDatesUpdatedEvent(Project project)
    {
        Project = project;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when project budget is updated
/// </summary>
public class ProjectBudgetUpdatedEvent : IDomainEvent
{
    public Project Project { get; }
    public DateTime OccurredOn { get; }

    public ProjectBudgetUpdatedEvent(Project project)
    {
        Project = project;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when project status changes
/// </summary>
public class ProjectStatusChangedEvent : IDomainEvent
{
    public Project Project { get; }
    public ProjectStatus OldStatus { get; }
    public ProjectStatus NewStatus { get; }
    public DateTime OccurredOn { get; }

    public ProjectStatusChangedEvent(Project project, ProjectStatus oldStatus, ProjectStatus newStatus)
    {
        Project = project;
        OldStatus = oldStatus;
        NewStatus = newStatus;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when external entities are assigned to a project
/// </summary>
public class ProjectExternalEntitiesAssignedEvent : IDomainEvent
{
    public Project Project { get; }
    public DateTime OccurredOn { get; }

    public ProjectExternalEntitiesAssignedEvent(Project project)
    {
        Project = project;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a project manager is added
/// </summary>
public class ProjectManagerAddedEvent : IDomainEvent
{
    public Project Project { get; }
    public ProjectManager Manager { get; }
    public DateTime OccurredOn { get; }

    public ProjectManagerAddedEvent(Project project, ProjectManager manager)
    {
        Project = project;
        Manager = manager;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a project manager is removed
/// </summary>
public class ProjectManagerRemovedEvent : IDomainEvent
{
    public Project Project { get; }
    public ProjectManager Manager { get; }
    public DateTime OccurredOn { get; }

    public ProjectManagerRemovedEvent(Project project, ProjectManager manager)
    {
        Project = project;
        Manager = manager;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a project schedule is added
/// </summary>
public class ProjectScheduleAddedEvent : IDomainEvent
{
    public Project Project { get; }
    public ProjectSchedule Schedule { get; }
    public DateTime OccurredOn { get; }

    public ProjectScheduleAddedEvent(Project project, ProjectSchedule schedule)
    {
        Project = project;
        Schedule = schedule;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a construction is added to a project
/// </summary>
public class ConstructionAddedEvent : IDomainEvent
{
    public Project Project { get; }
    public Construction Construction { get; }
    public DateTime OccurredOn { get; }

    public ConstructionAddedEvent(Project project, Construction construction)
    {
        Project = project;
        Construction = construction;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a daily report is added to a project
/// </summary>
public class ProjectDailyReportAddedEvent : IDomainEvent
{
    public Project Project { get; }
    public ProjectDailyReport DailyReport { get; }
    public DateTime OccurredOn { get; }

    public ProjectDailyReportAddedEvent(Project project, ProjectDailyReport dailyReport)
    {
        Project = project;
        DailyReport = dailyReport;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a project is started
/// </summary>
public class ProjectStartedEvent : IDomainEvent
{
    public Project Project { get; }
    public DateTime OccurredOn { get; }

    public ProjectStartedEvent(Project project)
    {
        Project = project;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a project is completed
/// </summary>
public class ProjectCompletedEvent : IDomainEvent
{
    public Project Project { get; }
    public DateTime OccurredOn { get; }

    public ProjectCompletedEvent(Project project)
    {
        Project = project;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a project is suspended
/// </summary>
public class ProjectSuspendedEvent : IDomainEvent
{
    public Project Project { get; }
    public DateTime OccurredOn { get; }

    public ProjectSuspendedEvent(Project project)
    {
        Project = project;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a project is cancelled
/// </summary>
public class ProjectCancelledEvent : IDomainEvent
{
    public Project Project { get; }
    public DateTime OccurredOn { get; }

    public ProjectCancelledEvent(Project project)
    {
        Project = project;
        OccurredOn = DateTime.UtcNow;
    }
}
