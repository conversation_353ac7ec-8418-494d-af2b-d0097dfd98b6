using Kantoku.Application; // For ApplicationContext
using Kantoku.Application.Interfaces; // For IApplicationContextProvider
using System.Threading;

namespace Kantoku.Infrastructure.Services
{
    /// <summary>
    /// Internal holder class for the AsyncLocal context to ensure proper updates.
    /// </summary>
    internal class ApplicationContextHolder
    {
        public ApplicationContext? Context;
    }

    /// <summary>
    /// Provides an ambient ApplicationContext using AsyncLocal.
    /// The context should be set by middleware at the beginning of a request/scope.
    /// </summary>
    public class AsyncLocalApplicationContextProvider : IApplicationContextProvider
    {
        private static readonly AsyncLocal<ApplicationContextHolder> _currentContext = new AsyncLocal<ApplicationContextHolder>();

        public ApplicationContext? Context => _currentContext.Value?.Context;

        /// <summary>
        /// Sets the current ApplicationContext for the current asynchronous control flow.
        /// This should typically be called by a middleware at the start of a request.
        /// </summary>
        /// <param name="context">The ApplicationContext to set.</param>
        public static void SetContext(ApplicationContext context)
        {
            _currentContext.Value = new ApplicationContextHolder { Context = context };
        }

        /// <summary>
        /// Clears the current ApplicationContext for the current asynchronous control flow.
        /// This can be called by middleware at the end of a request, though AsyncLocal usually handles this implicitly by flowing.
        /// </summary>
        public static void ClearContext()
        {
            _currentContext.Value = null;
        }
    }
} 