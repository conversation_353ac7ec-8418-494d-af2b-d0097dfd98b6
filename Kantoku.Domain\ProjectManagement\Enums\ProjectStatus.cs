using Kantoku.SharedKernel;

namespace Kantoku.Domain.ProjectManagement.Enums;

/// <summary>
/// Enumeration representing project status
/// </summary>
public class ProjectStatus : Enumeration
{
    public static readonly ProjectStatus Planning = new(1, nameof(Planning), "Planning");
    public static readonly ProjectStatus InProgress = new(2, nameof(InProgress), "In Progress");
    public static readonly ProjectStatus Suspended = new(3, nameof(Suspended), "Suspended");
    public static readonly ProjectStatus Completed = new(4, nameof(Completed), "Completed");
    public static readonly ProjectStatus Cancelled = new(5, nameof(Cancelled), "Cancelled");

    public string DisplayName { get; private set; }

    private ProjectStatus(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<ProjectStatus> GetAll()
    {
        return new[] { Planning, InProgress, Suspended, Completed, Cancelled };
    }

    public static ProjectStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown project status: {name}");
        return status;
    }

    public static ProjectStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown project status ID: {id}");
        return status;
    }

    public bool IsActive => this == Planning || this == InProgress;
    public bool IsInactive => this == Suspended || this == Completed || this == Cancelled;
    public bool IsCompleted => this == Completed;
    public bool IsCancelled => this == Cancelled;
    public bool CanBeModified => this == Planning || this == InProgress || this == Suspended;
}
