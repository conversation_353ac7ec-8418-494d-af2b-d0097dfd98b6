namespace Kantoku.SharedKernel.Results;

/// <summary>
/// Represents the result of an operation that can either succeed or fail.
/// This is a non-generic version for operations that don't return a value.
/// </summary>
public class Result
{
    /// <summary>
    /// Gets a value indicating whether the operation was successful
    /// </summary>
    public bool IsSuccess { get; protected set; }

    /// <summary>
    /// Gets a value indicating whether the operation failed
    /// </summary>
    public bool IsFailure => !IsSuccess;

    /// <summary>
    /// Gets the error message if the operation failed
    /// </summary>
    public string? Error { get; protected set; }

    /// <summary>
    /// Gets the collection of error messages if the operation failed
    /// </summary>
    public IReadOnlyList<string> Errors { get; protected set; } = [];

    /// <summary>
    /// Initializes a new instance of the Result class
    /// </summary>
    /// <param name="isSuccess">Whether the operation was successful</param>
    /// <param name="error">The error message if the operation failed</param>
    /// <param name="errors">The collection of error messages if the operation failed</param>
    protected Result(bool isSuccess, string? error = null, IEnumerable<string>? errors = null)
    {
        IsSuccess = isSuccess;
        Error = error;
        Errors = errors?.ToList() ?? [];

        if (isSuccess && (!string.IsNullOrEmpty(error) || Errors.Any()))
        {
            throw new InvalidOperationException("A successful result cannot have errors.");
        }

        if (!isSuccess && string.IsNullOrEmpty(error) && !Errors.Any())
        {
            throw new InvalidOperationException("A failed result must have at least one error.");
        }
    }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <returns>A successful result</returns>
    public static Result Success() => new(true);

    /// <summary>
    /// Creates a failed result with the specified error message
    /// </summary>
    /// <param name="error">The error message</param>
    /// <returns>A failed result</returns>
    public static Result Failure(string error) => new(false, error);

    /// <summary>
    /// Creates a failed result with the specified error messages
    /// </summary>
    /// <param name="errors">The error messages</param>
    /// <returns>A failed result</returns>
    public static Result Failure(IEnumerable<string> errors) => new(false, errors: errors);

    /// <summary>
    /// Creates a failed result with the specified error messages
    /// </summary>
    /// <param name="errors">The error messages</param>
    /// <returns>A failed result</returns>
    public static Result Failure(params string[] errors) => new(false, errors: errors);

    /// <summary>
    /// Implicitly converts a string to a failed result
    /// </summary>
    /// <param name="error">The error message</param>
    public static implicit operator Result(string error) => Failure(error);

    /// <summary>
    /// Combines multiple results into a single result.
    /// The combined result is successful only if all input results are successful.
    /// </summary>
    /// <param name="results">The results to combine</param>
    /// <returns>A combined result</returns>
    public static Result Combine(params Result[] results)
    {
        var failures = results.Where(r => r.IsFailure).ToList();
        
        if (!failures.Any())
            return Success();

        var allErrors = failures.SelectMany(f => f.Errors.Any() ? f.Errors : new[] { f.Error! }).ToList();
        return Failure(allErrors);
    }

    /// <summary>
    /// Combines multiple results into a single result.
    /// The combined result is successful only if all input results are successful.
    /// </summary>
    /// <param name="results">The results to combine</param>
    /// <returns>A combined result</returns>
    public static Result Combine(IEnumerable<Result> results) => Combine(results.ToArray());
}
