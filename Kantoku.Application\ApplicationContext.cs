using System;

namespace Kantoku.Application
{
    /// <summary>
    /// Holds contextual information for the current application scope (e.g., a request).
    /// </summary>
    public class ApplicationContext
    {
        public Guid? UserId { get; set; }
        public Guid? TenantId { get; set; }
        public Guid? AccountId { get; set; }
        
        // Add other relevant properties as needed, for example:
        // public string? CorrelationId { get; set; }
        // public string? UserRoles { get; set; } // Or a list of claims
        // public string? IpAddress { get; set; }
    }
} 