﻿namespace Kantoku.Domain.Models;

public class EmployeeShift : AuditableEntity
{
    public Guid EmployeeShiftUid { get; set; }

    public Guid? ProjectScheduleUid { get; set; }

    public Guid? MonthlyReportUid { get; set; }

    public Guid EmployeeUid { get; set; }

    public Guid ProjectUid { get; set; }


    public string? WorkingLocation { get; set; }


    public DateTime? ScheduledStartTime { get; set; }


    public DateTime? CheckInTime { get; set; }

    public int? CheckInTimeLastModifier { get; set; } // 0: author, 1: manager, 2: system 3: job


    public string? CheckInLocation { get; set; }


    public DateTime? ScheduledEndTime { get; set; }


    public DateTime? CheckOutTime { get; set; }

    public int? CheckOutTimeLastModifier { get; set; } // 0: author, 1: manager, 2: system 3: job

    public DateTime? AutoCheckOutTime { get; set; }


    public string? CheckOutLocation { get; set; }


    public ICollection<EmployeeShiftBreakTime>? EmployeeShiftBreakTimes { get; set; } = [];

    public int? BreakTimeLastModifier { get; set; } // 0: author, 1: manager, 2: system 3: job

    public float TotalScheduledWorkTime { get; set; }

    public float TotalWorkTime { get; set; }

    public float TotalBreakTime { get; set; }

    public float TotalOverTime { get; set; }

    public bool IsRequested { get; set; }

    public bool? IsApproved { get; set; }

    public Guid? ApprovedBy { get; set; }

    public DateTime? ApprovedTime { get; set; }

    public bool IsDeleted { get; set; }

    public string? Description { get; set; }

    public string? AssignedRole { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Employee? Approver { get; set; }

    public virtual Project Project { get; set; } = null!;

    public virtual ProjectSchedule? ProjectSchedule { get; set; }

    public virtual MonthlyReport? MonthlyReport { get; set; }
}

public class EmployeeShiftBreakTime
{
    public DateTime? BreakInTime { get; set; }

    public DateTime? BreakOutTime { get; set; }
}

public enum LastModifier
{
    Job = -1,
    System = 0,
    Author = 1,
    Manager = 2,
}
