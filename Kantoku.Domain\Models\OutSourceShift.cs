namespace Kantoku.Domain.Models;

public class OutSourceShift : AuditableEntity
{
    public Guid OutSourceShiftUid { get; set; }
    public Guid ProjectScheduleUid { get; set; }
    public Guid OutSourceUid { get; set; }


    public DateTime ScheduledStartTime { get; set; }


    public DateTime ScheduledEndTime { get; set; }


    public float AssignedWorkload { get; set; }


    public string? Role { get; set; }

    public bool IsDeleted { get; set; }

    public virtual OutSource OutSource { get; set; } = null!;

    public virtual ProjectSchedule ProjectSchedule { get; set; } = null!;
}
