﻿using Kantoku.Domain.Common.Auditing;
using Kantoku.Domain.EmployeeManagement.Events;
using Kantoku.Domain.EmployeeManagement.ValueObjects;
using Kantoku.Domain.EmployeeManagement.Enums;
using Kantoku.Domain.OrganizationManagement;

namespace Kantoku.Domain.EmployeeManagement;

/// <summary>
/// Employee aggregate root representing an employee in the system
/// </summary>
public class Employee : FullAuditedEntity<Guid>
{
    private readonly List<EmployeeLeave> _employeeLeaves = new();
    private readonly List<EmployeeRole> _employeeRoles = new();
    private readonly List<EmployeeShift> _employeeShifts = new();

    public Guid AccountId { get; private set; }
    public Guid OrgId { get; private set; }

    public Guid? StructureId { get; private set; }
    public Guid? PositionId { get; private set; }
    public Guid? RankingId { get; private set; }

    public string EmployeeCode { get; private set; } = null!;
    public EmployeeName EmployeeName { get; private set; } = null!;
    public EmployeeType EmployeeType { get; private set; } = EmployeeType.Officer;

    public EmployeeContactInfo? ContactInfo { get; private set; }
    public string? EmployeeAddress { get; private set; }

    public WorkingStatus WorkingStatus { get; private set; } = WorkingStatus.Invited;
    public DateTime? WorkingFromDate { get; private set; }
    public DateTime? WorkingToDate { get; private set; }

    public SalaryInfo SalaryInfo { get; private set; } = null!;
    public float StandardWorkingHours { get; private set; }
    public string? Description { get; private set; }

    public bool IsOrgAdmin { get; private set; } = false;
    public bool HasApprovalAuthority { get; private set; } = false;
    public bool IsHidden { get; private set; } = false;

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<EmployeeLeave> EmployeeLeaves => _employeeLeaves.AsReadOnly();
    public IReadOnlyCollection<EmployeeRole> EmployeeRoles => _employeeRoles.AsReadOnly();
    public IReadOnlyCollection<EmployeeShift> EmployeeShifts => _employeeShifts.AsReadOnly();

    // Private constructor for EF Core
    private Employee() : base() { }

    /// <summary>
    /// Creates a new employee
    /// </summary>
    public Employee(
        Guid id,
        Guid accountId,
        Guid orgId,
        string employeeCode,
        EmployeeName employeeName,
        SalaryInfo salaryInfo,
        float standardWorkingHours = 8.0f) : base(id)
    {
        AccountId = accountId;
        OrgId = orgId;
        SetEmployeeCode(employeeCode);
        EmployeeName = employeeName ?? throw new ArgumentNullException(nameof(employeeName));
        SalaryInfo = salaryInfo ?? throw new ArgumentNullException(nameof(salaryInfo));
        SetStandardWorkingHours(standardWorkingHours);

        AddDomainEvent(new EmployeeCreatedEvent(this));
    }

    /// <summary>
    /// Updates basic employee information
    /// </summary>
    public void UpdateBasicInfo(
        EmployeeName employeeName,
        EmployeeContactInfo? contactInfo = null,
        string? employeeAddress = null,
        string? description = null)
    {
        EmployeeName = employeeName ?? throw new ArgumentNullException(nameof(employeeName));
        ContactInfo = contactInfo;
        EmployeeAddress = employeeAddress;
        Description = description;

        AddDomainEvent(new EmployeeUpdatedEvent(this));
    }

    /// <summary>
    /// Updates employee organizational assignment
    /// </summary>
    public void UpdateOrganizationalAssignment(
        Guid? structureId = null,
        Guid? positionId = null,
        Guid? rankingId = null)
    {
        var oldStructureId = StructureId;
        var oldPositionId = PositionId;
        var oldRankingId = RankingId;

        StructureId = structureId;
        PositionId = positionId;
        RankingId = rankingId;

        if (oldStructureId != structureId || oldPositionId != positionId || oldRankingId != rankingId)
        {
            AddDomainEvent(new EmployeeOrganizationalAssignmentChangedEvent(this, oldStructureId, oldPositionId, oldRankingId));
        }
    }

    /// <summary>
    /// Updates employee working status and dates
    /// </summary>
    public void UpdateWorkingStatus(
        WorkingStatus workingStatus,
        DateTime? workingFromDate = null,
        DateTime? workingToDate = null)
    {
        var oldStatus = WorkingStatus;
        WorkingStatus = workingStatus;
        WorkingFromDate = workingFromDate;
        WorkingToDate = workingToDate;

        if (oldStatus != workingStatus)
        {
            AddDomainEvent(new EmployeeWorkingStatusChangedEvent(this, oldStatus, workingStatus));
        }
    }

    /// <summary>
    /// Updates employee salary information
    /// </summary>
    public void UpdateSalaryInfo(SalaryInfo salaryInfo)
    {
        SalaryInfo = salaryInfo ?? throw new ArgumentNullException(nameof(salaryInfo));
        AddDomainEvent(new EmployeeSalaryUpdatedEvent(this));
    }

    /// <summary>
    /// Updates standard working hours
    /// </summary>
    public void UpdateStandardWorkingHours(float standardWorkingHours)
    {
        SetStandardWorkingHours(standardWorkingHours);
    }

    /// <summary>
    /// Sets employee as organization admin
    /// </summary>
    public void SetAsOrgAdmin()
    {
        if (!IsOrgAdmin)
        {
            IsOrgAdmin = true;
            AddDomainEvent(new EmployeePromotedToOrgAdminEvent(this));
        }
    }

    /// <summary>
    /// Removes organization admin privileges
    /// </summary>
    public void RemoveOrgAdminPrivileges()
    {
        if (IsOrgAdmin)
        {
            IsOrgAdmin = false;
            AddDomainEvent(new EmployeeOrgAdminPrivilegesRemovedEvent(this));
        }
    }

    /// <summary>
    /// Grants approval authority
    /// </summary>
    public void GrantApprovalAuthority()
    {
        if (!HasApprovalAuthority)
        {
            HasApprovalAuthority = true;
            AddDomainEvent(new EmployeeApprovalAuthorityGrantedEvent(this));
        }
    }

    /// <summary>
    /// Revokes approval authority
    /// </summary>
    public void RevokeApprovalAuthority()
    {
        if (HasApprovalAuthority)
        {
            HasApprovalAuthority = false;
            AddDomainEvent(new EmployeeApprovalAuthorityRevokedEvent(this));
        }
    }

    /// <summary>
    /// Hides the employee
    /// </summary>
    public void Hide()
    {
        IsHidden = true;
    }

    /// <summary>
    /// Shows the employee
    /// </summary>
    public void Show()
    {
        IsHidden = false;
    }

    /// <summary>
    /// Deactivates the employee (soft delete)
    /// </summary>
    public void Deactivate()
    {
        if (IsDeleted)
            throw new InvalidOperationException("Employee is already deactivated");

        IsDeleted = true;
        DeletedAtUtc = DateTime.UtcNow;
        AddDomainEvent(new EmployeeDeactivatedEvent(this));
    }

    /// <summary>
    /// Reactivates the employee
    /// </summary>
    public void Reactivate()
    {
        if (!IsDeleted)
            throw new InvalidOperationException("Employee is already active");

        IsDeleted = false;
        DeletedAtUtc = null;
        DeletedBy = null;
        AddDomainEvent(new EmployeeReactivatedEvent(this));
    }

    /// <summary>
    /// Adds a role to the employee
    /// </summary>
    public void AddRole(EmployeeRole role)
    {
        if (role == null)
            throw new ArgumentNullException(nameof(role));

        if (_employeeRoles.Any(r => r.RoleId == role.RoleId))
            throw new InvalidOperationException("Employee already has this role");

        _employeeRoles.Add(role);
        AddDomainEvent(new EmployeeRoleAddedEvent(this, role));
    }

    /// <summary>
    /// Removes a role from the employee
    /// </summary>
    public void RemoveRole(Guid roleId)
    {
        var role = _employeeRoles.FirstOrDefault(r => r.RoleId == roleId);
        if (role != null)
        {
            _employeeRoles.Remove(role);
            AddDomainEvent(new EmployeeRoleRemovedEvent(this, role));
        }
    }

    /// <summary>
    /// Adds a leave record to the employee
    /// </summary>
    public void AddLeave(EmployeeLeave leave)
    {
        if (leave == null)
            throw new ArgumentNullException(nameof(leave));

        // Check for overlapping leave periods
        if (_employeeLeaves.Any(l => l.OverlapsWith(leave)))
            throw new InvalidOperationException("Leave period overlaps with existing leave");

        _employeeLeaves.Add(leave);
        AddDomainEvent(new EmployeeLeaveAddedEvent(this, leave));
    }

    // Private helper methods
    private void SetEmployeeCode(string employeeCode)
    {
        if (string.IsNullOrWhiteSpace(employeeCode))
            throw new ArgumentException("Employee code cannot be null or empty", nameof(employeeCode));

        if (employeeCode.Length > 50)
            throw new ArgumentException("Employee code cannot exceed 50 characters", nameof(employeeCode));

        EmployeeCode = employeeCode.Trim();
    }

    private void SetStandardWorkingHours(float standardWorkingHours)
    {
        if (standardWorkingHours <= 0)
            throw new ArgumentException("Standard working hours must be greater than 0", nameof(standardWorkingHours));

        if (standardWorkingHours > 24)
            throw new ArgumentException("Standard working hours cannot exceed 24 hours", nameof(standardWorkingHours));

        StandardWorkingHours = standardWorkingHours;
    }
}
