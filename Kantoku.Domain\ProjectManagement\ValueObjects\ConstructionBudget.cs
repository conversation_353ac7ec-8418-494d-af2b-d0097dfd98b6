using Kantoku.SharedKernel;

namespace Kantoku.Domain.ProjectManagement.ValueObjects;

/// <summary>
/// Value object representing construction budget information
/// </summary>
public class ConstructionBudget : ValueObject
{
    public decimal EstimatedCost { get; private set; }
    public decimal ActualCost { get; private set; }
    public string Currency { get; private set; } = "JPY";

    private ConstructionBudget() { } // For EF Core

    public ConstructionBudget(
        decimal estimatedCost,
        decimal actualCost = 0,
        string currency = "JPY")
    {
        SetEstimatedCost(estimatedCost);
        SetActualCost(actualCost);
        SetCurrency(currency);
    }

    /// <summary>
    /// Creates a budget with only estimated cost
    /// </summary>
    public static ConstructionBudget CreateEstimated(decimal estimatedCost, string currency = "JPY")
    {
        return new ConstructionBudget(estimatedCost, 0, currency);
    }

    /// <summary>
    /// Updates the actual cost
    /// </summary>
    public ConstructionBudget WithActualCost(decimal actualCost)
    {
        return new ConstructionBudget(EstimatedCost, actualCost, Currency);
    }

    /// <summary>
    /// Gets the cost variance (positive if over budget, negative if under budget)
    /// </summary>
    public decimal Variance => ActualCost - EstimatedCost;

    /// <summary>
    /// Gets the cost variance as a percentage
    /// </summary>
    public decimal VariancePercentage
    {
        get
        {
            if (EstimatedCost == 0)
                return 0;
            
            return (Variance / EstimatedCost) * 100;
        }
    }

    /// <summary>
    /// Checks if the construction is over budget
    /// </summary>
    public bool IsOverBudget => ActualCost > EstimatedCost;

    /// <summary>
    /// Checks if the construction is under budget
    /// </summary>
    public bool IsUnderBudget => ActualCost < EstimatedCost;

    /// <summary>
    /// Checks if the construction is on budget (within 5% tolerance)
    /// </summary>
    public bool IsOnBudget => Math.Abs(VariancePercentage) <= 5;

    /// <summary>
    /// Gets the remaining budget
    /// </summary>
    public decimal RemainingBudget => EstimatedCost - ActualCost;

    /// <summary>
    /// Gets the budget utilization percentage
    /// </summary>
    public decimal UtilizationPercentage
    {
        get
        {
            if (EstimatedCost == 0)
                return 0;
            
            return (ActualCost / EstimatedCost) * 100;
        }
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return EstimatedCost;
        yield return ActualCost;
        yield return Currency;
    }

    private void SetEstimatedCost(decimal estimatedCost)
    {
        if (estimatedCost < 0)
            throw new ArgumentException("Estimated cost cannot be negative", nameof(estimatedCost));

        EstimatedCost = estimatedCost;
    }

    private void SetActualCost(decimal actualCost)
    {
        if (actualCost < 0)
            throw new ArgumentException("Actual cost cannot be negative", nameof(actualCost));

        ActualCost = actualCost;
    }

    private void SetCurrency(string currency)
    {
        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("Currency cannot be null or empty", nameof(currency));

        if (currency.Length != 3)
            throw new ArgumentException("Currency must be a 3-character ISO code", nameof(currency));

        Currency = currency.ToUpperInvariant();
    }

    public override string ToString()
    {
        if (ActualCost > 0)
        {
            return $"Estimated: {EstimatedCost:N0} {Currency}, Actual: {ActualCost:N0} {Currency}, Variance: {Variance:N0} {Currency} ({VariancePercentage:F1}%)";
        }
        
        return $"Estimated: {EstimatedCost:N0} {Currency}";
    }
}
