using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class MonthlyReportQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedEmployee { get; set; } = false;
    public bool IncludedEmployeeShifts { get; set; } = false;
}

public interface IMonthlyReportQueryable
{
    IQueryable<MonthlyReport> GetMonthlyReportQuery(
        MonthlyReportQueryableOptions options
    );

    IQueryable<MonthlyReport> GetMonthlyReportQueryIncluded(
        MonthlyReportQueryableOptions options,
        IQueryable<MonthlyReport>? query = null
    );

    IQueryable<MonthlyReport> GetMonthlyReportQueryFiltered(
        MonthlyReportFilter filter,
        MonthlyReportQueryableOptions options,
        IQueryable<MonthlyReport>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class MonthlyReportQueryable(PostgreDbContext context) :
    BaseQueryable<MonthlyReport>(context), IMonthlyReportQueryable
{
    public IQueryable<MonthlyReport> GetMonthlyReportQuery(
        MonthlyReportQueryableOptions options
    )
    {
        var query = base.GetQuery(options);
        return query;
    }

    public IQueryable<MonthlyReport> GetMonthlyReportQueryIncluded(
        MonthlyReportQueryableOptions options,
        IQueryable<MonthlyReport>? query = null
    )
    {
        query ??= GetMonthlyReportQuery(options);
        if (options.IncludedEmployee || options.IncludedAll)
        {
            query = query.Include(s => s.Employee);
        }
        if (options.IncludedEmployeeShifts || options.IncludedAll)
        {
            query = query.Include(s => s.EmployeeShifts)
                .ThenInclude(es => es.Project);
        }

        return query;
    }

    public IQueryable<MonthlyReport> GetMonthlyReportQueryFiltered(
        MonthlyReportFilter filter,
        MonthlyReportQueryableOptions options,
        IQueryable<MonthlyReport>? query = null
    )
    {
        query ??= GetMonthlyReportQueryIncluded(options);
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(m => m.Employee.EmployeeName.Contains(filter.Keyword)
                || m.Employee.EmployeeCode.Contains(filter.Keyword));
        }
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(m => m.Employee.OrgUid == filter.OrgId);
        }
        if (DateOnly.TryParse(filter.ReportFrom, out var reportFrom))
        {
            query = query.Where(m => m.ReportFrom >= reportFrom);
        }
        if (DateOnly.TryParse(filter.ReportTo, out var reportTo))
        {
            query = query.Where(m => m.ReportTo <= reportTo);
        }
        return query;
    }
}
