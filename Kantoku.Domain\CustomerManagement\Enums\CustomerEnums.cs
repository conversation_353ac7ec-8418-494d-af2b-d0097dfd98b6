using Kantoku.SharedKernel;

namespace Kantoku.Domain.CustomerManagement.Enums;

/// <summary>
/// Enumeration representing customer status
/// </summary>
public class CustomerStatus : Enumeration
{
    public static readonly CustomerStatus Active = new(1, nameof(Active), "Active");
    public static readonly CustomerStatus Inactive = new(2, nameof(Inactive), "Inactive");
    public static readonly CustomerStatus Prospect = new(3, nameof(Prospect), "Prospect");
    public static readonly CustomerStatus Blacklisted = new(4, nameof(Blacklisted), "Blacklisted");
    public static readonly CustomerStatus Suspended = new(5, nameof(Suspended), "Suspended");

    public string DisplayName { get; private set; }

    private CustomerStatus(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<CustomerStatus> GetAll()
    {
        return new[] { Active, Inactive, Prospect, Blacklisted, Suspended };
    }

    public static CustomerStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown customer status: {name}");
        return status;
    }

    public static CustomerStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown customer status ID: {id}");
        return status;
    }

    public bool IsActive => this == Active;
    public bool CanDoBusinessWith => this == Active || this == Prospect;
    public bool IsRestricted => this == Blacklisted || this == Suspended;
}

/// <summary>
/// Enumeration representing contractor status
/// </summary>
public class ContractorStatus : Enumeration
{
    public static readonly ContractorStatus Active = new(1, nameof(Active), "Active");
    public static readonly ContractorStatus Inactive = new(2, nameof(Inactive), "Inactive");
    public static readonly ContractorStatus Qualified = new(3, nameof(Qualified), "Qualified");
    public static readonly ContractorStatus Unqualified = new(4, nameof(Unqualified), "Unqualified");
    public static readonly ContractorStatus Blacklisted = new(5, nameof(Blacklisted), "Blacklisted");
    public static readonly ContractorStatus Suspended = new(6, nameof(Suspended), "Suspended");

    public string DisplayName { get; private set; }

    private ContractorStatus(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<ContractorStatus> GetAll()
    {
        return new[] { Active, Inactive, Qualified, Unqualified, Blacklisted, Suspended };
    }

    public static ContractorStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown contractor status: {name}");
        return status;
    }

    public static ContractorStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown contractor status ID: {id}");
        return status;
    }

    public bool IsActive => this == Active || this == Qualified;
    public bool CanWork => this == Active || this == Qualified;
    public bool IsRestricted => this == Blacklisted || this == Suspended || this == Unqualified;
}

/// <summary>
/// Enumeration representing contact types
/// </summary>
public class ContactType : Enumeration
{
    public static readonly ContactType Primary = new(1, nameof(Primary), "Primary Contact");
    public static readonly ContactType Billing = new(2, nameof(Billing), "Billing Contact");
    public static readonly ContactType Technical = new(3, nameof(Technical), "Technical Contact");
    public static readonly ContactType Sales = new(4, nameof(Sales), "Sales Contact");
    public static readonly ContactType Support = new(5, nameof(Support), "Support Contact");
    public static readonly ContactType Emergency = new(6, nameof(Emergency), "Emergency Contact");
    public static readonly ContactType Legal = new(7, nameof(Legal), "Legal Contact");
    public static readonly ContactType Other = new(8, nameof(Other), "Other");

    public string DisplayName { get; private set; }

    private ContactType(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<ContactType> GetAll()
    {
        return new[] { Primary, Billing, Technical, Sales, Support, Emergency, Legal, Other };
    }

    public static ContactType FromName(string name)
    {
        var type = GetAll().FirstOrDefault(t => string.Equals(t.Name, name, StringComparison.OrdinalIgnoreCase));
        if (type == null)
            throw new ArgumentException($"Unknown contact type: {name}");
        return type;
    }

    public static ContactType FromId(int id)
    {
        var type = GetAll().FirstOrDefault(t => t.Id == id);
        if (type == null)
            throw new ArgumentException($"Unknown contact type ID: {id}");
        return type;
    }

    public bool IsPrimary => this == Primary;
    public bool IsFinancial => this == Billing;
    public bool IsOperational => this == Technical || this == Support;
}
