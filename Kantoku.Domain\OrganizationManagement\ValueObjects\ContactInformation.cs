using Kantoku.SharedKernel;

namespace Kantoku.Domain.OrganizationManagement.ValueObjects;

/// <summary>
/// Value object representing organization contact information
/// </summary>
public class ContactInformation : ValueObject
{
    public string? PhoneNumber { get; private set; }
    public string? Email { get; private set; }
    public string? Fax { get; private set; }
    public string? Website { get; private set; }

    private ContactInformation() { } // For EF Core

    public ContactInformation(
        string? phoneNumber = null,
        string? email = null,
        string? fax = null,
        string? website = null)
    {
        PhoneNumber = phoneNumber?.Trim();
        Email = email?.Trim();
        Fax = fax?.Trim();
        Website = website?.Trim();

        Validate();
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return PhoneNumber;
        yield return Email;
        yield return Fax;
        yield return Website;
    }

    private void Validate()
    {
        if (!string.IsNullOrEmpty(PhoneNumber) && PhoneNumber.Length > 20)
            throw new ArgumentException("Phone number cannot exceed 20 characters");

        if (!string.IsNullOrEmpty(Email))
        {
            if (Email.Length > 100)
                throw new ArgumentException("Email cannot exceed 100 characters");

            if (!IsValidEmail(Email))
                throw new ArgumentException("Invalid email format");
        }

        if (!string.IsNullOrEmpty(Fax) && Fax.Length > 20)
            throw new ArgumentException("Fax number cannot exceed 20 characters");

        if (!string.IsNullOrEmpty(Website))
        {
            if (Website.Length > 200)
                throw new ArgumentException("Website URL cannot exceed 200 characters");

            if (!IsValidUrl(Website))
                throw new ArgumentException("Invalid website URL format");
        }
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private static bool IsValidUrl(string url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
}
