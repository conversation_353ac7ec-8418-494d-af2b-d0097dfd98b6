using Kantoku.SharedKernel.BuildingBlocks;
using MediatR;

namespace Kantoku.SharedKernel.Events;

/// <summary>
/// Interface for handling domain events.
/// Domain event handlers contain the logic that should be executed when a specific domain event occurs.
/// </summary>
/// <typeparam name="TDomainEvent">The type of domain event to handle</typeparam>
public interface IDomainEventHandler<TDomainEvent> : INotificationHandler<TDomainEvent>
    where TDomainEvent : DomainEvent
{
    // Inherits Handle method from INotificationHandler<TDomainEvent>
    // Task Handle(TDomainEvent notification, CancellationToken cancellationToken);
}

/// <summary>
/// Base class for domain event handlers that provides common functionality
/// </summary>
/// <typeparam name="TDomainEvent">The type of domain event to handle</typeparam>
public abstract class DomainEventHandler<TDomainEvent> : IDomainEventHandler<TDomainEvent>
    where TDomainEvent : DomainEvent
{
    /// <summary>
    /// Handles the domain event
    /// </summary>
    /// <param name="domainEvent">The domain event to handle</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public abstract Task Handle(TDomainEvent domainEvent, CancellationToken cancellationToken);

    /// <summary>
    /// Logs information about the domain event being handled
    /// </summary>
    /// <param name="domainEvent">The domain event</param>
    /// <param name="message">Additional message to log</param>
    protected virtual void LogEventHandling(TDomainEvent domainEvent, string? message = null)
    {
        var eventType = domainEvent.GetType().Name;
        var eventId = domainEvent.EventId;
        var handlerType = GetType().Name;

        var logMessage = string.IsNullOrEmpty(message)
            ? $"Handling domain event {eventType} (ID: {eventId}) with {handlerType}"
            : $"Handling domain event {eventType} (ID: {eventId}) with {handlerType}: {message}";

        // Note: In a real implementation, you would use a proper logging framework
        // For now, this is just a placeholder for the logging concept
        Console.WriteLine($"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}] {logMessage}");
    }

    /// <summary>
    /// Validates the domain event before handling
    /// </summary>
    /// <param name="domainEvent">The domain event to validate</param>
    /// <returns>True if the event is valid; otherwise, false</returns>
    protected virtual bool ValidateEvent(TDomainEvent domainEvent)
    {
        if (domainEvent == null)
            return false;

        if (domainEvent.EventId == Guid.Empty)
            return false;

        if (domainEvent.DateOccurred == default)
            return false;

        return true;
    }

    /// <summary>
    /// Executes pre-handling logic
    /// </summary>
    /// <param name="domainEvent">The domain event</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A task representing the asynchronous operation</returns>
    protected virtual Task OnBeforeHandleAsync(TDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// Executes post-handling logic
    /// </summary>
    /// <param name="domainEvent">The domain event</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A task representing the asynchronous operation</returns>
    protected virtual Task OnAfterHandleAsync(TDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// Executes error handling logic
    /// </summary>
    /// <param name="domainEvent">The domain event</param>
    /// <param name="exception">The exception that occurred</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A task representing the asynchronous operation</returns>
    protected virtual Task OnErrorAsync(TDomainEvent domainEvent, Exception exception, CancellationToken cancellationToken)
    {
        // Note: In a real implementation, you would use a proper logging framework
        Console.WriteLine($"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}] Error handling domain event {domainEvent.GetType().Name} (ID: {domainEvent.EventId}): {exception.Message}");
        return Task.CompletedTask;
    }
}
