using Kantoku.Domain.ProjectManagement;
using Kantoku.Domain.ProjectManagement.ValueObjects;
using Kantoku.Domain.EmployeeManagement;
using Kantoku.Domain.InventoryManagement;

namespace Kantoku.Domain.DomainServices;

/// <summary>
/// Domain service for complex project cost calculations
/// </summary>
public class ProjectCostCalculationService
{
    /// <summary>
    /// Calculates total project cost including all constructions
    /// </summary>
    public ProjectCostSummary CalculateProjectCost(
        Project project,
        IEnumerable<Construction> constructions,
        IEnumerable<ConstructionCost> constructionCosts,
        IEnumerable<EmployeeShift> employeeShifts,
        IEnumerable<Item> materials)
    {
        if (project == null)
            throw new ArgumentNullException(nameof(project));

        var summary = new ProjectCostSummary
        {
            ProjectId = project.Id,
            ProjectName = project.ProjectName,
            Currency = project.Budget?.Currency ?? "JPY"
        };

        // Calculate construction costs
        var totalConstructionCost = constructionCosts.Sum(cc => cc.Amount);
        summary.ConstructionCosts = totalConstructionCost;

        // Calculate labor costs from employee shifts
        var totalLaborCost = CalculateLaborCosts(employeeShifts);
        summary.LaborCosts = totalLaborCost;

        // Calculate material costs
        var totalMaterialCost = CalculateMaterialCosts(materials, constructionCosts);
        summary.MaterialCosts = totalMaterialCost;

        // Calculate overhead (10% of direct costs)
        var directCosts = totalConstructionCost + totalLaborCost + totalMaterialCost;
        summary.OverheadCosts = directCosts * 0.10m;

        // Calculate total cost
        summary.TotalCost = directCosts + summary.OverheadCosts;

        // Calculate budget variance
        if (project.Budget != null)
        {
            summary.BudgetedAmount = project.Budget.EstimatedCost;
            summary.BudgetVariance = summary.TotalCost - summary.BudgetedAmount;
            summary.BudgetVariancePercentage = summary.BudgetedAmount > 0 
                ? (summary.BudgetVariance / summary.BudgetedAmount) * 100 
                : 0;
        }

        return summary;
    }

    /// <summary>
    /// Calculates cost per construction within a project
    /// </summary>
    public IEnumerable<ConstructionCostSummary> CalculateConstructionCosts(
        IEnumerable<Construction> constructions,
        IEnumerable<ConstructionCost> constructionCosts)
    {
        var result = new List<ConstructionCostSummary>();

        foreach (var construction in constructions)
        {
            var costs = constructionCosts.Where(cc => cc.ConstructionId == construction.Id);
            
            var summary = new ConstructionCostSummary
            {
                ConstructionId = construction.Id,
                ConstructionName = construction.ConstructionName,
                IsPrimary = construction.IsPrimary,
                Currency = construction.Budget?.Currency ?? "JPY"
            };

            // Group costs by type
            summary.MaterialCosts = costs.Where(c => c.CostType == "MATERIAL").Sum(c => c.Amount);
            summary.LaborCosts = costs.Where(c => c.CostType == "LABOR").Sum(c => c.Amount);
            summary.EquipmentCosts = costs.Where(c => c.CostType == "EQUIPMENT").Sum(c => c.Amount);
            summary.OtherCosts = costs.Where(c => c.CostType == "OTHER").Sum(c => c.Amount);
            summary.TotalCost = costs.Sum(c => c.Amount);

            // Calculate budget variance
            if (construction.Budget != null)
            {
                summary.BudgetedAmount = construction.Budget.EstimatedCost;
                summary.BudgetVariance = summary.TotalCost - summary.BudgetedAmount;
                summary.BudgetVariancePercentage = summary.BudgetedAmount > 0 
                    ? (summary.BudgetVariance / summary.BudgetedAmount) * 100 
                    : 0;
            }

            result.Add(summary);
        }

        return result;
    }

    /// <summary>
    /// Calculates projected final cost based on current progress
    /// </summary>
    public decimal CalculateProjectedFinalCost(
        Project project,
        decimal currentCost,
        decimal progressPercentage)
    {
        if (progressPercentage <= 0)
            return project.Budget?.EstimatedCost ?? currentCost;

        if (progressPercentage >= 100)
            return currentCost;

        // Simple projection: current cost / progress percentage
        var projectedCost = currentCost / (progressPercentage / 100);

        // Add contingency buffer (5% for projects over 50% complete, 10% for less)
        var contingencyRate = progressPercentage >= 50 ? 0.05m : 0.10m;
        return projectedCost * (1 + contingencyRate);
    }

    /// <summary>
    /// Calculates cost efficiency metrics
    /// </summary>
    public ProjectEfficiencyMetrics CalculateEfficiencyMetrics(
        Project project,
        decimal actualCost,
        decimal progressPercentage,
        int actualDays,
        int plannedDays)
    {
        var metrics = new ProjectEfficiencyMetrics
        {
            ProjectId = project.Id
        };

        // Cost Performance Index (CPI)
        var budgetedCost = project.Budget?.EstimatedCost ?? 0;
        var earnedValue = budgetedCost * (progressPercentage / 100);
        metrics.CostPerformanceIndex = actualCost > 0 ? earnedValue / actualCost : 1;

        // Schedule Performance Index (SPI)
        var plannedValue = budgetedCost * (actualDays / (decimal)plannedDays);
        metrics.SchedulePerformanceIndex = plannedValue > 0 ? earnedValue / plannedValue : 1;

        // Cost variance
        metrics.CostVariance = earnedValue - actualCost;
        metrics.CostVariancePercentage = budgetedCost > 0 ? (metrics.CostVariance / budgetedCost) * 100 : 0;

        // Schedule variance
        metrics.ScheduleVariance = earnedValue - plannedValue;
        metrics.ScheduleVariancePercentage = budgetedCost > 0 ? (metrics.ScheduleVariance / budgetedCost) * 100 : 0;

        return metrics;
    }

    // Private helper methods
    private decimal CalculateLaborCosts(IEnumerable<EmployeeShift> employeeShifts)
    {
        // This would need to be enhanced with actual employee salary information
        // For now, using a simplified calculation
        return employeeShifts.Sum(shift => shift.WorkingHours * 3000); // 3000 yen per hour average
    }

    private decimal CalculateMaterialCosts(IEnumerable<Item> materials, IEnumerable<ConstructionCost> constructionCosts)
    {
        // Material costs are already captured in construction costs with type "MATERIAL"
        return constructionCosts.Where(cc => cc.CostType == "MATERIAL").Sum(cc => cc.Amount);
    }
}

/// <summary>
/// Summary of project costs
/// </summary>
public class ProjectCostSummary
{
    public Guid ProjectId { get; set; }
    public string ProjectName { get; set; } = null!;
    public string Currency { get; set; } = "JPY";
    public decimal ConstructionCosts { get; set; }
    public decimal LaborCosts { get; set; }
    public decimal MaterialCosts { get; set; }
    public decimal OverheadCosts { get; set; }
    public decimal TotalCost { get; set; }
    public decimal BudgetedAmount { get; set; }
    public decimal BudgetVariance { get; set; }
    public decimal BudgetVariancePercentage { get; set; }
}

/// <summary>
/// Summary of construction costs
/// </summary>
public class ConstructionCostSummary
{
    public Guid ConstructionId { get; set; }
    public string ConstructionName { get; set; } = null!;
    public bool IsPrimary { get; set; }
    public string Currency { get; set; } = "JPY";
    public decimal MaterialCosts { get; set; }
    public decimal LaborCosts { get; set; }
    public decimal EquipmentCosts { get; set; }
    public decimal OtherCosts { get; set; }
    public decimal TotalCost { get; set; }
    public decimal BudgetedAmount { get; set; }
    public decimal BudgetVariance { get; set; }
    public decimal BudgetVariancePercentage { get; set; }
}

/// <summary>
/// Project efficiency metrics
/// </summary>
public class ProjectEfficiencyMetrics
{
    public Guid ProjectId { get; set; }
    public decimal CostPerformanceIndex { get; set; }
    public decimal SchedulePerformanceIndex { get; set; }
    public decimal CostVariance { get; set; }
    public decimal CostVariancePercentage { get; set; }
    public decimal ScheduleVariance { get; set; }
    public decimal ScheduleVariancePercentage { get; set; }
}
