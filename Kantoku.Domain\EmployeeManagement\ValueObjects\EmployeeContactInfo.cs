using Kantoku.SharedKernel;

namespace Kantoku.Domain.EmployeeManagement.ValueObjects;

/// <summary>
/// Value object representing employee contact information
/// </summary>
public class EmployeeContactInfo : ValueObject
{
    public IReadOnlyCollection<EmployeeEmail> Emails { get; private set; } = new List<EmployeeEmail>();
    public IReadOnlyCollection<EmployeePhone> Phones { get; private set; } = new List<EmployeePhone>();

    private EmployeeContactInfo() { } // For EF Core

    public EmployeeContactInfo(
        IEnumerable<EmployeeEmail>? emails = null,
        IEnumerable<EmployeePhone>? phones = null)
    {
        var emailList = emails?.ToList() ?? new List<EmployeeEmail>();
        var phoneList = phones?.ToList() ?? new List<EmployeePhone>();

        ValidateEmails(emailList);
        ValidatePhones(phoneList);

        Emails = emailList.AsReadOnly();
        Phones = phoneList.AsReadOnly();
    }

    public EmployeeEmail? GetPrimaryEmail()
    {
        return Emails.FirstOrDefault(e => e.IsPrimary) ?? Emails.FirstOrDefault();
    }

    public EmployeePhone? GetPrimaryPhone()
    {
        return Phones.FirstOrDefault(p => p.IsPrimary) ?? Phones.FirstOrDefault();
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        foreach (var email in Emails.OrderBy(e => e.Email))
            yield return email;
        
        foreach (var phone in Phones.OrderBy(p => p.Phone))
            yield return phone;
    }

    private static void ValidateEmails(List<EmployeeEmail> emails)
    {
        var primaryEmails = emails.Where(e => e.IsPrimary).ToList();
        if (primaryEmails.Count > 1)
            throw new ArgumentException("Only one email can be marked as primary");

        var duplicateEmails = emails.GroupBy(e => e.Email).Where(g => g.Count() > 1).ToList();
        if (duplicateEmails.Any())
            throw new ArgumentException("Duplicate email addresses are not allowed");
    }

    private static void ValidatePhones(List<EmployeePhone> phones)
    {
        var primaryPhones = phones.Where(p => p.IsPrimary).ToList();
        if (primaryPhones.Count > 1)
            throw new ArgumentException("Only one phone can be marked as primary");

        var duplicatePhones = phones.GroupBy(p => p.Phone).Where(g => g.Count() > 1).ToList();
        if (duplicatePhones.Any())
            throw new ArgumentException("Duplicate phone numbers are not allowed");
    }
}

/// <summary>
/// Value object representing an employee email
/// </summary>
public class EmployeeEmail : ValueObject
{
    public string Email { get; private set; } = null!;
    public bool IsPrimary { get; private set; }

    private EmployeeEmail() { } // For EF Core

    public EmployeeEmail(string email, bool isPrimary = false)
    {
        SetEmail(email);
        IsPrimary = isPrimary;
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Email;
        yield return IsPrimary;
    }

    private void SetEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be null or empty", nameof(email));

        if (email.Length > 100)
            throw new ArgumentException("Email cannot exceed 100 characters", nameof(email));

        if (!IsValidEmail(email))
            throw new ArgumentException("Invalid email format", nameof(email));

        Email = email.Trim().ToLowerInvariant();
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    public override string ToString() => Email;
}

/// <summary>
/// Value object representing an employee phone number
/// </summary>
public class EmployeePhone : ValueObject
{
    public string Phone { get; private set; } = null!;
    public bool IsPrimary { get; private set; }

    private EmployeePhone() { } // For EF Core

    public EmployeePhone(string phone, bool isPrimary = false)
    {
        SetPhone(phone);
        IsPrimary = isPrimary;
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Phone;
        yield return IsPrimary;
    }

    private void SetPhone(string phone)
    {
        if (string.IsNullOrWhiteSpace(phone))
            throw new ArgumentException("Phone cannot be null or empty", nameof(phone));

        if (phone.Length > 20)
            throw new ArgumentException("Phone cannot exceed 20 characters", nameof(phone));

        Phone = phone.Trim();
    }

    public override string ToString() => Phone;
}
