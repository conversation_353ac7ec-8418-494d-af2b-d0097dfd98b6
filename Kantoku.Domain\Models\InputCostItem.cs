namespace Kantoku.Domain.Models;

public class InputCostItem : AuditableEntity
{
    public Guid InputCostItemUid { get; set; }
    public Guid ConstructionUid { get; set; }
    public Guid? InputCostUid { get; set; }
    public Guid ItemUid { get; set; }
    public Guid? VendorUid { get; set; }
    public Guid OrgUid { get; set; }


    public DateOnly TransactionDate { get; set; }


    public string? Unit { get; set; }


    public float Quantity { get; set; }


    public int Price { get; set; }


    public float? TaxRate { get; set; }


    public long? TotalNonTaxed { get; set; }


    public long? TotalTaxed { get; set; }


    public string? Description { get; set; }

    public bool IsDeleted { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Construction Construction { get; set; } = null!;

    public virtual InputCost? InputCost { get; set; }

    public virtual Item Item { get; set; } = null!;

    public virtual Vendor? Vendor { get; set; }
}
