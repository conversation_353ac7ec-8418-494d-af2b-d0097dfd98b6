using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Databases.Models;

namespace Kantoku.Api.Dtos.Notification.Request;

public class CreateNotificationRequestDto
{
    /// <summary>
    /// Title of the notification
    /// </summary>
    [Required]
    public string Title { get; set; } = null!;

    /// <summary>
    /// Body of the notification
    /// </summary>
    [Required]
    public string Body { get; set; } = null!;

    /// <summary>
    /// Type of the notification (PUSH, EMAIL)
    /// </summary>
    [Required]
    public string NotificationType { get; set; } = NotificationTypeConstant.PUSH;

    /// <summary>
    /// Targets of the notification
    /// </summary>
    [Required]
    public IEnumerable<CreateNotificationTargetRequestDto> Targets { get; set; } = [];
}

public class CreateNotificationTargetRequestDto
{
    /// <summary>
    /// Target type of the notification (INDIVIDUAL, ROLE, ALL)
    /// </summary>
    [Required]
    public string TargetType { get; set; } = null!;

    /// <summary>
    /// Target ids of the notification
    /// </summary>
    [Required]
    public List<Guid> TargetIds { get; set; } = [];
}