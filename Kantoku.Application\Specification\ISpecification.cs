using System.Linq.Expressions;

namespace Kantoku.Application.Specification;

public interface ISpecification<TEntity>
{
    Expression<Func<TEntity, bool>>? Criteria { get; }
    Expression<Func<TEntity, object>>? OrderByExpression { get; }
    Expression<Func<TEntity, object>>? OrderByDescendingExpression { get; }
    Expression<Func<TEntity, object>>? GroupByExpression { get; }
    bool IsPagingEnabled { get; }
    int Take { get; }
    int Skip { get; }
    List<Expression<Func<TEntity, object>>> IncludesExpressions { get; }
}

public interface ISpecification<TEntity, TResult> : ISpecification<TEntity>
{

    Expression<Func<TEntity, TResult>>? SelectExpression { get; }
}
