using Kantoku.Domain.Common.BuildingBlocks;
using Kantoku.Domain.CustomerManagement.Enums;

namespace Kantoku.Domain.ContractorManagement;

/// <summary>
/// Entity representing a contact person for a contractor
/// </summary>
public class ContractorContact : Entity<Guid>
{
    public Guid ContractorId { get; private set; }
    public string FirstName { get; private set; } = null!;
    public string LastName { get; private set; } = null!;
    public string? MiddleName { get; private set; }
    public string? Title { get; private set; }
    public string? Department { get; private set; }
    public ContactType ContactType { get; private set; } = ContactType.Other;
    public string Email { get; private set; } = null!;
    public string? PhoneNumber { get; private set; }
    public string? MobileNumber { get; private set; }
    public string? Fax { get; private set; }
    public bool IsPrimary { get; private set; } = false;
    public string? Notes { get; private set; }

    // Private constructor for EF Core
    private ContractorContact() : base() { }

    /// <summary>
    /// Creates a new contractor contact
    /// </summary>
    public ContractorContact(
        Guid id,
        Guid contractorId,
        string firstName,
        string lastName,
        string email,
        ContactType? contactType = null,
        string? middleName = null,
        string? title = null,
        string? department = null,
        string? phoneNumber = null,
        string? mobileNumber = null,
        string? fax = null,
        bool isPrimary = false,
        string? notes = null) : base(id)
    {
        ContractorId = contractorId;
        SetFirstName(firstName);
        SetLastName(lastName);
        SetEmail(email);
        ContactType = contactType ?? ContactType.Other;
        MiddleName = middleName?.Trim();
        Title = title?.Trim();
        Department = department?.Trim();
        PhoneNumber = phoneNumber?.Trim();
        MobileNumber = mobileNumber?.Trim();
        Fax = fax?.Trim();
        IsPrimary = isPrimary;
        Notes = notes?.Trim();
    }

    /// <summary>
    /// Updates contact information
    /// </summary>
    public void UpdateContactInfo(
        string firstName,
        string lastName,
        string email,
        string? middleName = null,
        string? title = null,
        string? department = null,
        string? phoneNumber = null,
        string? mobileNumber = null,
        string? fax = null,
        string? notes = null)
    {
        SetFirstName(firstName);
        SetLastName(lastName);
        SetEmail(email);
        MiddleName = middleName?.Trim();
        Title = title?.Trim();
        Department = department?.Trim();
        PhoneNumber = phoneNumber?.Trim();
        MobileNumber = mobileNumber?.Trim();
        Fax = fax?.Trim();
        Notes = notes?.Trim();
    }

    /// <summary>
    /// Updates contact type
    /// </summary>
    public void UpdateContactType(ContactType contactType)
    {
        ContactType = contactType ?? throw new ArgumentNullException(nameof(contactType));
    }

    /// <summary>
    /// Sets this contact as primary
    /// </summary>
    public void SetAsPrimary()
    {
        IsPrimary = true;
    }

    /// <summary>
    /// Removes primary status
    /// </summary>
    public void RemovePrimaryStatus()
    {
        IsPrimary = false;
    }

    /// <summary>
    /// Gets the full name of the contact
    /// </summary>
    public string FullName
    {
        get
        {
            var parts = new List<string> { FirstName };
            if (!string.IsNullOrWhiteSpace(MiddleName))
                parts.Add(MiddleName);
            parts.Add(LastName);
            return string.Join(" ", parts);
        }
    }

    // Private helper methods
    private void SetFirstName(string firstName)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be null or empty", nameof(firstName));

        if (firstName.Length > 100)
            throw new ArgumentException("First name cannot exceed 100 characters", nameof(firstName));

        FirstName = firstName.Trim();
    }

    private void SetLastName(string lastName)
    {
        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be null or empty", nameof(lastName));

        if (lastName.Length > 100)
            throw new ArgumentException("Last name cannot exceed 100 characters", nameof(lastName));

        LastName = lastName.Trim();
    }

    private void SetEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be null or empty", nameof(email));

        if (email.Length > 255)
            throw new ArgumentException("Email cannot exceed 255 characters", nameof(email));

        if (!IsValidEmail(email))
            throw new ArgumentException("Invalid email format", nameof(email));

        Email = email.Trim().ToLowerInvariant();
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
