﻿namespace Kantoku.Domain.Models;

public class CustomerType
{
    public string CustomerTypeCode { get; set; } = null!;

    public ICollection<TranslatedCustomerType>? TranslatedCustomerType { get; set; }

    public virtual ICollection<Customer>? Customers { get; set; }
}

public class TranslatedCustomerType
{
    public string LanguageCode { get; set; } = null!;

    public string CustomerTypeName { get; set; } = null!;

    public string CustomerTypeDescription { get; set; } = null!;
}