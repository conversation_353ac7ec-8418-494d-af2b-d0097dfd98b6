using Kantoku.SharedKernel;

namespace Kantoku.Domain.NotificationManagement.Events;

/// <summary>
/// Domain event raised when a notification is created
/// </summary>
public class NotificationCreatedEvent : IDomainEvent
{
    public Notification Notification { get; }
    public DateTime OccurredOn { get; }

    public NotificationCreatedEvent(Notification notification)
    {
        Notification = notification;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a notification is updated
/// </summary>
public class NotificationUpdatedEvent : IDomainEvent
{
    public Notification Notification { get; }
    public DateTime OccurredOn { get; }

    public NotificationUpdatedEvent(Notification notification)
    {
        Notification = notification;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when notification scheduling is updated
/// </summary>
public class NotificationSchedulingUpdatedEvent : IDomainEvent
{
    public Notification Notification { get; }
    public DateTime OccurredOn { get; }

    public NotificationSchedulingUpdatedEvent(Notification notification)
    {
        Notification = notification;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a target is added to a notification
/// </summary>
public class NotificationTargetAddedEvent : IDomainEvent
{
    public Notification Notification { get; }
    public NotificationTarget Target { get; }
    public DateTime OccurredOn { get; }

    public NotificationTargetAddedEvent(Notification notification, NotificationTarget target)
    {
        Notification = notification;
        Target = target;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a target is removed from a notification
/// </summary>
public class NotificationTargetRemovedEvent : IDomainEvent
{
    public Notification Notification { get; }
    public NotificationTarget Target { get; }
    public DateTime OccurredOn { get; }

    public NotificationTargetRemovedEvent(Notification notification, NotificationTarget target)
    {
        Notification = notification;
        Target = target;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a notification is approved
/// </summary>
public class NotificationApprovedEvent : IDomainEvent
{
    public Notification Notification { get; }
    public DateTime OccurredOn { get; }

    public NotificationApprovedEvent(Notification notification)
    {
        Notification = notification;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a notification is rejected
/// </summary>
public class NotificationRejectedEvent : IDomainEvent
{
    public Notification Notification { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public NotificationRejectedEvent(Notification notification, string reason)
    {
        Notification = notification;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a notification is sent
/// </summary>
public class NotificationSentEvent : IDomainEvent
{
    public Notification Notification { get; }
    public DateTime OccurredOn { get; }

    public NotificationSentEvent(Notification notification)
    {
        Notification = notification;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a notification is cancelled
/// </summary>
public class NotificationCancelledEvent : IDomainEvent
{
    public Notification Notification { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public NotificationCancelledEvent(Notification notification, string reason)
    {
        Notification = notification;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a notification delivery is recorded
/// </summary>
public class NotificationDeliveryRecordedEvent : IDomainEvent
{
    public Notification Notification { get; }
    public NotificationDelivery Delivery { get; }
    public DateTime OccurredOn { get; }

    public NotificationDeliveryRecordedEvent(Notification notification, NotificationDelivery delivery)
    {
        Notification = notification;
        Delivery = delivery;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a notification target is marked as read
/// </summary>
public class NotificationReadEvent : IDomainEvent
{
    public Notification Notification { get; }
    public NotificationTarget Target { get; }
    public DateTime OccurredOn { get; }

    public NotificationReadEvent(Notification notification, NotificationTarget target)
    {
        Notification = notification;
        Target = target;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a notification target is dismissed
/// </summary>
public class NotificationDismissedEvent : IDomainEvent
{
    public Notification Notification { get; }
    public NotificationTarget Target { get; }
    public DateTime OccurredOn { get; }

    public NotificationDismissedEvent(Notification notification, NotificationTarget target)
    {
        Notification = notification;
        Target = target;
        OccurredOn = DateTime.UtcNow;
    }
}
