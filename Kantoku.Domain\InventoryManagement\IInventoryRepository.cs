using Kantoku.Domain.InventoryManagement.Enums;

namespace Kantoku.Domain.InventoryManagement;

/// <summary>
/// Repository interface for Inventory Management aggregates
/// </summary>
public interface IInventoryRepository
{
    #region Item Operations
    
    /// <summary>
    /// Gets an item by its ID
    /// </summary>
    Task<Item?> GetItemByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets an item by its code within an organization
    /// </summary>
    Task<Item?> GetItemByCodeAsync(Guid orgId, string itemCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all active items for an organization
    /// </summary>
    Task<IEnumerable<Item>> GetItemsByOrganizationAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets items by category within an organization
    /// </summary>
    Task<IEnumerable<Item>> GetItemsByCategoryAsync(Guid orgId, Guid categoryId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets items by status within an organization
    /// </summary>
    Task<IEnumerable<Item>> GetItemsByStatusAsync(Guid orgId, ItemStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets items that are low in stock
    /// </summary>
    Task<IEnumerable<Item>> GetLowStockItemsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets items that are out of stock
    /// </summary>
    Task<IEnumerable<Item>> GetOutOfStockItemsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets items that are overstocked
    /// </summary>
    Task<IEnumerable<Item>> GetOverstockedItemsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches items by name or code
    /// </summary>
    Task<IEnumerable<Item>> SearchItemsAsync(Guid orgId, string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an item with the given code exists within an organization
    /// </summary>
    Task<bool> ItemExistsByCodeAsync(Guid orgId, string itemCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an item with the given code exists within an organization (excluding the specified ID)
    /// </summary>
    Task<bool> ItemExistsByCodeAsync(Guid orgId, string itemCode, Guid excludeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new item
    /// </summary>
    Task AddItemAsync(Item item, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing item
    /// </summary>
    void UpdateItem(Item item);

    /// <summary>
    /// Removes an item
    /// </summary>
    void RemoveItem(Item item);

    #endregion

    #region Stock Operations

    /// <summary>
    /// Gets stock movements for a specific item
    /// </summary>
    Task<IEnumerable<ItemStock>> GetStockMovementsAsync(Guid itemId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets stock movements for a specific item within a date range
    /// </summary>
    Task<IEnumerable<ItemStock>> GetStockMovementsAsync(
        Guid itemId, 
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets stock movements by type for an organization
    /// </summary>
    Task<IEnumerable<ItemStock>> GetStockMovementsByTypeAsync(
        Guid orgId, 
        StockMovementType movementType, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets recent stock movements for an organization
    /// </summary>
    Task<IEnumerable<ItemStock>> GetRecentStockMovementsAsync(
        Guid orgId, 
        int days = 30, 
        CancellationToken cancellationToken = default);

    #endregion

    #region Price Operations

    /// <summary>
    /// Gets prices for a specific item
    /// </summary>
    Task<IEnumerable<ItemPrice>> GetItemPricesAsync(Guid itemId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active prices for a specific item
    /// </summary>
    Task<IEnumerable<ItemPrice>> GetActiveItemPricesAsync(Guid itemId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current price for a specific item and price type
    /// </summary>
    Task<ItemPrice?> GetCurrentItemPriceAsync(Guid itemId, string priceType = "STANDARD", CancellationToken cancellationToken = default);

    #endregion

    #region Category Operations

    /// <summary>
    /// Gets a category by its ID
    /// </summary>
    Task<Category?> GetCategoryByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a category by its code within an organization
    /// </summary>
    Task<Category?> GetCategoryByCodeAsync(Guid orgId, string categoryCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all active categories for an organization
    /// </summary>
    Task<IEnumerable<Category>> GetCategoriesByOrganizationAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets root categories (categories without parent) for an organization
    /// </summary>
    Task<IEnumerable<Category>> GetRootCategoriesAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets child categories for a specific parent category
    /// </summary>
    Task<IEnumerable<Category>> GetChildCategoriesAsync(Guid parentId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets categories by type within an organization
    /// </summary>
    Task<IEnumerable<Category>> GetCategoriesByTypeAsync(Guid orgId, string categoryType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a category with the given code exists within an organization
    /// </summary>
    Task<bool> CategoryExistsByCodeAsync(Guid orgId, string categoryCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a category with the given code exists within an organization (excluding the specified ID)
    /// </summary>
    Task<bool> CategoryExistsByCodeAsync(Guid orgId, string categoryCode, Guid excludeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new category
    /// </summary>
    Task AddCategoryAsync(Category category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing category
    /// </summary>
    void UpdateCategory(Category category);

    /// <summary>
    /// Removes a category
    /// </summary>
    void RemoveCategory(Category category);

    #endregion

    #region Reporting and Analytics

    /// <summary>
    /// Gets inventory summary for an organization
    /// </summary>
    Task<(int TotalItems, int ActiveItems, int LowStockItems, int OutOfStockItems)> GetInventorySummaryAsync(
        Guid orgId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets stock value summary for an organization
    /// </summary>
    Task<(decimal TotalValue, decimal AverageValue, int ItemsWithValue)> GetStockValueSummaryAsync(
        Guid orgId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets top items by stock movement volume
    /// </summary>
    Task<IEnumerable<(Item Item, decimal TotalMovement)>> GetTopItemsByMovementAsync(
        Guid orgId, 
        int topCount = 10, 
        int days = 30, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets items that haven't moved in a specified period
    /// </summary>
    Task<IEnumerable<Item>> GetStagnantItemsAsync(
        Guid orgId, 
        int days = 90, 
        CancellationToken cancellationToken = default);

    #endregion
}
