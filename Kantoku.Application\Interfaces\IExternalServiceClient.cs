using System.Threading;
using System.Threading.Tasks;

namespace Kantoku.Application.Interfaces
{
    /// <summary>
    /// Generic interface for clients that communicate with external services
    /// </summary>
    /// <typeparam name="TRequest">The type of request sent to the external service</typeparam>
    /// <typeparam name="TResponse">The type of response expected from the external service</typeparam>
    public interface IExternalServiceClient<in TRequest, TResponse>
    {
        /// <summary>
        /// Sends a request to an external service and receives a response
        /// </summary>
        /// <param name="request">The request to send</param>
        /// <param name="cancellationToken">A CancellationToken to observe while waiting for the task to complete</param>
        /// <returns>The response from the external service</returns>
        Task<TResponse> SendAsync(TRequest request, CancellationToken cancellationToken = default);
    }
} 