namespace Kantoku.SharedKernel.Time;

/// <summary>
/// Abstraction for getting the current time.
/// This interface allows for testable time-dependent code by providing a way to mock the current time.
/// </summary>
public interface IClock
{
    /// <summary>
    /// Gets the current UTC date and time
    /// </summary>
    DateTime UtcNow { get; }

    /// <summary>
    /// Gets the current local date and time
    /// </summary>
    DateTime Now { get; }

    /// <summary>
    /// Gets the current date (without time component)
    /// </summary>
    DateTime Today { get; }

    /// <summary>
    /// Gets the current UTC date and time as DateTimeOffset
    /// </summary>
    DateTimeOffset UtcNowOffset { get; }

    /// <summary>
    /// Gets the current local date and time as DateTimeOffset
    /// </summary>
    DateTimeOffset NowOffset { get; }
}
