﻿namespace Kantoku.Domain.Models;

public class Project : AuditableEntity
{
    public Guid ProjectUid { get; set; }
    public Guid OrgUid { get; set; }


    public string ProjectCode { get; set; } = null!;


    public string ProjectName { get; set; } = null!;


    public Guid? ProjectTypeUid { get; set; }


    public Guid? CustomerUid { get; set; }


    public Guid? ContractorUid { get; set; }


    public string? Address { get; set; }


    public string StatusCode { get; set; } = null!;


    public int MonthlyReportDate { get; set; }

    public bool IsDeleted { get; set; } = false;
    public bool IsOffice { get; set; } = false;
    public bool IsDefault { get; set; } = false;


    public DateOnly? ExpectedStartDate { get; set; }


    public DateOnly? ExpectedEndDate { get; set; }


    public DateOnly? ActualStartDate { get; set; }


    public DateOnly? ActualEndDate { get; set; }


    public float? InitialBudget { get; set; }


    public float? ActualBudget { get; set; }


    public string? Description { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ProjectType? ProjectType { get; set; }

    public virtual Contractor? Contractor { get; set; }

    public virtual Customer? Customer { get; set; }

    public virtual Status Status { get; set; } = null!;

    public virtual ICollection<Request> Requests { get; set; } = [];

    public virtual ICollection<ProjectSchedule> ProjectSchedules { get; set; } = [];

    public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = [];

    public virtual ICollection<ProjectManager> Managers { get; set; } = [];

    public virtual ICollection<ProjectWorkShift> ProjectWorkShifts { get; set; } = [];

    public virtual ICollection<Construction> Constructions { get; set; } = [];

    public virtual ICollection<ProjectDailyReport> ProjectDailyReports { get; set; } = [];

    public virtual ICollection<ProjectRankingCost> ProjectRankingCosts { get; set; } = [];
}
