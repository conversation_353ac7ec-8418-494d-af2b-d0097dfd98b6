using MediatR;

namespace Kantoku.Application.Interfaces
{
    /// <summary>
    /// Marker interface for Commands that don't return data (void operations)
    /// </summary>
    public interface ICommand : IRequest
    {
    }

    /// <summary>
    /// Interface for Commands that return data of type TResponse
    /// </summary>
    /// <typeparam name="TResponse">The type of the response</typeparam>
    public interface ICommand<out TResponse> : IRequest<TResponse>
    {
    }
} 