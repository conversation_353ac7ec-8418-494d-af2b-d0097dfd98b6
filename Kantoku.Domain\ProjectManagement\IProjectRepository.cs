namespace Kantoku.Domain.ProjectManagement;

/// <summary>
/// Repository interface for Project aggregate
/// </summary>
public interface IProjectRepository
{
    /// <summary>
    /// Gets a project by its ID
    /// </summary>
    Task<Project?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a project by its code within an organization
    /// </summary>
    Task<Project?> GetByCodeAsync(Guid orgId, string projectCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all active projects for an organization
    /// </summary>
    Task<IEnumerable<Project>> GetByOrganizationAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets projects by status within an organization
    /// </summary>
    Task<IEnumerable<Project>> GetByStatusAsync(Guid orgId, string status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets projects by customer within an organization
    /// </summary>
    Task<IEnumerable<Project>> GetByCustomerAsync(Guid orgId, Guid customerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets projects by contractor within an organization
    /// </summary>
    Task<IEnumerable<Project>> GetByContractorAsync(Guid orgId, Guid contractorId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets projects managed by a specific employee
    /// </summary>
    Task<IEnumerable<Project>> GetByManagerAsync(Guid employeeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets overdue projects within an organization
    /// </summary>
    Task<IEnumerable<Project>> GetOverdueProjectsAsync(Guid orgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a project with the given code exists within an organization
    /// </summary>
    Task<bool> ExistsByCodeAsync(Guid orgId, string projectCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a project with the given code exists within an organization (excluding the specified ID)
    /// </summary>
    Task<bool> ExistsByCodeAsync(Guid orgId, string projectCode, Guid excludeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new project
    /// </summary>
    Task AddAsync(Project project, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing project
    /// </summary>
    void Update(Project project);

    /// <summary>
    /// Removes a project
    /// </summary>
    void Remove(Project project);

    /// <summary>
    /// Gets project managers for a specific project
    /// </summary>
    Task<IEnumerable<ProjectManager>> GetManagersAsync(Guid projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets project schedules for a specific project
    /// </summary>
    Task<IEnumerable<ProjectSchedule>> GetSchedulesAsync(Guid projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets project schedules for a specific project within a date range
    /// </summary>
    Task<IEnumerable<ProjectSchedule>> GetSchedulesAsync(Guid projectId, DateOnly startDate, DateOnly endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets constructions for a specific project
    /// </summary>
    Task<IEnumerable<Construction>> GetConstructionsAsync(Guid projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets daily reports for a specific project
    /// </summary>
    Task<IEnumerable<ProjectDailyReport>> GetDailyReportsAsync(Guid projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets daily reports for a specific project within a date range
    /// </summary>
    Task<IEnumerable<ProjectDailyReport>> GetDailyReportsAsync(Guid projectId, DateOnly startDate, DateOnly endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a daily report for a specific project and date
    /// </summary>
    Task<ProjectDailyReport?> GetDailyReportAsync(Guid projectId, DateOnly reportDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets construction costs for a specific construction
    /// </summary>
    Task<IEnumerable<ConstructionCost>> GetConstructionCostsAsync(Guid constructionId, CancellationToken cancellationToken = default);
}
