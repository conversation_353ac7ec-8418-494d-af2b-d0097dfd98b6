using Kantoku.SharedKernel;

namespace Kantoku.Domain.OrganizationManagement;

/// <summary>
/// Entity representing a work shift definition within an organization
/// </summary>
public class WorkShift : Entity<Guid>
{
    public Guid OrgId { get; private set; }
    public string WorkShiftCode { get; private set; } = null!;
    public string WorkShiftName { get; private set; } = null!;
    public string? Description { get; private set; }
    public TimeOnly StartTime { get; private set; }
    public TimeOnly EndTime { get; private set; }
    public TimeSpan BreakDuration { get; private set; }
    public decimal WorkingHours { get; private set; }
    public bool IsDefault { get; private set; } = false;
    public bool IsDeleted { get; private set; } = false;

    // Private constructor for EF Core
    private WorkShift() : base() { }

    /// <summary>
    /// Creates a new work shift
    /// </summary>
    public WorkShift(
        Guid id,
        Guid orgId,
        string workShiftCode,
        string workShiftName,
        TimeOnly startTime,
        TimeOnly endTime,
        TimeSpan breakDuration,
        string? description = null) : base(id)
    {
        OrgId = orgId;
        SetWorkShiftCode(workShiftCode);
        SetWorkShiftName(workShiftName);
        SetTimes(startTime, endTime, breakDuration);
        Description = description;
    }

    /// <summary>
    /// Updates work shift information
    /// </summary>
    public void Update(
        string workShiftName,
        TimeOnly startTime,
        TimeOnly endTime,
        TimeSpan breakDuration,
        string? description = null)
    {
        SetWorkShiftName(workShiftName);
        SetTimes(startTime, endTime, breakDuration);
        Description = description;
    }

    /// <summary>
    /// Sets the work shift as default
    /// </summary>
    public void SetAsDefault()
    {
        IsDefault = true;
    }

    /// <summary>
    /// Unsets the work shift as default
    /// </summary>
    public void UnsetAsDefault()
    {
        IsDefault = false;
    }

    /// <summary>
    /// Soft deletes the work shift
    /// </summary>
    public void Delete()
    {
        IsDeleted = true;
    }

    /// <summary>
    /// Restores the work shift
    /// </summary>
    public void Restore()
    {
        IsDeleted = false;
    }

    /// <summary>
    /// Calculates if the shift spans midnight
    /// </summary>
    public bool SpansMidnight => EndTime < StartTime;

    /// <summary>
    /// Gets the total duration of the shift including break
    /// </summary>
    public TimeSpan TotalDuration
    {
        get
        {
            if (SpansMidnight)
            {
                return TimeSpan.FromDays(1) - (StartTime.ToTimeSpan() - EndTime.ToTimeSpan());
            }
            return EndTime.ToTimeSpan() - StartTime.ToTimeSpan();
        }
    }

    // Private helper methods
    private void SetWorkShiftCode(string workShiftCode)
    {
        if (string.IsNullOrWhiteSpace(workShiftCode))
            throw new ArgumentException("Work shift code cannot be null or empty", nameof(workShiftCode));

        if (workShiftCode.Length > 50)
            throw new ArgumentException("Work shift code cannot exceed 50 characters", nameof(workShiftCode));

        WorkShiftCode = workShiftCode.Trim();
    }

    private void SetWorkShiftName(string workShiftName)
    {
        if (string.IsNullOrWhiteSpace(workShiftName))
            throw new ArgumentException("Work shift name cannot be null or empty", nameof(workShiftName));

        if (workShiftName.Length > 200)
            throw new ArgumentException("Work shift name cannot exceed 200 characters", nameof(workShiftName));

        WorkShiftName = workShiftName.Trim();
    }

    private void SetTimes(TimeOnly startTime, TimeOnly endTime, TimeSpan breakDuration)
    {
        if (breakDuration < TimeSpan.Zero)
            throw new ArgumentException("Break duration cannot be negative", nameof(breakDuration));

        if (breakDuration > TimeSpan.FromHours(12))
            throw new ArgumentException("Break duration cannot exceed 12 hours", nameof(breakDuration));

        StartTime = startTime;
        EndTime = endTime;
        BreakDuration = breakDuration;

        // Calculate working hours
        var totalDuration = TotalDuration;
        var workingDuration = totalDuration - breakDuration;

        if (workingDuration < TimeSpan.Zero)
            throw new ArgumentException("Break duration cannot exceed total shift duration");

        WorkingHours = (decimal)workingDuration.TotalHours;
    }
}
