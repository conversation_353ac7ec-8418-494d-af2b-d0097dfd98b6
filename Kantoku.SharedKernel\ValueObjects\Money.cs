using Kantoku.SharedKernel.BuildingBlocks;
using Kantoku.SharedKernel.Guards;

namespace Kantoku.SharedKernel.ValueObjects;

/// <summary>
/// Value object representing a monetary amount with currency
/// </summary>
public sealed class Money : ValueObject
{
    /// <summary>
    /// Gets the amount of money
    /// </summary>
    public decimal Amount { get; private set; }

    /// <summary>
    /// Gets the currency code (ISO 4217)
    /// </summary>
    public string Currency { get; private set; }

    /// <summary>
    /// Initializes a new instance of the Money class
    /// </summary>
    /// <param name="amount">The monetary amount</param>
    /// <param name="currency">The currency code (ISO 4217)</param>
    /// <exception cref="ArgumentException">Thrown when the currency is invalid</exception>
    private Money(decimal amount, string currency)
    {
        Amount = amount;
        Currency = Guard.NotNullOrWhiteSpace(currency).ToUpperInvariant();

        if (!IsValidCurrency(Currency))
            throw new ArgumentException($"Invalid currency code: {currency}", nameof(currency));
    }

    /// <summary>
    /// Creates a new Money instance
    /// </summary>
    /// <param name="amount">The monetary amount</param>
    /// <param name="currency">The currency code (ISO 4217)</param>
    /// <returns>A new Money instance</returns>
    public static Money Create(decimal amount, string currency) => new(amount, currency);

    /// <summary>
    /// Creates a new Money instance with zero amount
    /// </summary>
    /// <param name="currency">The currency code (ISO 4217)</param>
    /// <returns>A new Money instance with zero amount</returns>
    public static Money Zero(string currency) => new(0, currency);

    /// <summary>
    /// Adds two Money instances
    /// </summary>
    /// <param name="left">The first Money instance</param>
    /// <param name="right">The second Money instance</param>
    /// <returns>The sum of the two Money instances</returns>
    /// <exception cref="InvalidOperationException">Thrown when currencies don't match</exception>
    public static Money operator +(Money left, Money right)
    {
        EnsureSameCurrency(left, right);
        return new Money(left.Amount + right.Amount, left.Currency);
    }

    /// <summary>
    /// Subtracts two Money instances
    /// </summary>
    /// <param name="left">The first Money instance</param>
    /// <param name="right">The second Money instance</param>
    /// <returns>The difference of the two Money instances</returns>
    /// <exception cref="InvalidOperationException">Thrown when currencies don't match</exception>
    public static Money operator -(Money left, Money right)
    {
        EnsureSameCurrency(left, right);
        return new Money(left.Amount - right.Amount, left.Currency);
    }

    /// <summary>
    /// Multiplies a Money instance by a scalar
    /// </summary>
    /// <param name="money">The Money instance</param>
    /// <param name="multiplier">The multiplier</param>
    /// <returns>The product of the Money instance and the multiplier</returns>
    public static Money operator *(Money money, decimal multiplier)
    {
        return new Money(money.Amount * multiplier, money.Currency);
    }

    /// <summary>
    /// Multiplies a Money instance by a scalar
    /// </summary>
    /// <param name="multiplier">The multiplier</param>
    /// <param name="money">The Money instance</param>
    /// <returns>The product of the Money instance and the multiplier</returns>
    public static Money operator *(decimal multiplier, Money money)
    {
        return money * multiplier;
    }

    /// <summary>
    /// Divides a Money instance by a scalar
    /// </summary>
    /// <param name="money">The Money instance</param>
    /// <param name="divisor">The divisor</param>
    /// <returns>The quotient of the Money instance and the divisor</returns>
    /// <exception cref="DivideByZeroException">Thrown when the divisor is zero</exception>
    public static Money operator /(Money money, decimal divisor)
    {
        if (divisor == 0)
            throw new DivideByZeroException("Cannot divide money by zero.");

        return new Money(money.Amount / divisor, money.Currency);
    }

    /// <summary>
    /// Determines whether this Money instance is equal to another object
    /// </summary>
    /// <param name="obj">The object to compare</param>
    /// <returns>True if the objects are equal; otherwise, false</returns>
    public override bool Equals(object? obj)
    {
        if (obj is not Money other) return false;
        return Amount == other.Amount && Currency == other.Currency;
    }

    /// <summary>
    /// Returns the hash code for this Money instance
    /// </summary>
    /// <returns>A hash code for this Money instance</returns>
    public override int GetHashCode()
    {
        return HashCode.Combine(Amount, Currency);
    }

    /// <summary>
    /// Compares two Money instances for equality
    /// </summary>
    /// <param name="left">The first Money instance</param>
    /// <param name="right">The second Money instance</param>
    /// <returns>True if the Money instances are equal; otherwise, false</returns>
    public static bool operator ==(Money? left, Money? right)
    {
        if (left is null && right is null) return true;
        if (left is null || right is null) return false;
        return left.Equals(right);
    }

    /// <summary>
    /// Compares two Money instances for inequality
    /// </summary>
    /// <param name="left">The first Money instance</param>
    /// <param name="right">The second Money instance</param>
    /// <returns>True if the Money instances are not equal; otherwise, false</returns>
    public static bool operator !=(Money? left, Money? right) => !(left == right);

    /// <summary>
    /// Determines whether this Money instance is greater than another
    /// </summary>
    /// <param name="left">The first Money instance</param>
    /// <param name="right">The second Money instance</param>
    /// <returns>True if the first Money instance is greater; otherwise, false</returns>
    /// <exception cref="InvalidOperationException">Thrown when currencies don't match</exception>
    public static bool operator >(Money left, Money right)
    {
        EnsureSameCurrency(left, right);
        return left.Amount > right.Amount;
    }

    /// <summary>
    /// Determines whether this Money instance is less than another
    /// </summary>
    /// <param name="left">The first Money instance</param>
    /// <param name="right">The second Money instance</param>
    /// <returns>True if the first Money instance is less; otherwise, false</returns>
    /// <exception cref="InvalidOperationException">Thrown when currencies don't match</exception>
    public static bool operator <(Money left, Money right)
    {
        EnsureSameCurrency(left, right);
        return left.Amount < right.Amount;
    }

    /// <summary>
    /// Determines whether this Money instance is greater than or equal to another
    /// </summary>
    /// <param name="left">The first Money instance</param>
    /// <param name="right">The second Money instance</param>
    /// <returns>True if the first Money instance is greater than or equal; otherwise, false</returns>
    /// <exception cref="InvalidOperationException">Thrown when currencies don't match</exception>
    public static bool operator >=(Money left, Money right)
    {
        EnsureSameCurrency(left, right);
        return left.Amount >= right.Amount;
    }

    /// <summary>
    /// Determines whether this Money instance is less than or equal to another
    /// </summary>
    /// <param name="left">The first Money instance</param>
    /// <param name="right">The second Money instance</param>
    /// <returns>True if the first Money instance is less than or equal; otherwise, false</returns>
    /// <exception cref="InvalidOperationException">Thrown when currencies don't match</exception>
    public static bool operator <=(Money left, Money right)
    {
        EnsureSameCurrency(left, right);
        return left.Amount <= right.Amount;
    }

    /// <summary>
    /// Returns the absolute value of the Money instance
    /// </summary>
    /// <returns>A new Money instance with the absolute amount</returns>
    public Money Abs() => new(Math.Abs(Amount), Currency);

    /// <summary>
    /// Rounds the Money instance to the specified number of decimal places
    /// </summary>
    /// <param name="decimals">The number of decimal places</param>
    /// <param name="midpointRounding">The rounding strategy</param>
    /// <returns>A new Money instance with the rounded amount</returns>
    public Money Round(int decimals = 2, MidpointRounding midpointRounding = MidpointRounding.ToEven)
    {
        return new Money(Math.Round(Amount, decimals, midpointRounding), Currency);
    }

    /// <summary>
    /// Determines whether the Money instance is zero
    /// </summary>
    /// <returns>True if the amount is zero; otherwise, false</returns>
    public bool IsZero() => Amount == 0;

    /// <summary>
    /// Determines whether the Money instance is positive
    /// </summary>
    /// <returns>True if the amount is positive; otherwise, false</returns>
    public bool IsPositive() => Amount > 0;

    /// <summary>
    /// Determines whether the Money instance is negative
    /// </summary>
    /// <returns>True if the amount is negative; otherwise, false</returns>
    public bool IsNegative() => Amount < 0;

    /// <summary>
    /// Returns the string representation of the Money instance
    /// </summary>
    /// <returns>The Money instance as a formatted string</returns>
    public override string ToString() => $"{Amount:N2} {Currency}";

    /// <summary>
    /// Gets the equality components for value object comparison
    /// </summary>
    /// <returns>The equality components</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Amount;
        yield return Currency;
    }

    /// <summary>
    /// Ensures that two Money instances have the same currency
    /// </summary>
    /// <param name="left">The first Money instance</param>
    /// <param name="right">The second Money instance</param>
    /// <exception cref="InvalidOperationException">Thrown when currencies don't match</exception>
    private static void EnsureSameCurrency(Money left, Money right)
    {
        if (left.Currency != right.Currency)
            throw new InvalidOperationException($"Cannot perform operation on different currencies: {left.Currency} and {right.Currency}");
    }

    /// <summary>
    /// Validates a currency code (basic validation for common ISO 4217 codes)
    /// </summary>
    /// <param name="currency">The currency code to validate</param>
    /// <returns>True if the currency code is valid; otherwise, false</returns>
    private static bool IsValidCurrency(string currency)
    {
        // Basic validation - should be 3 uppercase letters
        if (currency.Length != 3)
            return false;

        // Common currency codes (this could be expanded or moved to a configuration)
        var commonCurrencies = new HashSet<string>
        {
            "USD", "EUR", "JPY", "GBP", "AUD", "CAD", "CHF", "CNY", "SEK", "NZD",
            "MXN", "SGD", "HKD", "NOK", "TRY", "ZAR", "BRL", "INR", "KRW", "PLN"
        };

        return commonCurrencies.Contains(currency) || currency.All(char.IsLetter);
    }
}
