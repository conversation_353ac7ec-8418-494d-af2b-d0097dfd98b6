using System;
using System.Security.Claims;
using Kantoku.Application.Interfaces;
using Microsoft.AspNetCore.Http;

namespace Kantoku.Persistence.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        public Guid? UserId
        {
            get
            {
                // Attempt to get the user ID from the NameIdentifier claim first
                var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (Guid.TryParse(userIdClaim, out var userId))
                {
                    return userId;
                }

                // Fallback or alternative claim for user ID (e.g., "sub" or a custom claim)
                // var subClaim = _httpContextAccessor.HttpContext?.User?.FindFirstValue("sub");
                // if (Guid.TryParse(subClaim, out userId))
                // {
                //     return userId;
                // }
                
                // Add more fallbacks if your user ID is stored in other claims

                return null; // Or throw an exception if UserId is expected but not found
            }
        }
    }
} 