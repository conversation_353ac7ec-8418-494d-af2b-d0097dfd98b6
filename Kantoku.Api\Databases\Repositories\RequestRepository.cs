using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Databases.Queryables;

namespace Kantoku.Api.Databases.Repositories;

public interface IRequestRepository
{
    Task<(IEnumerable<Request>, int)> GetByFilter(RequestFilter filter, RequestQueryableOptions options);
    Task<(IEnumerable<Request>, int)> GetByAuthor(Guid authorId, RequestFilter filter, RequestQueryableOptions options);
    Task<(IEnumerable<Request>, int)> GetByManager(Guid managerId, RequestFilter filter, RequestQueryableOptions options);
    Task<(IEnumerable<Request>, int)> GetByProject(Guid projectId, RequestFilter filter, RequestQueryableOptions options);
    Task<Request?> GetById(Guid requestId, RequestQueryableOptions options);
    Task<Request?> Create(Request request, RequestQueryableOptions options);
    Task<Request?> Update(Request request, RequestQueryableOptions options);
}

[Repository(ServiceLifetime.Scoped)]
public class RequestRepository : BaseRepository<Request>, IRequestRepository
{
    private readonly IRequestQueryable requestQueryable;

    public RequestRepository(
        PostgreDbContext context,
        IRequestQueryable requestQueryable,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    ) : base(context, logger, httpContextAccessor)
    {
        this.requestQueryable = requestQueryable;
    }

    public async Task<Request?> GetById(Guid requestId, RequestQueryableOptions options)
    {
        try
        {
            var query = requestQueryable.GetRequestQueryIncluded(options)
                .Where(r => r.RequestUid == requestId);
                
            var result = await query.FirstOrDefaultAsync();
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting request with requestId {RequestId}", requestId);
            return null;
        }
    }

    public async Task<(IEnumerable<Request>, int)> GetByFilter(RequestFilter filter, RequestQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = requestQueryable.GetRequestQueryFiltered(filter, options);

            var result = await query
                .OrderBy(r => r.CreatedTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all paginated requests");
            return ([], 0);
        }
    }

    public async Task<(IEnumerable<Request>, int)> GetByAuthor(Guid authorId, RequestFilter filter, RequestQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = requestQueryable.GetRequestQueryFiltered(filter, options)
                .Where(r => r.AuthorUid == authorId);

            var result = await query
                .OrderBy(r => r.CreatedTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error("Error get requests by user: {error}", ex);
            return ([], 0);
        }
    }

    public async Task<(IEnumerable<Request>, int)> GetByManager(Guid managerId, RequestFilter filter, RequestQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = requestQueryable.GetRequestQueryFiltered(filter, options)
                .Where(r => r.Project.Managers.Any(pm => pm.EmployeeUid == managerId));

            var result = await query
                .OrderBy(r => r.CreatedTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();

            return (result, total);
        }
        catch (System.Exception ex)
        {
            logger.Error("Error: {error}", ex);
            return ([], 0);
        }
    }

    public async Task<(IEnumerable<Request>, int)> GetByProject(Guid projectId, RequestFilter filter, RequestQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = requestQueryable.GetRequestQueryFiltered(filter, options)
                .Where(r => r.ProjectUid == projectId);

            var result = await query
                .OrderBy(r => r.CreatedTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();

            return (result, total);
        }
        catch (System.Exception ex)
        {
            logger.Error("Error: {error}", ex);
            return ([], 0);
        }
    }

    public async Task<Request?> Create(Request request, RequestQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Requests.AddAsync(request);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(request.RequestUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating request {RequestId}", request.RequestUid);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Request?> Update(Request request, RequestQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Requests.Update(request);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(request.RequestUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating request {RequestId}", request.RequestUid);
            await transaction.RollbackAsync();
            return null;
        }
    }
}
