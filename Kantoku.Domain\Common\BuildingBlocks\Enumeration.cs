using System.Reflection;

namespace Kantoku.Domain.Common.BuildingBlocks;

public abstract class Enumeration<TEnum, TValue> : IEquatable<Enumeration<TEnum, TValue>>
    where TEnum : Enumeration<TEnum, TValue>
    where TValue : notnull
{
    private static readonly Lazy<Dictionary<TValue, TEnum>> LazyEnumerations = new(Init);
    private static Dictionary<TValue, TEnum> Enumerations => LazyEnumerations.Value;

    public TValue Value { get; protected init; }
    public string Name { get; protected init; } = string.Empty;

    protected Enumeration(TValue value, string name)
    {
        if (value is null)
        {
            throw new ArgumentNullException(nameof(value));
        }
        Value = value;
        Name = name;
    }

    public static TEnum? FromValue(TValue value)
    {
        if (value is null)
        {
            return default;
        }
        return Enumerations
            .TryGetValue(value, out var enumeration)
            ? enumeration
            : default;
    }

    public static TEnum? FromName(string name)
    {
        return Enumerations.Values.SingleOrDefault(x => x.Name == name);
    }

    private static Dictionary<TValue, TEnum> Init()
    {
        var enumType = typeof(TEnum);
        var fields = enumType.GetFields(
                BindingFlags.Public |
                BindingFlags.Static |
                BindingFlags.DeclaredOnly)
            .Where(f => enumType.IsAssignableFrom(f.FieldType))
            .Select(f => (TEnum)f.GetValue(null)!)
            .ToDictionary(x => x.Value, x => x);

        return fields;
    }

    public override bool Equals(object? obj)
    {
        if (obj is not Enumeration<TEnum, TValue> otherValue)
        {
            return false;
        }

        return Equals(otherValue);
    }

    public bool Equals(Enumeration<TEnum, TValue>? other)
    {
        if (other is null)
        {
            return false;
        }

        // Type check is important for correctness
        if (GetType() != other.GetType())
        {
            return false;
        }

        // Use EqualityComparer<TValue> for robust equality, especially for generics
        return EqualityComparer<TValue>.Default.Equals(Value, other.Value);
    }

    public override int GetHashCode()
    {
        // Use EqualityComparer<TValue> for robust hash code generation
        return EqualityComparer<TValue>.Default.GetHashCode(Value);
    }
}
