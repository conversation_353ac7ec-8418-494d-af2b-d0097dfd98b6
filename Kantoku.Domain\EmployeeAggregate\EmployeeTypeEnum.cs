using Kantoku.Domain.Common.BuildingBlocks;

namespace Kantoku.Domain.EmployeeAggregate;

public class EmployeeTypeEnum : Enumeration<EmployeeTypeEnum, bool>
{
    public static readonly EmployeeTypeEnum OFFICER = new(true, nameof(OFFICER));
    public static readonly EmployeeTypeEnum WORKER = new(false, nameof(WORKER));

    private EmployeeTypeEnum(bool value, string name) : base(value, name)
    {
    }
}
