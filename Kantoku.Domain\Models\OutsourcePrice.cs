namespace Kantoku.Domain.Models;

public class OutSourcePrice : AuditableEntity
{
    public Guid OutSourcePriceUid { get; set; }
    public Guid OutSourceUid { get; set; }

    public DateOnly? EffectiveDate { get; set; }


    public int? PricePerDay { get; set; }


    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;

    public virtual OutSource OutSource { get; set; } = null!;
}

