namespace Kantoku.Domain.Models;

public class EventCalendar : AuditableEntity
{
    public Guid EventUid { get; set; }


    public string EventName { get; set; } = null!;


    public DateOnly? EventStartDate { get; set; }


    public DateOnly? EventEndDate { get; set; }


    public TimeOnly? EventStartTime { get; set; }


    public TimeOnly? EventEndTime { get; set; }

    // true: recurring, false: not recurring

    public bool IsRecurring { get; set; }


    public DateOnly? RecurringFrom { get; set; }


    public DateOnly? RecurringTo { get; set; }

    // Recurring type: daily, weekly, monthly, yearly 

    public string? RecurringType { get; set; }

    // only if RecurringType is weekly
    // for example: [1, 2, 3, 4, 5, 6, 7] as day of week 
    // 1: Monday, 2: Tuesday, 3: Wednesday, 4: Thursday, 5: Friday, 6: Saturday, 7: Sunday

    public List<int>? RecurringDay { get; set; }

    // only if RecurringType is weekly
    // for example: [1, 2, 3, 4] as week of month
    // 1: first week, 2: second week, 3: third week, 4: fourth week

    public List<int>? RecurringWeek { get; set; }

    // only if RecurringType is monthly
    // for example: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as month of year
    // 1: January, 2: February, 3: March, 4: April, 5: May, 6: June, 7: July, 8: August, 9: September, 10: October, 11: November, 12: December

    public List<int>? RecurringMonth { get; set; }


    public string? Description { get; set; }


    public bool IsDayOff { get; set; } = true;

    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }

    public virtual Org? Org { get; set; }
}
