using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Filters.Domains;

namespace Kantoku.Api.Databases.Repositories;

public interface IOutSourceShiftRepository
{
    Task<(IEnumerable<OutSourceShift>, int)> GetByFilter(OutSourceShiftFilter filter, OutSourceShiftQueryableOptions options);
    Task<OutSourceShift?> GetById(Guid outSourceShiftId, OutSourceShiftQueryableOptions options);
    Task<IEnumerable<OutSourceShift>> GetByProjectScheduleId(Guid projectScheduleId, OutSourceShiftQueryableOptions options);
    Task<OutSourceShift?> Create(OutSourceShift outSourceShift, OutSourceShiftQueryableOptions options);
    Task<OutSourceShift?> Update(OutSourceShift outSourceShift, OutSourceShiftQueryableOptions options);
}

[Repository(ServiceLifetime.Scoped)]
public class OutSourceShiftRepository : BaseRepository<OutSourceShift>, IOutSourceShiftRepository
{
    private readonly IOutSourceShiftQueryable outSourceShiftQueryable;
    public OutSourceShiftRepository(
        PostgreDbContext context,
        IOutSourceShiftQueryable outSourceShiftQueryable,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
        : base(context, logger, httpContextAccessor)
    {
        this.outSourceShiftQueryable = outSourceShiftQueryable;
    }

    public async Task<(IEnumerable<OutSourceShift>, int)> GetByFilter(OutSourceShiftFilter filter, OutSourceShiftQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = outSourceShiftQueryable.GetOutSourceShiftQueryFiltered(filter, options);

            var outSourceShifts = await query
                .OrderBy(p => p.OutSourceShiftUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();
            return (outSourceShifts, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all outSourceShifts");
            return ([], 0);
        }
    }

    public async Task<OutSourceShift?> GetById(Guid outSourceShiftId, OutSourceShiftQueryableOptions options)
    {
        try
        {
            var query = outSourceShiftQueryable.GetOutSourceShiftQueryIncluded(options)
                .Where(p => p.OutSourceShiftUid == outSourceShiftId);

            var outSourceShift = await query
                .FirstOrDefaultAsync();

            return outSourceShift;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outSourceShift by id {OutSourceShiftId}", outSourceShiftId);
            return null;
        }
    }

    public async Task<IEnumerable<OutSourceShift>> GetByProjectScheduleId(Guid projectScheduleId, OutSourceShiftQueryableOptions options)
    {
        try
        {
            var query = outSourceShiftQueryable.GetOutSourceShiftQueryIncluded(options)
                .Where(p => p.ProjectScheduleUid == projectScheduleId);

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outSourceShifts by project schedule id {ProjectScheduleId}", projectScheduleId);
            return [];
        }
    }

    public async Task<OutSourceShift?> Create(OutSourceShift outSourceShift, OutSourceShiftQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.OutSourceShifts.AddAsync(outSourceShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(outSourceShift.OutSourceShiftUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating outSourceShift {OutSourceShift}", outSourceShift);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<OutSourceShift?> Update(OutSourceShift outSourceShift, OutSourceShiftQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.OutSourceShifts.Update(outSourceShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(outSourceShift.OutSourceShiftUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating outSourceShift {OutSourceShift}", outSourceShift);
            await transaction.RollbackAsync();
            return null;
        }
    }
}

