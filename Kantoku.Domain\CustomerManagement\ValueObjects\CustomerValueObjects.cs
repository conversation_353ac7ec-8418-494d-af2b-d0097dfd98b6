using Kantoku.SharedKernel;

namespace Kantoku.Domain.CustomerManagement.ValueObjects;

/// <summary>
/// Value object representing customer address
/// </summary>
public class CustomerAddress : ValueObject
{
    public string? PostalCode { get; private set; }
    public string? Address { get; private set; }
    public string? City { get; private set; }
    public string? State { get; private set; }
    public string? Country { get; private set; }

    private CustomerAddress() { } // For EF Core

    public CustomerAddress(
        string? postalCode = null,
        string? address = null,
        string? city = null,
        string? state = null,
        string? country = null)
    {
        PostalCode = postalCode?.Trim();
        Address = address?.Trim();
        City = city?.Trim();
        State = state?.Trim();
        Country = country?.Trim();

        Validate();
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return PostalCode;
        yield return Address;
        yield return City;
        yield return State;
        yield return Country;
    }

    private void Validate()
    {
        if (!string.IsNullOrEmpty(PostalCode) && PostalCode.Length > 20)
            throw new ArgumentException("Postal code cannot exceed 20 characters");

        if (!string.IsNullOrEmpty(Address) && Address.Length > 500)
            throw new ArgumentException("Address cannot exceed 500 characters");

        if (!string.IsNullOrEmpty(City) && City.Length > 100)
            throw new ArgumentException("City cannot exceed 100 characters");

        if (!string.IsNullOrEmpty(State) && State.Length > 100)
            throw new ArgumentException("State cannot exceed 100 characters");

        if (!string.IsNullOrEmpty(Country) && Country.Length > 100)
            throw new ArgumentException("Country cannot exceed 100 characters");
    }

    public override string ToString()
    {
        var parts = new List<string>();
        
        if (!string.IsNullOrEmpty(Address))
            parts.Add(Address);
            
        if (!string.IsNullOrEmpty(City))
            parts.Add(City);
            
        if (!string.IsNullOrEmpty(State))
            parts.Add(State);
            
        if (!string.IsNullOrEmpty(PostalCode))
            parts.Add(PostalCode);
            
        if (!string.IsNullOrEmpty(Country))
            parts.Add(Country);

        return string.Join(", ", parts);
    }
}

/// <summary>
/// Value object representing customer contact information
/// </summary>
public class CustomerContactInfo : ValueObject
{
    public string? PhoneNumber { get; private set; }
    public string? Email { get; private set; }
    public string? Fax { get; private set; }
    public string? MobileNumber { get; private set; }

    private CustomerContactInfo() { } // For EF Core

    public CustomerContactInfo(
        string? phoneNumber = null,
        string? email = null,
        string? fax = null,
        string? mobileNumber = null)
    {
        PhoneNumber = phoneNumber?.Trim();
        Email = email?.Trim();
        Fax = fax?.Trim();
        MobileNumber = mobileNumber?.Trim();

        Validate();
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return PhoneNumber;
        yield return Email;
        yield return Fax;
        yield return MobileNumber;
    }

    private void Validate()
    {
        if (!string.IsNullOrEmpty(PhoneNumber) && PhoneNumber.Length > 20)
            throw new ArgumentException("Phone number cannot exceed 20 characters");

        if (!string.IsNullOrEmpty(Email))
        {
            if (Email.Length > 100)
                throw new ArgumentException("Email cannot exceed 100 characters");

            if (!IsValidEmail(Email))
                throw new ArgumentException("Invalid email format");
        }

        if (!string.IsNullOrEmpty(Fax) && Fax.Length > 20)
            throw new ArgumentException("Fax number cannot exceed 20 characters");

        if (!string.IsNullOrEmpty(MobileNumber) && MobileNumber.Length > 20)
            throw new ArgumentException("Mobile number cannot exceed 20 characters");
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Value object representing customer business information
/// </summary>
public class CustomerBusinessInfo : ValueObject
{
    public string? TaxNumber { get; private set; }
    public string? RegistrationNumber { get; private set; }
    public string? Industry { get; private set; }
    public int? EmployeeCount { get; private set; }
    public decimal? AnnualRevenue { get; private set; }
    public string? RevenueCurrency { get; private set; }

    private CustomerBusinessInfo() { } // For EF Core

    public CustomerBusinessInfo(
        string? taxNumber = null,
        string? registrationNumber = null,
        string? industry = null,
        int? employeeCount = null,
        decimal? annualRevenue = null,
        string? revenueCurrency = null)
    {
        TaxNumber = taxNumber?.Trim();
        RegistrationNumber = registrationNumber?.Trim();
        Industry = industry?.Trim();
        EmployeeCount = employeeCount;
        AnnualRevenue = annualRevenue;
        RevenueCurrency = revenueCurrency?.Trim()?.ToUpperInvariant();

        Validate();
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return TaxNumber;
        yield return RegistrationNumber;
        yield return Industry;
        yield return EmployeeCount;
        yield return AnnualRevenue;
        yield return RevenueCurrency;
    }

    private void Validate()
    {
        if (!string.IsNullOrEmpty(TaxNumber) && TaxNumber.Length > 50)
            throw new ArgumentException("Tax number cannot exceed 50 characters");

        if (!string.IsNullOrEmpty(RegistrationNumber) && RegistrationNumber.Length > 50)
            throw new ArgumentException("Registration number cannot exceed 50 characters");

        if (!string.IsNullOrEmpty(Industry) && Industry.Length > 100)
            throw new ArgumentException("Industry cannot exceed 100 characters");

        if (EmployeeCount.HasValue && EmployeeCount.Value < 0)
            throw new ArgumentException("Employee count cannot be negative");

        if (AnnualRevenue.HasValue && AnnualRevenue.Value < 0)
            throw new ArgumentException("Annual revenue cannot be negative");

        if (!string.IsNullOrEmpty(RevenueCurrency) && RevenueCurrency.Length != 3)
            throw new ArgumentException("Revenue currency must be a 3-character ISO code");
    }

    /// <summary>
    /// Gets the company size category based on employee count
    /// </summary>
    public string GetCompanySize()
    {
        if (!EmployeeCount.HasValue)
            return "Unknown";

        return EmployeeCount.Value switch
        {
            <= 10 => "Micro",
            <= 50 => "Small",
            <= 250 => "Medium",
            <= 1000 => "Large",
            _ => "Enterprise"
        };
    }

    /// <summary>
    /// Checks if this is a large enterprise
    /// </summary>
    public bool IsEnterprise => EmployeeCount.HasValue && EmployeeCount.Value > 1000;
}
