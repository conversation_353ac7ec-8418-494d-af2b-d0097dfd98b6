using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.SharedKernel.Events;

/// <summary>
/// Interface for dispatching domain events.
/// Domain event dispatchers are responsible for publishing domain events to their handlers.
/// </summary>
public interface IDomainEventDispatcher
{
    /// <summary>
    /// Dispatches a single domain event
    /// </summary>
    /// <param name="domainEvent">The domain event to dispatch</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task DispatchAsync(DomainEvent domainEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Dispatches multiple domain events
    /// </summary>
    /// <param name="domainEvents">The domain events to dispatch</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task DispatchAsync(IEnumerable<DomainEvent> domainEvents, CancellationToken cancellationToken = default);

    /// <summary>
    /// Dispatches all domain events from entities that implement IHasDomainEvents
    /// </summary>
    /// <param name="entities">The entities with domain events</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task DispatchEventsAsync(IEnumerable<IHasDomainEvents> entities, CancellationToken cancellationToken = default);

    /// <summary>
    /// Dispatches all domain events from a single entity and clears them
    /// </summary>
    /// <param name="entity">The entity with domain events</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task DispatchAndClearEventsAsync(IHasDomainEvents entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Dispatches all domain events from entities and clears them
    /// </summary>
    /// <param name="entities">The entities with domain events</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task DispatchAndClearEventsAsync(IEnumerable<IHasDomainEvents> entities, CancellationToken cancellationToken = default);
}
