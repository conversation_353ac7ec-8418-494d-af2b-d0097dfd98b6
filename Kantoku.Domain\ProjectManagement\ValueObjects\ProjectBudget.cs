using Kantoku.SharedKernel;

namespace Kantoku.Domain.ProjectManagement.ValueObjects;

/// <summary>
/// Value object representing project budget information
/// </summary>
public class ProjectBudget : ValueObject
{
    public decimal InitialBudget { get; private set; }
    public decimal ActualBudget { get; private set; }
    public string Currency { get; private set; } = "JPY";

    private ProjectBudget() { } // For EF Core

    public ProjectBudget(
        decimal initialBudget,
        decimal actualBudget = 0,
        string currency = "JPY")
    {
        SetInitialBudget(initialBudget);
        SetActualBudget(actualBudget);
        SetCurrency(currency);
    }

    /// <summary>
    /// Creates a budget with only initial amount
    /// </summary>
    public static ProjectBudget CreateInitial(decimal initialBudget, string currency = "JPY")
    {
        return new ProjectBudget(initialBudget, 0, currency);
    }

    /// <summary>
    /// Updates the actual budget
    /// </summary>
    public ProjectBudget WithActualBudget(decimal actualBudget)
    {
        return new ProjectBudget(InitialBudget, actualBudget, Currency);
    }

    /// <summary>
    /// Gets the budget variance (positive if over budget, negative if under budget)
    /// </summary>
    public decimal Variance => ActualBudget - InitialBudget;

    /// <summary>
    /// Gets the budget variance as a percentage
    /// </summary>
    public decimal VariancePercentage
    {
        get
        {
            if (InitialBudget == 0)
                return 0;
            
            return (Variance / InitialBudget) * 100;
        }
    }

    /// <summary>
    /// Checks if the project is over budget
    /// </summary>
    public bool IsOverBudget => ActualBudget > InitialBudget;

    /// <summary>
    /// Checks if the project is under budget
    /// </summary>
    public bool IsUnderBudget => ActualBudget < InitialBudget;

    /// <summary>
    /// Checks if the project is on budget (within 5% tolerance)
    /// </summary>
    public bool IsOnBudget => Math.Abs(VariancePercentage) <= 5;

    /// <summary>
    /// Gets the remaining budget
    /// </summary>
    public decimal RemainingBudget => InitialBudget - ActualBudget;

    /// <summary>
    /// Gets the budget utilization percentage
    /// </summary>
    public decimal UtilizationPercentage
    {
        get
        {
            if (InitialBudget == 0)
                return 0;
            
            return (ActualBudget / InitialBudget) * 100;
        }
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return InitialBudget;
        yield return ActualBudget;
        yield return Currency;
    }

    private void SetInitialBudget(decimal initialBudget)
    {
        if (initialBudget < 0)
            throw new ArgumentException("Initial budget cannot be negative", nameof(initialBudget));

        InitialBudget = initialBudget;
    }

    private void SetActualBudget(decimal actualBudget)
    {
        if (actualBudget < 0)
            throw new ArgumentException("Actual budget cannot be negative", nameof(actualBudget));

        ActualBudget = actualBudget;
    }

    private void SetCurrency(string currency)
    {
        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("Currency cannot be null or empty", nameof(currency));

        if (currency.Length != 3)
            throw new ArgumentException("Currency must be a 3-character ISO code", nameof(currency));

        Currency = currency.ToUpperInvariant();
    }

    public override string ToString()
    {
        if (ActualBudget > 0)
        {
            return $"Initial: {InitialBudget:N0} {Currency}, Actual: {ActualBudget:N0} {Currency}, Variance: {Variance:N0} {Currency} ({VariancePercentage:F1}%)";
        }
        
        return $"Budget: {InitialBudget:N0} {Currency}";
    }
}
