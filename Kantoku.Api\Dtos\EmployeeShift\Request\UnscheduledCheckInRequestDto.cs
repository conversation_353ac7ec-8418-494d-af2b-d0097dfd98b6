using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.EmployeeShift.Request;

/// <summary>
/// Used for checkin and checkout, 
/// when user checkin or checkout without scheduled time, need to provide projectId and working location
/// otherwise, provide only gps info
/// </summary>
public class UnscheduledCheckInRequestDto
{
    /// <summary>
    /// (*) The ID of the project for this check-in/out
    /// </summary>
    [Required]
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// The location where the work is being performed
    /// </summary>
    public string? WorkingLocation { get; set; }

    /// <summary>
    /// The latitude coordinate of the GPS location
    /// </summary>
    public string? Latitude { get; set; }

    /// <summary>
    /// The longitude coordinate of the GPS location
    /// </summary>
    public string? Longitude { get; set; }
}