namespace Kantoku.SharedKernel.Time;

/// <summary>
/// Extension methods for DateTime and DateTimeOffset
/// </summary>
public static class DateTimeExtensions
{
    /// <summary>
    /// Gets the start of the day (midnight) for the specified date
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <returns>The start of the day</returns>
    public static DateTime StartOfDay(this DateTime dateTime)
    {
        return dateTime.Date;
    }

    /// <summary>
    /// Gets the end of the day (23:59:59.999) for the specified date
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <returns>The end of the day</returns>
    public static DateTime EndOfDay(this DateTime dateTime)
    {
        return dateTime.Date.AddDays(1).AddTicks(-1);
    }

    /// <summary>
    /// Gets the start of the week (Monday) for the specified date
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <param name="startOfWeek">The day that represents the start of the week (default: Monday)</param>
    /// <returns>The start of the week</returns>
    public static DateTime StartOfWeek(this DateTime dateTime, DayOfWeek startOfWeek = DayOfWeek.Monday)
    {
        var diff = (7 + (dateTime.DayOfWeek - startOfWeek)) % 7;
        return dateTime.AddDays(-1 * diff).Date;
    }

    /// <summary>
    /// Gets the end of the week for the specified date
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <param name="startOfWeek">The day that represents the start of the week (default: Monday)</param>
    /// <returns>The end of the week</returns>
    public static DateTime EndOfWeek(this DateTime dateTime, DayOfWeek startOfWeek = DayOfWeek.Monday)
    {
        return dateTime.StartOfWeek(startOfWeek).AddDays(6).EndOfDay();
    }

    /// <summary>
    /// Gets the start of the month for the specified date
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <returns>The start of the month</returns>
    public static DateTime StartOfMonth(this DateTime dateTime)
    {
        return new DateTime(dateTime.Year, dateTime.Month, 1, 0, 0, 0, dateTime.Kind);
    }

    /// <summary>
    /// Gets the end of the month for the specified date
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <returns>The end of the month</returns>
    public static DateTime EndOfMonth(this DateTime dateTime)
    {
        return dateTime.StartOfMonth().AddMonths(1).AddTicks(-1);
    }

    /// <summary>
    /// Gets the start of the year for the specified date
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <returns>The start of the year</returns>
    public static DateTime StartOfYear(this DateTime dateTime)
    {
        return new DateTime(dateTime.Year, 1, 1, 0, 0, 0, dateTime.Kind);
    }

    /// <summary>
    /// Gets the end of the year for the specified date
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <returns>The end of the year</returns>
    public static DateTime EndOfYear(this DateTime dateTime)
    {
        return dateTime.StartOfYear().AddYears(1).AddTicks(-1);
    }

    /// <summary>
    /// Determines whether the date is a weekend (Saturday or Sunday)
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <returns>True if the date is a weekend; otherwise, false</returns>
    public static bool IsWeekend(this DateTime dateTime)
    {
        return dateTime.DayOfWeek == DayOfWeek.Saturday || dateTime.DayOfWeek == DayOfWeek.Sunday;
    }

    /// <summary>
    /// Determines whether the date is a weekday (Monday through Friday)
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <returns>True if the date is a weekday; otherwise, false</returns>
    public static bool IsWeekday(this DateTime dateTime)
    {
        return !dateTime.IsWeekend();
    }

    /// <summary>
    /// Gets the age in years from the specified birth date to the current date
    /// </summary>
    /// <param name="birthDate">The birth date</param>
    /// <param name="asOfDate">The date to calculate age as of (default: today)</param>
    /// <returns>The age in years</returns>
    public static int GetAge(this DateTime birthDate, DateTime? asOfDate = null)
    {
        var referenceDate = asOfDate ?? DateTime.Today;
        var age = referenceDate.Year - birthDate.Year;
        
        if (referenceDate.Month < birthDate.Month || 
            (referenceDate.Month == birthDate.Month && referenceDate.Day < birthDate.Day))
        {
            age--;
        }
        
        return age;
    }

    /// <summary>
    /// Rounds the DateTime to the nearest specified time span
    /// </summary>
    /// <param name="dateTime">The date time to round</param>
    /// <param name="timeSpan">The time span to round to</param>
    /// <returns>The rounded date time</returns>
    public static DateTime Round(this DateTime dateTime, TimeSpan timeSpan)
    {
        var ticks = (dateTime.Ticks + timeSpan.Ticks / 2 + 1) / timeSpan.Ticks;
        return new DateTime(ticks * timeSpan.Ticks, dateTime.Kind);
    }

    /// <summary>
    /// Rounds the DateTime down to the specified time span
    /// </summary>
    /// <param name="dateTime">The date time to round</param>
    /// <param name="timeSpan">The time span to round to</param>
    /// <returns>The rounded down date time</returns>
    public static DateTime Floor(this DateTime dateTime, TimeSpan timeSpan)
    {
        var ticks = dateTime.Ticks / timeSpan.Ticks;
        return new DateTime(ticks * timeSpan.Ticks, dateTime.Kind);
    }

    /// <summary>
    /// Rounds the DateTime up to the specified time span
    /// </summary>
    /// <param name="dateTime">The date time to round</param>
    /// <param name="timeSpan">The time span to round to</param>
    /// <returns>The rounded up date time</returns>
    public static DateTime Ceiling(this DateTime dateTime, TimeSpan timeSpan)
    {
        var ticks = (dateTime.Ticks + timeSpan.Ticks - 1) / timeSpan.Ticks;
        return new DateTime(ticks * timeSpan.Ticks, dateTime.Kind);
    }

    /// <summary>
    /// Converts a DateTime to Unix timestamp (seconds since Unix epoch)
    /// </summary>
    /// <param name="dateTime">The date time to convert</param>
    /// <returns>The Unix timestamp</returns>
    public static long ToUnixTimestamp(this DateTime dateTime)
    {
        var epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        return (long)(dateTime.ToUniversalTime() - epoch).TotalSeconds;
    }

    /// <summary>
    /// Converts a Unix timestamp to DateTime
    /// </summary>
    /// <param name="unixTimestamp">The Unix timestamp</param>
    /// <returns>The DateTime</returns>
    public static DateTime FromUnixTimestamp(long unixTimestamp)
    {
        var epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        return epoch.AddSeconds(unixTimestamp);
    }

    /// <summary>
    /// Gets a human-readable relative time string (e.g., "2 hours ago", "in 3 days")
    /// </summary>
    /// <param name="dateTime">The date time</param>
    /// <param name="relativeTo">The date time to compare to (default: now)</param>
    /// <returns>A human-readable relative time string</returns>
    public static string ToRelativeTimeString(this DateTime dateTime, DateTime? relativeTo = null)
    {
        var reference = relativeTo ?? DateTime.Now;
        var timeSpan = reference - dateTime;
        var totalSeconds = Math.Abs(timeSpan.TotalSeconds);
        var isPast = timeSpan.TotalSeconds > 0;

        return totalSeconds switch
        {
            < 60 => isPast ? "just now" : "in a moment",
            < 3600 => FormatRelativeTime((int)(totalSeconds / 60), "minute", isPast),
            < 86400 => FormatRelativeTime((int)(totalSeconds / 3600), "hour", isPast),
            < 2592000 => FormatRelativeTime((int)(totalSeconds / 86400), "day", isPast),
            < 31536000 => FormatRelativeTime((int)(totalSeconds / 2592000), "month", isPast),
            _ => FormatRelativeTime((int)(totalSeconds / 31536000), "year", isPast)
        };
    }

    private static string FormatRelativeTime(int value, string unit, bool isPast)
    {
        var plural = value != 1 ? "s" : "";
        return isPast ? $"{value} {unit}{plural} ago" : $"in {value} {unit}{plural}";
    }
}
