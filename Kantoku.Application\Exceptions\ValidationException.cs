using System;
using System.Collections.Generic;
using System.Linq;

namespace Kantoku.Application.Exceptions
{
    /// <summary>
    /// Exception that is thrown when validation fails
    /// </summary>
    public class ValidationException : Exception
    {
        public ValidationException() 
            : base("One or more validation failures have occurred.")
        {
            Errors = new Dictionary<string, string[]>();
        }

        public ValidationException(IEnumerable<FluentValidation.Results.ValidationFailure> failures)
            : this()
        {
            Errors = failures
                .GroupBy(e => e.PropertyName, e => e.ErrorMessage)
                .ToDictionary(failureGroup => failureGroup.Key, failureGroup => failureGroup.ToArray());
        }

        public IDictionary<string, string[]> Errors { get; }
    }
} 