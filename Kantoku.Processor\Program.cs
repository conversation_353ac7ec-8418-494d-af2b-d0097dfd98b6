using Kantoku.Processor.Configurations;
using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Data.Seeders;
using Kantoku.Processor.Extensions;

var builder = WebApplication.CreateBuilder(args);

var env = builder.Environment;
builder.Configuration.AddEnvironmentVariables();
builder.Configuration.AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: false, reloadOnChange: true);

// Configure options
builder.Services.Configure<JobScheduleConfig>(builder.Configuration.GetSection("BatchProcessing"));
builder.Services.Configure<JobDbConfig>(builder.Configuration.GetSection("JobDbConfig"));
builder.Services.Configure<AppDbConfig>(builder.Configuration.GetSection("AppDbConfig"));

// Configure job database context
builder.Services.AddDbContext<JobDbContext>(options =>
{
    if (env.IsDevelopment())
    {
        options.EnableDetailedErrors();
        options.EnableSensitiveDataLogging();
    }
});

// Configure application database context
builder.Services.AddDbContext<AppDbContext>(options =>
{
    if (env.IsDevelopment())
    {
        options.EnableDetailedErrors();
        options.EnableSensitiveDataLogging();
    }
});

// Configure services
builder.Services.ServiceCollect();



var app = builder.Build();

// Seed database
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var seeder = services.GetRequiredService<DatabaseSeeder>();
        await seeder.SeedAsync();
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while seeding the database");
    }
}

// Run as a service without handling HTTP requests
await app.RunAsync();
