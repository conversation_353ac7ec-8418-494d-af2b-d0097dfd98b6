﻿namespace Kantoku.Domain.Models;

public class Function
{
    public Guid FunctionUid { get; set; }

    public string FunctionUrl { get; set; } = null!;

    public string FunctionName { get; set; } = null!;

    public Guid? ParentUid { get; set; }

    public string? Icon { get; set; }

    public string? Locale { get; set; }

    public string? Redirect { get; set; }

    public string? Component { get; set; }

    public bool? HideInMenu { get; set; }

    public string? Title { get; set; }

    public bool? IsHeader { get; set; }

    public bool? HideInBreadcrumb { get; set; }

    public bool? HideChildrenInMenu { get; set; }

    public int? DisplayOrder { get; set; }

    public bool? OnlySuperUser { get; set; }

    public virtual Function? Parent { get; set; }

    public virtual ICollection<Function> Children { get; set; } = [];

    public virtual ICollection<RoleFunction> RoleFunctions { get; set; } = [];
}
